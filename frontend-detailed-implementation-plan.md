# Postal Terminal API - Detailed Frontend Implementation Plan

## Executive Summary

This document provides a comprehensive implementation plan for the Postal Terminal API frontend, based on systematic analysis of the existing backend infrastructure. The frontend will be built using Next.js 15.3.5, DaisyUI 5, and TailwindCSS 4, creating a modern, minimalistic, and highly professional SaaS application.

## Backend Analysis Summary

### Current Backend Architecture
- **Framework**: Fastify with TypeScript
- **Database**: PostgreSQL with PostGIS extension
- **Authentication**: Dual system (JWT for web dashboard + API Keys for programmatic access)
- **Payment Processing**: Stripe integration
- **Email Service**: Comprehensive email system for verification and notifications
- **Rate Limiting**: Multi-tier rate limiting system
- **Caching**: PostgreSQL-based caching system

### Existing API Endpoints

#### Authentication Endpoints
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Token refresh
- `POST /api/v1/auth/forgot-password` - Password reset request
- `POST /api/v1/auth/reset-password` - Password reset
- `POST /api/v1/auth/verify-email` - Email verification
- `POST /api/v1/auth/resend-verification` - Resend verification
- `GET /api/v1/auth/google/callback` - Google OAuth callback

#### User Management
- `GET /api/v1/users/me` - Get current user profile
- `PATCH /api/v1/users/me` - Update user profile
- `DELETE /api/v1/users/me` - Delete user account

#### Subscription Management
- `GET /api/v1/subscriptions/plans` - List subscription plans
- `GET /api/v1/subscriptions/my-subscription` - Get user subscription
- `POST /api/v1/subscriptions/checkout` - Create checkout session
- `POST /api/v1/subscriptions/change` - Change subscription
- `POST /api/v1/subscriptions/cancel` - Cancel subscription

#### API Key Management
- `GET /api/v1/users/my-api-keys` - List user API keys
- `POST /api/v1/users/my-api-keys` - Create API key
- `PATCH /api/v1/users/my-api-keys/:id` - Update API key
- `DELETE /api/v1/users/my-api-keys/:id` - Delete API key

#### Core API Endpoints (API Key Protected)
- `GET /api/v1/terminals` - List postal terminals
- `GET /api/v1/terminals/:id` - Get terminal details
- `GET /api/v1/terminals/search` - Search terminals
- `GET /api/v1/terminals/nearby` - Find nearby terminals
- `GET /api/v1/track` - Track packages

### Database Schema

#### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT,
  google_id TEXT UNIQUE,
  google_email TEXT,
  first_name TEXT,
  last_name TEXT,
  role TEXT NOT NULL CHECK (role IN ('ADMIN', 'CUSTOMER')),
  is_active BOOLEAN DEFAULT TRUE,
  email_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_login_at TIMESTAMPTZ
);
```

#### Subscription Plans
```sql
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  description TEXT,
  price_eur DECIMAL(10,2) NOT NULL,
  billing_interval TEXT DEFAULT 'monthly',
  api_requests_per_month INTEGER NOT NULL,
  api_requests_per_minute INTEGER NOT NULL,
  max_api_keys INTEGER DEFAULT 1,
  features JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  is_public BOOLEAN DEFAULT TRUE,
  sort_order INTEGER DEFAULT 0
);
```

## Frontend Implementation Plan

### Technology Stack

- **Framework**: Next.js 15.3.5 with App Router
- **UI Framework**: DaisyUI 5 with TailwindCSS 4
- **State Management**: 
  - Zustand for client state
  - TanStack Query for server state
- **Forms**: React Hook Form with Zod validation
- **Authentication**: JWT with refresh token strategy
- **Maps**: React-Leaflet for terminal visualization
- **Charts**: Recharts for analytics
- **Internationalization**: next-intl (English/Lithuanian)
- **Icons**: Lucide React
- **Date Handling**: date-fns

### Project Structure

```
frontend/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Auth route group
│   │   ├── login/
│   │   │   └── page.tsx
│   │   ├── register/
│   │   │   └── page.tsx
│   │   ├── forgot-password/
│   │   │   └── page.tsx
│   │   ├── reset-password/
│   │   │   └── page.tsx
│   │   ├── verify-email/
│   │   │   └── page.tsx
│   │   └── layout.tsx            # Auth layout
│   ├── (dashboard)/              # Dashboard route group
│   │   ├── dashboard/
│   │   │   └── page.tsx
│   │   ├── profile/
│   │   │   └── page.tsx
│   │   ├── subscription/
│   │   │   ├── page.tsx
│   │   │   ├── plans/
│   │   │   └── billing/
│   │   ├── api-keys/
│   │   │   └── page.tsx
│   │   ├── analytics/
│   │   │   └── page.tsx
│   │   └── layout.tsx            # Dashboard layout
│   ├── (admin)/                  # Admin route group
│   │   ├── admin/
│   │   │   ├── users/
│   │   │   ├── subscriptions/
│   │   │   ├── analytics/
│   │   │   └── settings/
│   │   └── layout.tsx            # Admin layout
│   ├── api/                      # API routes (if needed)
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   ├── loading.tsx               # Global loading UI
│   ├── not-found.tsx             # 404 page
│   ├── error.tsx                 # Error boundary
│   └── page.tsx                  # Landing page
├── components/
│   ├── ui/                       # Base UI components
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Card.tsx
│   │   ├── Modal.tsx
│   │   ├── Badge.tsx
│   │   ├── Alert.tsx
│   │   ├── Loading.tsx
│   │   ├── Pagination.tsx
│   │   └── Tabs.tsx
│   ├── layout/                   # Layout components
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   ├── Footer.tsx
│   │   ├── Navigation.tsx
│   │   └── Breadcrumb.tsx
│   ├── forms/                    # Form components
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   ├── ProfileForm.tsx
│   │   ├── ApiKeyForm.tsx
│   │   └── SubscriptionForm.tsx
│   ├── features/                 # Feature-specific components
│   │   ├── auth/
│   │   ├── dashboard/
│   │   ├── subscription/
│   │   ├── api-keys/
│   │   ├── analytics/
│   │   └── terminals/
│   └── common/                   # Common components
│       ├── LanguageSelector.tsx
│       ├── ThemeToggle.tsx
│       ├── UserMenu.tsx
│       └── Logo.tsx
├── lib/
│   ├── api/                      # API client
│   │   ├── client.ts
│   │   ├── auth.ts
│   │   ├── users.ts
│   │   ├── subscriptions.ts
│   │   ├── api-keys.ts
│   │   └── terminals.ts
│   ├── auth/                     # Auth utilities
│   │   ├── jwt.ts
│   │   ├── storage.ts
│   │   └── guards.ts
│   ├── hooks/                    # Custom hooks
│   │   ├── useAuth.ts
│   │   ├── useSubscription.ts
│   │   ├── useApiKeys.ts
│   │   └── useLocalStorage.ts
│   ├── stores/                   # Zustand stores
│   │   ├── authStore.ts
│   │   ├── uiStore.ts
│   │   └── subscriptionStore.ts
│   ├── utils/
│   │   ├── cn.ts                 # Class name utility
│   │   ├── format.ts             # Formatting utilities
│   │   ├── validation.ts         # Zod schemas
│   │   └── constants.ts
│   └── config.ts                 # App configuration
├── messages/                     # i18n messages
│   ├── en.json
│   └── lt.json
├── public/
│   ├── icons/
│   ├── images/
│   └── favicon.ico
├── types/
│   ├── auth.ts
│   ├── subscription.ts
│   ├── api.ts
│   └── common.ts
├── middleware.ts                 # Next.js middleware
├── next.config.js
├── tailwind.config.js
├── package.json
└── tsconfig.json
```

## Phase 1: Foundation & Landing Page

### 1.1 Project Setup

#### Dependencies
```json
{
  "dependencies": {
    "next": "^15.3.5",
    "react": "^18.3.0",
    "react-dom": "^18.3.0",
    "typescript": "^5.6.0",
    "tailwindcss": "^4.0.0",
    "daisyui": "^5.0.0",
    "@tanstack/react-query": "^5.59.0",
    "zustand": "^5.0.0",
    "react-hook-form": "^7.53.0",
    "@hookform/resolvers": "^3.9.0",
    "zod": "^3.23.0",
    "next-intl": "^3.22.0",
    "date-fns": "^4.1.0",
    "lucide-react": "^0.453.0",
    "recharts": "^2.12.0",
    "react-leaflet": "^4.2.0",
    "leaflet": "^1.9.0"
  },
  "devDependencies": {
    "@types/node": "^22.0.0",
    "@types/react": "^18.3.0",
    "@types/react-dom": "^18.3.0",
    "@types/leaflet": "^1.9.0",
    "eslint": "^9.0.0",
    "eslint-config-next": "^15.3.5",
    "prettier": "^3.3.0",
    "@testing-library/react": "^16.0.0",
    "@testing-library/jest-dom": "^6.6.0",
    "jest": "^29.7.0",
    "jest-environment-jsdom": "^29.7.0"
  }
}
```

#### Environment Variables
```env
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1
NEXT_PUBLIC_APP_NAME=Postal Terminal API
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_MAPS=true
NEXT_PUBLIC_ENABLE_OAUTH=true
NEXT_PUBLIC_DEFAULT_LOCALE=en
NEXT_PUBLIC_SUPPORTED_LOCALES=en,lt
```

### 1.2 Landing Page Design

#### Key Features
- **Hero Section**: Clean, professional introduction to the API
- **Features Section**: Highlight key API capabilities
- **Pricing Section**: Clear subscription plan comparison
- **Documentation Preview**: Code examples and API showcase
- **CTA Sections**: Strategic placement of sign-up buttons
- **Footer**: Links, legal pages, contact information

#### Design Principles
- **Minimalistic**: Clean, uncluttered design
- **Professional**: Business-focused aesthetic
- **Mobile-First**: Responsive design for all devices
- **Performance**: Optimized loading and Core Web Vitals
- **Accessibility**: WCAG 2.1 AA compliance

#### Landing Page Sections

1. **Navigation Bar**
   - Logo and brand name
   - Navigation links (Features, Pricing, Docs, Login)
   - Language selector (EN/LT)
   - CTA button (Get Started)

2. **Hero Section**
   - Compelling headline
   - Value proposition
   - Primary CTA (Start Free Trial)
   - Secondary CTA (View Documentation)
   - Hero image/illustration

3. **Features Section**
   - Terminal Search & Discovery
   - Package Tracking
   - Real-time Data
   - Multiple Providers
   - Geographic Search
   - RESTful API

4. **API Preview Section**
   - Interactive code examples
   - Live API response preview
   - Multiple programming languages

5. **Pricing Section**
   - Plan comparison table
   - Feature highlights
   - Popular plan badge
   - Monthly/Yearly toggle

6. **Trust Section**
   - API reliability stats
   - Data freshness indicators
   - Security badges
   - Uptime guarantees

7. **CTA Section**
   - Final conversion opportunity
   - Free trial emphasis
   - No credit card required

8. **Footer**
   - Company information
   - Legal links
   - Social media
   - Contact information

### 1.3 Core Configuration Files

#### Next.js Configuration
```javascript
/** @type {import('next').NextConfig} */
const withNextIntl = require('next-intl/plugin')();

const nextConfig = {
  experimental: {
    typedRoutes: true,
  },
  images: {
    domains: ['localhost'],
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
      },
    ];
  },
};

module.exports = withNextIntl(nextConfig);
```

#### TailwindCSS Configuration
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [require('daisyui')],
  daisyui: {
    themes: [
      {
        light: {
          primary: '#3b82f6',
          secondary: '#64748b',
          accent: '#f59e0b',
          neutral: '#374151',
          'base-100': '#ffffff',
          'base-200': '#f8fafc',
          'base-300': '#e2e8f0',
          info: '#0ea5e9',
          success: '#10b981',
          warning: '#f59e0b',
          error: '#ef4444',
        },
      },
      {
        dark: {
          primary: '#60a5fa',
          secondary: '#94a3b8',
          accent: '#fbbf24',
          neutral: '#1f2937',
          'base-100': '#111827',
          'base-200': '#1f2937',
          'base-300': '#374151',
          info: '#38bdf8',
          success: '#34d399',
          warning: '#fbbf24',
          error: '#f87171',
        },
      },
    ],
    darkTheme: 'dark',
    base: true,
    styled: true,
    utils: true,
    prefix: '',
    logs: true,
    themeRoot: ':root',
  },
};
```

## Phase 2: Authentication System

### 2.1 Authentication Architecture

#### JWT Token Strategy
- **Access Token**: Short-lived (15 minutes)
- **Refresh Token**: Long-lived (7 days)
- **Automatic Refresh**: Silent token refresh
- **Secure Storage**: HttpOnly cookies for refresh tokens

#### Authentication Flow
1. User submits login credentials
2. Backend validates and returns JWT tokens
3. Frontend stores tokens securely
4. API requests include access token
5. Automatic refresh when token expires
6. Logout clears all tokens

### 2.2 Authentication Pages

#### Login Page (`/login`)
- **Email/Password Form**
  - Email validation
  - Password requirements
  - Remember me option
  - Form validation with Zod
- **Google OAuth Button**
  - One-click Google sign-in
  - Proper error handling
- **Additional Links**
  - Forgot password
  - Create account
  - Back to home

#### Register Page (`/register`)
- **Registration Form**
  - Email (required)
  - Password (with strength indicator)
  - Confirm password
  - First name (optional)
  - Last name (optional)
  - Terms acceptance
- **Google OAuth Option**
- **Form Validation**
  - Real-time validation
  - Password strength meter
  - Email format validation

#### Forgot Password Page (`/forgot-password`)
- **Email Input Form**
- **Clear Instructions**
- **Success/Error States**
- **Back to Login Link**

#### Reset Password Page (`/reset-password`)
- **Token Validation**
- **New Password Form**
- **Password Confirmation**
- **Success Redirect**

#### Email Verification Page (`/verify-email`)
- **Token Processing**
- **Success/Error States**
- **Resend Verification Option**
- **Login Redirect**

### 2.3 Authentication Components

#### Auth Layout Component
```typescript
interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  showBackToHome?: boolean;
}

export function AuthLayout({ 
  children, 
  title, 
  subtitle, 
  showBackToHome = true 
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Logo className="mx-auto h-12 w-auto" />
          <h2 className="mt-6 text-3xl font-bold text-base-content">
            {title}
          </h2>
          {subtitle && (
            <p className="mt-2 text-sm text-base-content/70">
              {subtitle}
            </p>
          )}
        </div>
        <div className="bg-base-100 py-8 px-6 shadow-lg rounded-lg">
          {children}
        </div>
        {showBackToHome && (
          <div className="text-center">
            <Link href="/" className="text-primary hover:text-primary-focus">
              ← Back to Home
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
```

#### Login Form Component
```typescript
interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().default(false),
});

export function LoginForm() {
  const router = useRouter();
  const { login } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      await login(data.email, data.password, data.rememberMe);
      router.push('/dashboard');
    } catch (error) {
      setError('root', {
        message: error.message || 'Login failed. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {errors.root && (
        <Alert type="error">{errors.root.message}</Alert>
      )}
      
      <div>
        <Input
          {...register('email')}
          type="email"
          label="Email Address"
          placeholder="Enter your email"
          error={errors.email?.message}
          required
        />
      </div>

      <div>
        <Input
          {...register('password')}
          type="password"
          label="Password"
          placeholder="Enter your password"
          error={errors.password?.message}
          required
        />
      </div>

      <div className="flex items-center justify-between">
        <label className="flex items-center">
          <input
            {...register('rememberMe')}
            type="checkbox"
            className="checkbox checkbox-primary checkbox-sm"
          />
          <span className="ml-2 text-sm text-base-content/70">
            Remember me
          </span>
        </label>
        
        <Link
          href="/forgot-password"
          className="text-sm text-primary hover:text-primary-focus"
        >
          Forgot password?
        </Link>
      </div>

      <Button
        type="submit"
        className="w-full"
        loading={isLoading}
        disabled={isLoading}
      >
        Sign In
      </Button>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-base-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-base-100 text-base-content/70">
            Or continue with
          </span>
        </div>
      </div>

      <GoogleOAuthButton />

      <div className="text-center">
        <span className="text-sm text-base-content/70">
          Don't have an account?{' '}
          <Link
            href="/register"
            className="text-primary hover:text-primary-focus font-medium"
          >
            Sign up
          </Link>
        </span>
      </div>
    </form>
  );
}
```

### 2.4 Authentication Hooks and Utilities

#### useAuth Hook
```typescript
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthActions {
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (data: UpdateProfileData) => Promise<void>;
}

export function useAuth(): AuthState & AuthActions {
  const { user, isAuthenticated, isLoading } = useAuthStore();
  const queryClient = useQueryClient();

  const login = async (email: string, password: string, rememberMe = false) => {
    const response = await authApi.login({ email, password });
    
    // Store tokens
    tokenStorage.setAccessToken(response.accessToken);
    tokenStorage.setRefreshToken(response.refreshToken);
    
    // Update auth state
    useAuthStore.getState().setUser(response.user);
    
    // Set up automatic token refresh
    setupTokenRefresh(response.expiresIn);
  };

  const logout = async () => {
    try {
      const refreshToken = tokenStorage.getRefreshToken();
      if (refreshToken) {
        await authApi.logout({ refreshToken });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear tokens and state
      tokenStorage.clearTokens();
      useAuthStore.getState().clearUser();
      queryClient.clear();
    }
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshToken,
    updateProfile,
  };
}
```

## Phase 3: Dashboard Foundation

### 3.1 Dashboard Layout

#### Layout Structure
- **Header**: Navigation, user menu, notifications
- **Sidebar**: Main navigation menu
- **Main Content**: Page-specific content
- **Footer**: Minimal footer with links

#### Responsive Design
- **Desktop**: Full sidebar + header
- **Tablet**: Collapsible sidebar
- **Mobile**: Bottom navigation + hamburger menu

### 3.2 Dashboard Navigation

#### Main Navigation Items
- **Dashboard**: Overview and quick stats
- **API Keys**: Manage API keys
- **Subscription**: Plan management and billing
- **Analytics**: Usage statistics and charts
- **Profile**: Account settings
- **Documentation**: API docs (external link)

#### Admin Navigation (Admin Role)
- **Admin Dashboard**: System overview
- **Users**: User management
- **Subscriptions**: Subscription management
- **Analytics**: System analytics
- **Settings**: System settings

### 3.3 Dashboard Components

#### Dashboard Header
```typescript
export function DashboardHeader() {
  const { user } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-base-100 border-b border-base-300">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Mobile menu button */}
          <button
            className="lg:hidden btn btn-ghost btn-sm"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-5 w-5" />
          </button>

          {/* Logo */}
          <div className="flex items-center">
            <Logo className="h-8 w-auto" />
            <span className="ml-2 text-xl font-semibold text-base-content">
              Dashboard
            </span>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            <NotificationBell />
            <LanguageSelector />
            <ThemeToggle />
            <UserMenu user={user} />
          </div>
        </div>
      </div>
    </header>
  );
}
```

#### Dashboard Sidebar
```typescript
interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  adminOnly?: boolean;
}

const navigationItems: NavigationItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'API Keys', href: '/api-keys', icon: Key },
  { name: 'Subscription', href: '/subscription', icon: CreditCard },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: 'Profile', href: '/profile', icon: User },
  { name: 'Admin', href: '/admin', icon: Shield, adminOnly: true },
];

export function DashboardSidebar() {
  const pathname = usePathname();
  const { user } = useAuth();
  const isAdmin = user?.role === 'ADMIN';

  return (
    <aside className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:pt-16">
      <div className="flex flex-col flex-grow bg-base-100 border-r border-base-300 pt-5 pb-4 overflow-y-auto">
        <nav className="flex-1 px-2 space-y-1">
          {navigationItems.map((item) => {
            if (item.adminOnly && !isAdmin) return null;
            
            const isActive = pathname === item.href;
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-primary text-primary-content'
                    : 'text-base-content hover:bg-base-200'
                )}
              >
                <item.icon
                  className={cn(
                    'mr-3 h-5 w-5 flex-shrink-0',
                    isActive ? 'text-primary-content' : 'text-base-content/70'
                  )}
                />
                {item.name}
                {item.badge && (
                  <span className="ml-auto badge badge-primary badge-sm">
                    {item.badge}
                  </span>
                )}
              </Link>
            );
          })}
        </nav>
      </div>
    </aside>
  );
}
```

## Implementation Timeline

### Week 1: Foundation Setup
- [ ] Project initialization with Next.js 15.3.5
- [ ] Configure TailwindCSS 4 and DaisyUI 5
- [ ] Set up TypeScript configuration
- [ ] Implement basic project structure
- [ ] Create landing page layout
- [ ] Implement responsive navigation

### Week 2: Landing Page
- [ ] Complete landing page design
- [ ] Implement hero section
- [ ] Create features showcase
- [ ] Build pricing section
- [ ] Add API preview section
- [ ] Implement call-to-action sections
- [ ] Mobile optimization

### Week 3: Authentication System
- [ ] Set up authentication architecture
- [ ] Implement JWT token management
- [ ] Create login page
- [ ] Create registration page
- [ ] Implement password reset flow
- [ ] Add email verification
- [ ] Google OAuth integration

### Week 4: Dashboard Foundation
- [ ] Create dashboard layout
- [ ] Implement navigation system
- [ ] Build user profile management
- [ ] Create basic dashboard overview
- [ ] Implement responsive design
- [ ] Add loading states and error handling

### Week 5-6: Core Features
- [ ] API key management interface
- [ ] Subscription management pages
- [ ] Usage analytics dashboard
- [ ] Billing history and invoices
- [ ] Account settings

### Week 7-8: Advanced Features
- [ ] Admin dashboard (if admin role)
- [ ] Terminal search interface
- [ ] Map integration for terminals
- [ ] Advanced analytics charts
- [ ] Notification system

### Week 9-10: Polish & Testing
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] Accessibility improvements
- [ ] SEO optimization
- [ ] Documentation
- [ ] Deployment preparation

## Quality Assurance

### Testing Strategy
- **Unit Tests**: Jest + React Testing Library
- **Integration Tests**: API integration testing
- **E2E Tests**: Playwright for critical user flows
- **Visual Tests**: Storybook for component testing
- **Performance Tests**: Lighthouse CI

### Code Quality
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting
- **TypeScript**: Type safety
- **Husky**: Pre-commit hooks
- **Conventional Commits**: Commit message standards

### Performance Targets
- **Core Web Vitals**: All metrics in "Good" range
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **Time to Interactive**: < 3.5s

## Deployment Strategy

### Environment Setup
- **Development**: Local development with hot reload
- **Staging**: Preview deployments for testing
- **Production**: Optimized production build

### CI/CD Pipeline
- **Build**: Automated builds on push
- **Test**: Run all test suites
- **Deploy**: Automatic deployment to staging/production
- **Monitor**: Performance and error monitoring

## Conclusion

This comprehensive implementation plan provides a roadmap for building a modern, professional frontend for the Postal Terminal API. The plan emphasizes:

- **Modern Technology Stack**: Next.js 15.3.5, DaisyUI 5, TailwindCSS 4
- **Professional Design**: Clean, minimalistic, business-focused
- **Comprehensive Features**: Complete SaaS functionality
- **Quality Focus**: Testing, performance, accessibility
- **Scalable Architecture**: Maintainable and extensible codebase

The implementation will result in a high-quality SaaS application that effectively showcases the Postal Terminal API while providing an excellent user experience for both customers and administrators.