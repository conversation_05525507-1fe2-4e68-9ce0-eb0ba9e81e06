{"common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close"}, "navigation": {"home": "Home", "features": "Features", "pricing": "Pricing", "docs": "Documentation", "contact": "Contact", "login": "Sign In", "register": "Get Started", "dashboard": "Dashboard", "logout": "Sign Out"}, "docs": {"title": "API Documentation", "subtitle": "Interactive documentation for the Postal Terminal API", "apiKey": {"title": "API Key", "placeholder": "Enter your API key to test endpoints", "required": "API key is required to test this endpoint", "invalid": "Invalid API key format", "hideKey": "Hide API key", "showKey": "Show API key", "format": "Format: ptapi_[64-character-hex-string]"}, "endpoints": {"terminals": "Terminal Endpoints", "tracking": "Package Tracking"}, "tryItOut": "Try it out", "execute": "Execute", "cancel": "Cancel", "copy": "Copy", "copied": "Copied!", "request": "Request", "response": "Response", "parameters": "Parameters", "requestBody": "Request Body", "responses": "Responses", "example": "Example", "required": "Required", "optional": "Optional", "description": "Description", "type": "Type", "authentication": "Authentication", "rateLimit": "Rate Limiting", "caching": "Caching", "errors": "Error Responses", "loading": "Loading...", "error": "Error occurred while making request", "noResponse": "No response received", "curlCommand": "cURL Command", "requestUrl": "Request URL", "statusCode": "Status Code", "responseTime": "Response Time", "responseHeaders": "Response Headers", "responseBody": "Response Body", "url": "URL", "curl": "cURL", "path": "Path", "query": "Query", "header": "Header", "gettingStarted": {"title": "Getting Started", "quickStartGuide": "Quick Start Guide", "startBuilding": "Start Building", "viewExamples": "View Examples", "totalEndpoints": "Total Endpoints", "restfulEndpoints": "RESTful API endpoints", "rateLimit": "Rate Limit", "requestsPerMinute": "requests per minute", "uptime": "Uptime", "serviceAvailability": "service availability", "getApiKey": "Get API Key", "makeRequest": "Make Request", "handleResponse": "Handle Response"}, "authSection": {"apiKeyRequired": "API Key Required", "apiKeyDescription": "All endpoints require an API key in the X-API-Key header. Format: ptapi_[64-character-hex-string]", "rateLimits": "Rate Limits", "rateLimitsDescription": "1,000 requests per minute per API key. Rate limit headers included in responses.", "caching": "Caching", "cachingDescription": "Terminal data cached for 5 minutes, search results for 2 minutes for optimal performance."}, "terminals": {"overview": "Terminal API Overview", "overviewDescription": "Access comprehensive postal terminal data including locations, services, and operating hours.", "availableEndpoints": "Available Endpoints", "terminalOperations": "Terminal operations", "responseFormat": "Response Format", "structuredData": "Structured data", "cacheDuration": "<PERSON><PERSON>", "optimizedPerformance": "Optimized performance"}, "tracking": {"features": "Package Tracking Features", "featuresDescription": "Real-time tracking information for packages and shipments across postal networks.", "availableEndpoints": "Available Endpoints", "trackingOperations": "Tracking operations", "updateFrequency": "Update Frequency", "realTime": "Real-time", "liveTrackingData": "Live tracking data", "coverage": "Coverage", "global": "Global", "worldwideTracking": "Worldwide tracking"}, "errorHandling": {"title": "Erro<PERSON>", "description": "All endpoints return errors in a consistent format:", "commonStatusCodes": "Common HTTP Status Codes:", "success": "Success", "badRequest": "Bad Request", "unauthorized": "Unauthorized", "notFound": "Not Found", "tooManyRequests": "Too Many Requests", "internalServerError": "Internal Server Error"}, "codeExamples": {"title": "Code Examples", "javascript": "JavaScript/Node.js", "python": "Python"}, "sidebar": {"apiDocumentation": "API Documentation", "postalTerminalApi": "Postal Terminal API v1.0", "endpoints": "Endpoints", "rateLimit": "Rate Limit"}, "navigation": {"gettingStarted": "Getting Started", "authentication": "Authentication", "terminalEndpoints": "Terminal Endpoints", "trackingEndpoints": "Tracking Endpoints", "errorHandling": "Erro<PERSON>", "codeExamples": "Code Examples"}}, "landing": {"hero": {"badge": "Unified Parcel Locker API", "title": "Find Postal Terminals Across Lithuania", "subtitle": "Access 1,923+ postal terminals from LP Express, Omniva, DPD, and Venipak with our comprehensive API.", "searchTitle": "", "searchPlaceholder": "Search by city, address, or terminal name...", "searchButton": "Search", "realTimeData": "Real-time Data", "packageTracking": "Package Tracking", "fastApi": "Fast API", "cta": "Get Started", "viewDocs": "View API Docs"}, "features": {"title": "Why Choose Our API?", "subtitle": "Built for developers, designed for scale", "realtime": {"title": "Real-time Data", "description": "Get up-to-date postal terminal information with our constantly updated database"}, "reliable": {"title": "99.9% Uptime", "description": "Enterprise-grade infrastructure ensures your applications stay online"}, "fast": {"title": "Lightning Fast", "description": "Optimized endpoints deliver responses in under 100ms globally"}, "secure": {"title": "Secure & Compliant", "description": "SOC 2 compliant with enterprise-grade security and data protection"}}, "pricing": {"title": "Simple, Transparent Pricing", "subtitle": "Choose the perfect plan for your needs. All plans include access to our comprehensive postal terminal database.", "monthly": "Monthly", "yearly": "Yearly", "save": "Save 20%", "starter": {"name": "Starter", "period": "/month", "description": "Perfect for small applications and testing", "features": ["10,000 API calls/month", "All postal terminal data", "Basic support", "Standard rate limits"], "cta": "Get Started"}, "pro": {"name": "Professional", "description": "Best for growing businesses and applications", "period": "/month", "features": ["100,000 API calls/month", "Real-time terminal data", "Priority support", "Advanced filtering", "Webhook notifications", "Custom integrations"], "cta": "Start Pro Plan", "popular": "Most Popular"}}, "cta": {"title": "Ready to Get Started?", "subtitle": "Join thousands of developers using our API to power their applications", "button": "Start Your Free Trial"}}, "auth": {"login": {"title": "Welcome Back", "subtitle": "Sign in to your account to continue", "email": "Email Address", "emailPlaceholder": "Enter your email address", "password": "Password", "passwordPlaceholder": "Enter your password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "submit": "Sign In", "or": "or", "noAccount": "Don't have an account?", "signUp": "Sign up here", "googleSignIn": "Continue with Google"}, "register": {"title": "Create Your Account", "subtitle": "Get started with your free account today", "firstName": "First Name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter your last name", "email": "Email Address", "emailPlaceholder": "Enter your email address", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "acceptTerms": "I agree to the", "termsLink": "Terms of Service", "and": "and", "privacyLink": "Privacy Policy", "submit": "Create Account", "or": "or", "hasAccount": "Already have an account?", "signIn": "Sign in here", "googleSignUp": "Continue with Google"}, "forgotPassword": {"title": "Reset Your Password", "subtitle": "Enter your email address and we'll send you a reset link", "email": "Email Address", "submit": "Send Reset Link", "backToLogin": "Back to Sign In", "success": "Reset link sent! Check your email for instructions."}, "resetPassword": {"title": "Set New Password", "subtitle": "Enter your new password below", "password": "New Password", "confirmPassword": "Confirm New Password", "submit": "Update Password", "success": "Password updated successfully!"}, "passwordStrength": {"label": "Password Strength", "veryWeak": "Very Weak", "weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong"}, "validation": {"invalidEmail": "Invalid email address", "passwordMinLength": "Password must be at least 6 characters", "passwordMinLengthRegister": "Password must be at least 8 characters", "passwordComplexity": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "firstNameMinLength": "First name must be at least 2 characters", "lastNameMinLength": "Last name must be at least 2 characters", "passwordsNoMatch": "Passwords don't match", "acceptTerms": "You must accept the terms and conditions"}, "emailVerification": {"title": "Verify Your Email", "message": "We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.", "emailSentTo": "Email sent to:", "resendEmail": "Resend Verification Email", "resendIn": "Resend in", "backToLogin": "Back to Login", "verifyingTitle": "Verifying Email...", "verifyingMessage": "Please wait while we verify your email address.", "successTitle": "Email Verified!", "successMessage": "Your email has been successfully verified. You can now access all features of your account.", "continueToLogin": "Continue to Login", "helpTitle": "Didn't receive the email?", "helpStep1": "Check your spam or junk folder", "helpStep2": "Make sure the email address is correct", "helpStep3": "Wait a few minutes and try again", "stillNeedHelp": "Still need help?", "contactSupport": "Contact Support"}, "verifyEmail": {"title": "Verify Your Email", "subtitle": "We've sent a verification link to your email address", "resend": "Resend Verification Email", "success": "Email verified successfully!", "error": "Invalid or expired verification link"}}, "errors": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password must be at least 8 characters", "passwordMatch": "Passwords do not match", "terms": "You must agree to the terms and conditions", "loginFailed": "Invalid email or password", "networkError": "Network error. Please try again.", "serverError": "Server error. Please try again later.", "resetFailed": "Failed to reset password. Please try again.", "invalidToken": "Invalid or expired token. Please request a new password reset.", "passwordTooWeak": "Password is too weak. Please choose a stronger password.", "verificationFailed": "Email verification failed. Please try again or request a new verification email.", "resendFailed": "Failed to resend verification email. Please try again later."}, "ui": {"geolocationNotSupported": "Geolocation is not supported by this browser.", "locationFound": "Found your location! Searching for nearby terminals...", "locationError": "Unable to get your location. Please try again or search manually.", "allProviders": "All Providers", "lpExpress": "LP Express", "omniva": "Omniva", "dpd": "DPD", "venipak": "Venipak", "search": "Search", "go": "Go", "readyToStart": "Ready to Get Started?", "choosePlan": "Choose the perfect plan for your needs and start accessing postal terminal data today", "startBuilding": "Start building with our API", "getStarted": "Get Started", "viewDocumentation": "View Documentation", "docs": "Docs", "fastApiResponse": "Fast API Response", "developerSupport": "Developer Support", "secureReliable": "Secure & Reliable", "realTimeData": "Real-time Data", "accessUpToDate": "Access up-to-date terminal information with live status updates", "packageTracking": "Package Tracking", "trackPackages": "Track packages across all major postal providers in one place", "lightningFast": "Lightning Fast", "optimizedApi": "Optimized API responses delivered in under 100ms globally", "easyTerminalSearch": "Easy Terminal Search", "easilySearch": "Easily search terminals across multiple providers using terminal name, city, postal code or location", "startBuildingToday": "Start building with our API today", "simpleIntegration": "Simple Integration", "getStartedCode": "Get started with just a few lines of code", "twentyPercentOff": "20% OFF", "sslEncryption": "SSL encryption", "monitoring247": "24/7 monitoring", "comprehensiveDocumentation": "Comprehensive documentation", "postalApi": "PostalAPI", "invalidResetLink": "Invalid Reset Link", "invalidResetLinkMessage": "This password reset link is invalid or has expired. Please request a new one.", "requestNewResetLink": "Request New Reset Link", "backToLogin": "Back to Login", "rememberPassword": "Remember your password?", "createAccount": "Create Account", "joinThousands": "Join thousands of developers using our API", "alreadyHaveAccount": "Already have an account?", "signInHere": "Sign in here", "dontHaveAccount": "Don't have an account?", "signUpHere": "Sign up here", "clear": "Clear"}, "dashboard": {"welcomeBack": "Welcome back, {name}!", "overviewText": "Here's an overview of your API usage and account status.", "totalRequests": "Total Requests", "thisMonth": "This Month", "apiKeys": "API Keys", "plan": "Plan", "monthlyUsage": "Monthly Usage", "requestsUsed": "Requests Used", "requestsRemaining": "{count} requests remaining this month", "manageApiKeys": "Manage your API keys and access tokens", "manageKeys": "Manage Keys", "documentation": "Documentation", "learnIntegrate": "Learn how to integrate our API", "viewDocs": "View Docs", "upgradePlan": "Upgrade Plan", "getMoreRequests": "Get more requests and features", "upgrade": "Upgrade", "failedToLoad": "Failed to load dashboard data"}, "accessibility": {"findNearbyTerminals": "Find nearby terminals", "selectProvider": "Select provider", "searchTerminals": "Search terminals", "closeAlert": "Close alert", "toggleTheme": "Toggle theme", "englishFlag": "English flag", "lithuanianFlag": "Lithuanian flag"}, "contact": {"title": "Contact Us", "subtitle": "Get in touch with our team for support and inquiries", "getInTouch": "Get in Touch", "email": {"title": "Email Support", "description": "Send us an email and we'll get back to you as soon as possible."}, "hours": {"title": "Business Hours", "description": "Our support team is available during these hours:", "weekdays": "Monday - Friday", "weekends": "Saturday - Sunday", "closed": "Closed"}, "response": {"title": "Response Time", "description": "We aim to respond to all inquiries promptly.", "time": "Typical response: 24-48 hours"}, "support": {"title": "Additional Resources", "documentation": "Documentation", "docDescription": "Find answers to common questions and learn how to integrate our API.", "viewDocs": "View Documentation", "apiStatus": "API Status", "statusDescription": "Check the current status of our API services.", "operational": "All systems operational"}}}