'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { useTranslations } from '@/lib/translations';
import { apiClient, DashboardStats } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import StatsCard, { ProgressStatsCard } from '@/components/ui/StatsCard';
import { BarChart3, Key, CreditCard, TrendingUp, Activity, AlertTriangle } from 'lucide-react';

export default function DashboardPage() {
  const { t } = useTranslations();
  const { user } = useAuthStore();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await apiClient.getDashboardStats();
        setStats(data);
      } catch (err: any) {
        console.error('Failed to fetch dashboard stats:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, []);

  if (error) {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <AlertTriangle className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Failed to Load Dashboard</h1>
              <p className="py-6 opacity-70">{error}</p>
              <button
                className="btn btn-primary"
                onClick={() => window.location.reload()}
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">
            {t('dashboard.welcome')}, {user?.first_name || user?.email}!
          </h1>
          <p className="opacity-70 mt-2">
            Here's an overview of your API usage and account status.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Requests"
            value={stats?.total_requests?.toLocaleString() || '0'}
            icon={BarChart3}
            iconColor="text-primary"
            description="All time"
            loading={loading}
          />

          <StatsCard
            title="This Month"
            value={stats?.requests_this_month?.toLocaleString() || '0'}
            icon={TrendingUp}
            iconColor="text-success"
            description="Current month"
            loading={loading}
          />

          <StatsCard
            title="API Keys"
            value={stats?.api_keys_count?.toString() || '0'}
            icon={Key}
            iconColor="text-warning"
            description="Active keys"
            loading={loading}
          />

          <StatsCard
            title="Plan"
            value={stats?.subscription_plan || 'Free'}
            icon={CreditCard}
            iconColor="text-info"
            description="Current plan"
            loading={loading}
          />
        </div>

        {/* Usage Progress */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ProgressStatsCard
            title="Monthly Usage"
            current={stats?.remaining_quota ? stats.total_quota - stats.remaining_quota : 0}
            total={stats?.total_quota || 1000}
            unit="requests"
            icon={Activity}
            iconColor="text-primary"
            progressColor="progress-primary"
            loading={loading}
          />

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h3 className="card-title">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-4 mt-4">
                <a href="/dashboard/api-keys" className="btn btn-outline">
                  <Key className="w-4 h-4 mr-2" />
                  Manage Keys
                </a>
                <a href="/dashboard/subscription" className="btn btn-outline">
                  <CreditCard className="w-4 h-4 mr-2" />
                  Subscription
                </a>
                <a href="/dashboard/analytics" className="btn btn-outline">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Analytics
                </a>
                <a href="/docs" className="btn btn-outline">
                  📚 API Docs
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Top Endpoints and Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h3 className="card-title">Top Endpoints</h3>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-base-300 rounded w-full mb-2"></div>
                      <div className="h-2 bg-base-300 rounded w-3/4"></div>
                    </div>
                  ))}
                </div>
              ) : stats?.top_endpoints?.length ? (
                <div className="space-y-3">
                  {stats.top_endpoints.map((endpoint, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{endpoint.endpoint}</p>
                        <div className="flex items-center gap-2">
                          <progress
                            className="progress progress-primary w-24"
                            value={endpoint.percentage}
                            max={100}
                          ></progress>
                          <span className="text-xs opacity-70">{endpoint.percentage}%</span>
                        </div>
                      </div>
                      <div className="badge badge-neutral">{endpoint.count}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 opacity-70">
                  <Activity className="w-12 h-12 mx-auto mb-2" />
                  <p>No API usage yet</p>
                </div>
              )}
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h3 className="card-title">Recent Activity</h3>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse flex items-center justify-between">
                      <div className="flex-1">
                        <div className="h-4 bg-base-300 rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-base-300 rounded w-1/2"></div>
                      </div>
                      <div className="h-6 w-16 bg-base-300 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : stats?.recent_activity?.length ? (
                <div className="space-y-4">
                  {stats.recent_activity.map((activity, index) => (
                    <div key={index} className="flex items-center justify-between py-2 border-b border-base-300 last:border-b-0">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.endpoint}</p>
                        <p className="text-xs opacity-60">
                          {new Date(activity.timestamp).toLocaleString()} • {activity.response_time}ms
                        </p>
                      </div>
                      <div className={`badge ${
                        activity.status >= 200 && activity.status < 300 ? 'badge-success' :
                        activity.status >= 400 ? 'badge-error' : 'badge-warning'
                      } badge-sm`}>
                        {activity.status}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 opacity-70">
                  <Activity className="w-12 h-12 mx-auto mb-2" />
                  <p>No recent activity</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}