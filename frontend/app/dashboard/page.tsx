'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/auth-store';
import { useTranslations } from '@/lib/translations';
import { ApiClient } from '@/lib/api';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

interface DashboardStats {
  totalRequests: number;
  requestsThisMonth: number;
  apiKeysCount: number;
  subscriptionPlan: string;
  remainingQuota: number;
  totalQuota: number;
}

export default function DashboardPage() {
  const { t } = useTranslations();
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuthStore();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    fetchDashboardData();
  }, [isAuthenticated, router]);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      const apiClient = new ApiClient();
      
      // Mock data for now - replace with actual API calls
      const mockStats: DashboardStats = {
        totalRequests: 15420,
        requestsThisMonth: 2340,
        apiKeysCount: 3,
        subscriptionPlan: 'Pro',
        remainingQuota: 7660,
        totalQuota: 10000
      };
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setStats(mockStats);
    } catch (err: any) {
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-100">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">
          {t('dashboard.welcomeBack', { name: user?.firstName || 'User' })}
        </h1>
        <p className="text-base-content/70">
          {t('dashboard.overviewText')}
        </p>
        </div>

        {error && (
          <div className="alert alert-error mb-6">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{error}</span>
          </div>
        )}

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card bg-base-200 shadow-xl">
                <div className="card-body">
                  <div className="skeleton h-4 w-20 mb-2"></div>
                  <div className="skeleton h-8 w-16"></div>
                </div>
              </div>
            ))}
          </div>
        ) : stats ? (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="card bg-primary text-primary-content shadow-xl">
                <div className="card-body">
                  <h3 className="card-title text-sm opacity-90">{t('dashboard.totalRequests')}</h3>
                  <p className="text-3xl font-bold">{stats.totalRequests.toLocaleString()}</p>
                </div>
              </div>
              
              <div className="card bg-secondary text-secondary-content shadow-xl">
                <div className="card-body">
                  <h3 className="card-title text-sm opacity-90">{t('dashboard.thisMonth')}</h3>
                  <p className="text-3xl font-bold">{stats.requestsThisMonth.toLocaleString()}</p>
                </div>
              </div>
              
              <div className="card bg-accent text-accent-content shadow-xl">
                <div className="card-body">
                  <h3 className="card-title text-sm opacity-90">{t('dashboard.apiKeys')}</h3>
                  <p className="text-3xl font-bold">{stats.apiKeysCount}</p>
                </div>
              </div>
              
              <div className="card bg-success text-success-content shadow-xl">
                <div className="card-body">
                  <h3 className="card-title text-sm opacity-90">{t('dashboard.plan')}</h3>
                  <p className="text-3xl font-bold">{stats.subscriptionPlan}</p>
                </div>
              </div>
            </div>

            {/* Usage Progress */}
            <div className="card bg-base-200 shadow-xl mb-8">
              <div className="card-body">
                <h3 className="card-title mb-4">{t('dashboard.monthlyUsage')}</h3>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm">{t('dashboard.requestsUsed')}</span>
                  <span className="text-sm font-medium">
                    {(stats.totalQuota - stats.remainingQuota).toLocaleString()} / {stats.totalQuota.toLocaleString()}
                  </span>
                </div>
                <progress 
                  className="progress progress-primary w-full" 
                  value={stats.totalQuota - stats.remainingQuota} 
                  max={stats.totalQuota}
                ></progress>
                <div className="text-sm text-base-content/70 mt-2">
                  {t('dashboard.requestsRemaining', { count: stats.remainingQuota.toLocaleString() })}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="card bg-base-200 shadow-xl">
                <div className="card-body">
                  <h3 className="card-title">{t('dashboard.apiKeys')}</h3>
                  <p className="text-base-content/70 mb-4">
                    {t('dashboard.manageApiKeys')}
                  </p>
                  <div className="card-actions">
                    <button className="btn btn-primary btn-sm">
                      {t('dashboard.manageKeys')}
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="card bg-base-200 shadow-xl">
                <div className="card-body">
                  <h3 className="card-title">{t('dashboard.documentation')}</h3>
                  <p className="text-base-content/70 mb-4">
                    {t('dashboard.learnIntegrate')}
                  </p>
                  <div className="card-actions">
                    <button className="btn btn-outline btn-sm">
                      {t('dashboard.viewDocs')}
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="card bg-base-200 shadow-xl">
                <div className="card-body">
                  <h3 className="card-title">{t('dashboard.upgradePlan')}</h3>
                  <p className="text-base-content/70 mb-4">
                    {t('dashboard.getMoreRequests')}
                  </p>
                  <div className="card-actions">
                    <button className="btn btn-secondary btn-sm">
                      {t('dashboard.upgrade')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : null}
      </main>
      
      <Footer />
    </div>
  );
}