'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import StatsCard from '@/components/ui/StatsCard';
import DataTable, { Column } from '@/components/ui/DataTable';
import { BarChart3, TrendingUp, Clock, Activity, AlertTriangle, Calendar } from 'lucide-react';

interface AnalyticsData {
  usageStats: any;
  timeSeriesData: any[];
  topEndpoints: Array<{
    endpoint: string;
    count: number;
    percentage: number;
    avg_response_time: number;
  }>;
  quotas: {
    current_usage: number;
    limit: number;
    percentage_used: number;
    reset_date: string;
  };
}

export default function AnalyticsPage() {
  const { user } = useAuthStore();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [usageStats, timeSeriesData, topEndpoints, quotas] = await Promise.all([
        apiClient.getUsageStats({
          startDate: dateRange.startDate,
          endDate: dateRange.endDate
        }),
        apiClient.getTimeSeriesData({
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
          interval: 'day'
        }),
        apiClient.getTopEndpoints({
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
          limit: 10
        }),
        apiClient.getUsageQuotas()
      ]);

      setAnalytics({
        usageStats,
        timeSeriesData,
        topEndpoints,
        quotas
      });
    } catch (err: any) {
      console.error('Failed to fetch analytics:', err);
      setError(err.message || 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const endpointColumns: Column<any>[] = [
    {
      key: 'endpoint',
      header: 'Endpoint',
      render: (value) => (
        <code className="text-sm bg-base-200 px-2 py-1 rounded">{value}</code>
      )
    },
    {
      key: 'count',
      header: 'Requests',
      render: (value) => value?.toLocaleString() || '0'
    },
    {
      key: 'percentage',
      header: 'Usage %',
      render: (value) => (
        <div className="flex items-center gap-2">
          <progress 
            className="progress progress-primary w-16" 
            value={value} 
            max={100}
          ></progress>
          <span className="text-sm">{value?.toFixed(1)}%</span>
        </div>
      )
    },
    {
      key: 'avg_response_time',
      header: 'Avg Response Time',
      render: (value) => `${value?.toFixed(0) || 0}ms`
    }
  ];

  if (error && !analytics) {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <AlertTriangle className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Failed to Load Analytics</h1>
              <p className="py-6 opacity-70">{error}</p>
              <button 
                className="btn btn-primary"
                onClick={fetchAnalytics}
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Analytics</h1>
            <p className="opacity-70 mt-2">
              Detailed insights into your API usage and performance
            </p>
          </div>
          
          {/* Date Range Selector */}
          <div className="flex items-center gap-2">
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="input input-bordered input-sm"
            />
            <span className="text-sm opacity-70">to</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="input input-bordered input-sm"
            />
          </div>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Requests"
            value={analytics?.usageStats?.total_requests?.toLocaleString() || '0'}
            icon={BarChart3}
            iconColor="text-primary"
            description="Selected period"
            loading={loading}
          />

          <StatsCard
            title="Success Rate"
            value={analytics?.usageStats?.success_rate ? `${analytics.usageStats.success_rate.toFixed(1)}%` : '0%'}
            icon={TrendingUp}
            iconColor="text-success"
            description="2xx responses"
            loading={loading}
          />

          <StatsCard
            title="Avg Response Time"
            value={analytics?.usageStats?.avg_response_time ? `${analytics.usageStats.avg_response_time.toFixed(0)}ms` : '0ms'}
            icon={Clock}
            iconColor="text-info"
            description="All endpoints"
            loading={loading}
          />

          <StatsCard
            title="Unique Endpoints"
            value={analytics?.usageStats?.unique_endpoints?.toString() || '0'}
            icon={Activity}
            iconColor="text-warning"
            description="Accessed"
            loading={loading}
          />
        </div>

        {/* Usage Quota */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h3 className="card-title">Usage Timeline</h3>
                {loading ? (
                  <div className="h-64 flex items-center justify-center">
                    <span className="loading loading-spinner loading-lg"></span>
                  </div>
                ) : analytics?.timeSeriesData?.length ? (
                  <div className="h-64 flex items-center justify-center opacity-70">
                    <div className="text-center">
                      <BarChart3 className="w-16 h-16 mx-auto mb-4" />
                      <p>Chart visualization would go here</p>
                      <p className="text-sm">Integrate with your preferred charting library</p>
                    </div>
                  </div>
                ) : (
                  <div className="h-64 flex items-center justify-center opacity-70">
                    <div className="text-center">
                      <Activity className="w-16 h-16 mx-auto mb-4" />
                      <p>No usage data for selected period</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h3 className="card-title text-sm">Monthly Quota</h3>
                {loading ? (
                  <div className="animate-pulse space-y-2">
                    <div className="h-4 bg-base-300 rounded w-1/2"></div>
                    <div className="h-2 bg-base-300 rounded w-full"></div>
                    <div className="h-3 bg-base-300 rounded w-1/3"></div>
                  </div>
                ) : analytics?.quotas ? (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used</span>
                      <span className="font-semibold">
                        {analytics.quotas.current_usage.toLocaleString()} / {analytics.quotas.limit.toLocaleString()}
                      </span>
                    </div>
                    <progress 
                      className="progress progress-primary w-full" 
                      value={analytics.quotas.current_usage} 
                      max={analytics.quotas.limit}
                    ></progress>
                    <div className="text-xs opacity-70">
                      {analytics.quotas.percentage_used.toFixed(1)}% used • Resets {new Date(analytics.quotas.reset_date).toLocaleDateString()}
                    </div>
                  </div>
                ) : null}
              </div>
            </div>

            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h3 className="card-title text-sm">Quick Stats</h3>
                {loading ? (
                  <div className="animate-pulse space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex justify-between">
                        <div className="h-3 bg-base-300 rounded w-1/2"></div>
                        <div className="h-3 bg-base-300 rounded w-1/4"></div>
                      </div>
                    ))}
                  </div>
                ) : analytics?.usageStats ? (
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="opacity-70">Peak Hour</span>
                      <span className="font-semibold">{analytics.usageStats.peak_hour || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="opacity-70">Error Rate</span>
                      <span className="font-semibold">{analytics.usageStats.error_rate?.toFixed(2) || '0'}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="opacity-70">Most Active Day</span>
                      <span className="font-semibold">{analytics.usageStats.most_active_day || 'N/A'}</span>
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </div>

        {/* Top Endpoints */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h3 className="card-title">Top Endpoints</h3>
            <DataTable
              data={analytics?.topEndpoints || []}
              columns={endpointColumns}
              loading={loading}
              emptyMessage="No endpoint usage data available for the selected period"
            />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
