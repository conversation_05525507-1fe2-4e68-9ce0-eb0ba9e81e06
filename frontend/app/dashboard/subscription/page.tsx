'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient, UserSubscription, SubscriptionPlan } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import StatsCard from '@/components/ui/StatsCard';
import Modal, { ConfirmModal } from '@/components/ui/Modal';
import { CreditCard, Calendar, AlertTriangle, CheckCircle, XCircle, Clock } from 'lucide-react';

export default function SubscriptionPage() {
  const { user } = useAuthStore();
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [cancelConfirm, setCancelConfirm] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [subscriptionData, plansData] = await Promise.all([
        apiClient.getUserSubscription(),
        apiClient.getSubscriptionPlans()
      ]);
      setSubscription(subscriptionData);
      setPlans(plansData);
    } catch (err: any) {
      console.error('Failed to fetch subscription data:', err);
      setError(err.message || 'Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async () => {
    if (!selectedPlan) return;

    try {
      setActionLoading(true);
      const result = await apiClient.changePlan({
        newPlanId: selectedPlan.id,
        billingCycle,
        successUrl: `${window.location.origin}/dashboard/subscription?success=true`,
        cancelUrl: `${window.location.origin}/dashboard/subscription?canceled=true`
      });
      
      // Redirect to Stripe checkout
      window.location.href = result.url;
    } catch (err: any) {
      console.error('Failed to upgrade plan:', err);
      setError(err.message || 'Failed to upgrade plan');
      setActionLoading(false);
    }
  };

  const handleCancel = async () => {
    try {
      setActionLoading(true);
      await apiClient.cancelSubscription(true);
      await fetchSubscriptionData();
      setCancelConfirm(false);
    } catch (err: any) {
      console.error('Failed to cancel subscription:', err);
      setError(err.message || 'Failed to cancel subscription');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <div className="badge badge-success gap-2"><CheckCircle className="w-3 h-3" />Active</div>;
      case 'past_due':
        return <div className="badge badge-warning gap-2"><Clock className="w-3 h-3" />Past Due</div>;
      case 'canceled':
        return <div className="badge badge-error gap-2"><XCircle className="w-3 h-3" />Canceled</div>;
      case 'trialing':
        return <div className="badge badge-info gap-2"><Clock className="w-3 h-3" />Trial</div>;
      default:
        return <div className="badge badge-neutral">{status}</div>;
    }
  };

  const formatPrice = (price: number, interval: string) => {
    return `€${price.toFixed(2)}/${interval === 'yearly' ? 'year' : 'month'}`;
  };

  if (error && !subscription) {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <AlertTriangle className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Failed to Load Subscription</h1>
              <p className="py-6 opacity-70">{error}</p>
              <button 
                className="btn btn-primary"
                onClick={fetchSubscriptionData}
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Subscription</h1>
          <p className="opacity-70 mt-2">
            Manage your subscription plan and billing information
          </p>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Current Subscription */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h2 className="card-title">Current Plan</h2>
                {loading ? (
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-base-300 rounded w-1/2"></div>
                    <div className="h-8 bg-base-300 rounded w-1/3"></div>
                    <div className="h-4 bg-base-300 rounded w-2/3"></div>
                  </div>
                ) : subscription ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-2xl font-bold">{subscription.plan?.display_name || 'Unknown Plan'}</h3>
                        <p className="opacity-70">{subscription.plan?.description}</p>
                      </div>
                      {getStatusBadge(subscription.status)}
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm opacity-70">Price</p>
                        <p className="font-semibold">
                          {subscription.plan ? formatPrice(subscription.plan.price_eur, subscription.billing_cycle) : 'N/A'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm opacity-70">Billing Cycle</p>
                        <p className="font-semibold capitalize">{subscription.billing_cycle}</p>
                      </div>
                      <div>
                        <p className="text-sm opacity-70">Current Period</p>
                        <p className="font-semibold">
                          {new Date(subscription.current_period_start).toLocaleDateString()} - {new Date(subscription.current_period_end).toLocaleDateString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm opacity-70">API Requests</p>
                        <p className="font-semibold">
                          {subscription.api_requests_used.toLocaleString()} / {subscription.plan?.api_requests_per_month.toLocaleString() || 'Unlimited'}
                        </p>
                      </div>
                    </div>

                    <div className="card-actions justify-end">
                      <button
                        onClick={() => setShowUpgradeModal(true)}
                        className="btn btn-primary"
                      >
                        Change Plan
                      </button>
                      {subscription.status === 'active' && (
                        <button
                          onClick={() => setCancelConfirm(true)}
                          className="btn btn-outline btn-error"
                        >
                          Cancel Subscription
                        </button>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CreditCard className="w-16 h-16 mx-auto opacity-50 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Active Subscription</h3>
                    <p className="opacity-70 mb-4">Choose a plan to get started with the Postal Terminal API</p>
                    <button
                      onClick={() => setShowUpgradeModal(true)}
                      className="btn btn-primary"
                    >
                      Choose Plan
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Usage Stats */}
          <div className="space-y-4">
            <StatsCard
              title="API Requests Used"
              value={subscription?.api_requests_used?.toLocaleString() || '0'}
              icon={Calendar}
              iconColor="text-primary"
              description="This billing period"
              loading={loading}
            />
            
            <StatsCard
              title="Days Remaining"
              value={subscription ? Math.max(0, Math.ceil((new Date(subscription.current_period_end).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))).toString() : '0'}
              icon={Clock}
              iconColor="text-info"
              description="Until next billing"
              loading={loading}
            />
          </div>
        </div>

        {/* Available Plans Modal */}
        <Modal
          isOpen={showUpgradeModal}
          onClose={() => setShowUpgradeModal(false)}
          title="Choose Your Plan"
          size="xl"
        >
          <div className="space-y-6">
            {/* Billing Cycle Toggle */}
            <div className="flex justify-center">
              <div className="tabs tabs-boxed">
                <button
                  className={`tab ${billingCycle === 'monthly' ? 'tab-active' : ''}`}
                  onClick={() => setBillingCycle('monthly')}
                >
                  Monthly
                </button>
                <button
                  className={`tab ${billingCycle === 'yearly' ? 'tab-active' : ''}`}
                  onClick={() => setBillingCycle('yearly')}
                >
                  Yearly (Save 20%)
                </button>
              </div>
            </div>

            {/* Plans Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {plans
                .filter(plan => plan.billing_interval === billingCycle && plan.is_public)
                .sort((a, b) => a.sort_order - b.sort_order)
                .map((plan) => (
                  <div
                    key={plan.id}
                    className={`card border-2 cursor-pointer transition-all ${
                      selectedPlan?.id === plan.id
                        ? 'border-primary bg-primary/5'
                        : 'border-base-300 hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedPlan(plan)}
                  >
                    <div className="card-body">
                      <h3 className="card-title">{plan.display_name}</h3>
                      <div className="text-2xl font-bold text-primary">
                        {formatPrice(plan.price_eur, plan.billing_interval)}
                      </div>
                      <p className="text-sm opacity-70">{plan.description}</p>
                      
                      <div className="space-y-2 mt-4">
                        <div className="flex justify-between text-sm">
                          <span>API Requests/month</span>
                          <span className="font-semibold">{plan.api_requests_per_month.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Rate Limit</span>
                          <span className="font-semibold">{plan.api_requests_per_minute}/min</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Max API Keys</span>
                          <span className="font-semibold">{plan.max_api_keys}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>

            <div className="modal-action">
              <button
                onClick={() => setShowUpgradeModal(false)}
                className="btn btn-outline"
                disabled={actionLoading}
              >
                Cancel
              </button>
              <button
                onClick={handleUpgrade}
                className="btn btn-primary"
                disabled={!selectedPlan || actionLoading}
              >
                {actionLoading && <span className="loading loading-spinner loading-sm"></span>}
                {subscription ? 'Change Plan' : 'Subscribe'}
              </button>
            </div>
          </div>
        </Modal>

        {/* Cancel Confirmation Modal */}
        <ConfirmModal
          isOpen={cancelConfirm}
          onClose={() => setCancelConfirm(false)}
          onConfirm={handleCancel}
          title="Cancel Subscription"
          message="Are you sure you want to cancel your subscription? You'll continue to have access until the end of your current billing period."
          confirmText="Cancel Subscription"
          type="warning"
          loading={actionLoading}
        />
      </div>
    </DashboardLayout>
  );
}
