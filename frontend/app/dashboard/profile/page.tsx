'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient, User } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { User as UserIcon, Mail, Calendar, Shield, AlertTriangle, CheckCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const profileSchema = z.object({
  first_name: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  email: z.string().email('Invalid email address'),
});

const passwordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z.string().min(8, 'Password must be at least 8 characters'),
  confirm_password: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
});

type ProfileForm = z.infer<typeof profileSchema>;
type PasswordForm = z.infer<typeof passwordSchema>;

export default function ProfilePage() {
  const { user, updateUser } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const profileForm = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      email: user?.email || '',
    }
  });

  const passwordForm = useForm<PasswordForm>({
    resolver: zodResolver(passwordSchema)
  });

  useEffect(() => {
    if (user) {
      profileForm.reset({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
      });
    }
  }, [user, profileForm]);

  const handleProfileUpdate = async (data: ProfileForm) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const updatedUser = await apiClient.updateProfile(data);
      updateUser(updatedUser);
      setSuccess('Profile updated successfully');
    } catch (err: any) {
      console.error('Failed to update profile:', err);
      setError(err.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (data: PasswordForm) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      await apiClient.changePassword({
        current_password: data.current_password,
        new_password: data.new_password
      });
      
      passwordForm.reset();
      setSuccess('Password changed successfully');
    } catch (err: any) {
      console.error('Failed to change password:', err);
      setError(err.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      await apiClient.resendVerificationEmail();
      setSuccess('Verification email sent successfully');
    } catch (err: any) {
      console.error('Failed to resend verification:', err);
      setError(err.message || 'Failed to send verification email');
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Profile</h1>
          <p className="opacity-70 mt-2">
            Manage your account information and security settings
          </p>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {success && (
          <div className="alert alert-success">
            <CheckCircle className="w-5 h-5" />
            <span>{success}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setSuccess(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Overview */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">Account Overview</h2>
              
              <div className="flex items-center gap-4 mb-4">
                <div className="avatar placeholder">
                  <div className="bg-primary text-primary-content rounded-full w-16">
                    <span className="text-xl">
                      {user?.first_name?.[0] || user?.email?.[0] || 'U'}
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold">
                    {user?.first_name && user?.last_name 
                      ? `${user.first_name} ${user.last_name}`
                      : user?.email
                    }
                  </h3>
                  <p className="text-sm opacity-70">{user?.email}</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 opacity-70" />
                  <div className="flex-1">
                    <span className="text-sm">Email Status</span>
                    <div className="flex items-center gap-2">
                      {user?.email_verified ? (
                        <div className="badge badge-success badge-sm gap-1">
                          <CheckCircle className="w-3 h-3" />
                          Verified
                        </div>
                      ) : (
                        <div className="badge badge-warning badge-sm gap-1">
                          <AlertTriangle className="w-3 h-3" />
                          Unverified
                        </div>
                      )}
                    </div>
                  </div>
                  {!user?.email_verified && (
                    <button
                      onClick={handleResendVerification}
                      className="btn btn-xs btn-outline"
                      disabled={loading}
                    >
                      Resend
                    </button>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  <Shield className="w-4 h-4 opacity-70" />
                  <div>
                    <span className="text-sm">Role</span>
                    <div className="badge badge-neutral badge-sm ml-2">
                      {user?.role === 'ADMIN' ? 'Administrator' : 'Customer'}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Calendar className="w-4 h-4 opacity-70" />
                  <div>
                    <span className="text-sm">Member since</span>
                    <p className="text-sm font-medium">
                      {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
                    </p>
                  </div>
                </div>

                {user?.last_login_at && (
                  <div className="flex items-center gap-3">
                    <UserIcon className="w-4 h-4 opacity-70" />
                    <div>
                      <span className="text-sm">Last login</span>
                      <p className="text-sm font-medium">
                        {new Date(user.last_login_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Profile Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Personal Information */}
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h2 className="card-title">Personal Information</h2>
                
                <form onSubmit={profileForm.handleSubmit(handleProfileUpdate)} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">First Name *</span>
                      </label>
                      <input
                        {...profileForm.register('first_name')}
                        type="text"
                        className={`input input-bordered ${profileForm.formState.errors.first_name ? 'input-error' : ''}`}
                      />
                      {profileForm.formState.errors.first_name && (
                        <label className="label">
                          <span className="label-text-alt text-error">
                            {profileForm.formState.errors.first_name.message}
                          </span>
                        </label>
                      )}
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">Last Name *</span>
                      </label>
                      <input
                        {...profileForm.register('last_name')}
                        type="text"
                        className={`input input-bordered ${profileForm.formState.errors.last_name ? 'input-error' : ''}`}
                      />
                      {profileForm.formState.errors.last_name && (
                        <label className="label">
                          <span className="label-text-alt text-error">
                            {profileForm.formState.errors.last_name.message}
                          </span>
                        </label>
                      )}
                    </div>
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Email Address *</span>
                    </label>
                    <input
                      {...profileForm.register('email')}
                      type="email"
                      className={`input input-bordered ${profileForm.formState.errors.email ? 'input-error' : ''}`}
                    />
                    {profileForm.formState.errors.email && (
                      <label className="label">
                        <span className="label-text-alt text-error">
                          {profileForm.formState.errors.email.message}
                        </span>
                      </label>
                    )}
                  </div>

                  <div className="card-actions justify-end">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading || !profileForm.formState.isDirty}
                    >
                      {loading && <span className="loading loading-spinner loading-sm"></span>}
                      Update Profile
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {/* Change Password */}
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h2 className="card-title">Change Password</h2>
                
                <form onSubmit={passwordForm.handleSubmit(handlePasswordChange)} className="space-y-4">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Current Password *</span>
                    </label>
                    <input
                      {...passwordForm.register('current_password')}
                      type="password"
                      className={`input input-bordered ${passwordForm.formState.errors.current_password ? 'input-error' : ''}`}
                    />
                    {passwordForm.formState.errors.current_password && (
                      <label className="label">
                        <span className="label-text-alt text-error">
                          {passwordForm.formState.errors.current_password.message}
                        </span>
                      </label>
                    )}
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">New Password *</span>
                    </label>
                    <input
                      {...passwordForm.register('new_password')}
                      type="password"
                      className={`input input-bordered ${passwordForm.formState.errors.new_password ? 'input-error' : ''}`}
                    />
                    {passwordForm.formState.errors.new_password && (
                      <label className="label">
                        <span className="label-text-alt text-error">
                          {passwordForm.formState.errors.new_password.message}
                        </span>
                      </label>
                    )}
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Confirm New Password *</span>
                    </label>
                    <input
                      {...passwordForm.register('confirm_password')}
                      type="password"
                      className={`input input-bordered ${passwordForm.formState.errors.confirm_password ? 'input-error' : ''}`}
                    />
                    {passwordForm.formState.errors.confirm_password && (
                      <label className="label">
                        <span className="label-text-alt text-error">
                          {passwordForm.formState.errors.confirm_password.message}
                        </span>
                      </label>
                    )}
                  </div>

                  <div className="card-actions justify-end">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading && <span className="loading loading-spinner loading-sm"></span>}
                      Change Password
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
