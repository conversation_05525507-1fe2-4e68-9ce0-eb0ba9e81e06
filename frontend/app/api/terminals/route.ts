import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Helper function to get API key for terminal requests
async function getApiKeyForTerminalRequest(request: NextRequest): Promise<string | null> {
  try {
    // First, try to get user's API key if they're authenticated
    const cookieStore = cookies();
    const accessToken = cookieStore.get('access_token')?.value;
    
    if (accessToken) {
      // User is authenticated, try to get their API key
      const response = await fetch(`${BACKEND_URL}/api/v1/my-api-keys`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        const activeKey = data.data?.find((key: any) => key.is_active);
        if (activeKey) {
          // We have the API key metadata, but we need the actual secret
          // For security, we'll create a demo key or use environment variable
          return process.env.DEMO_API_KEY || null;
        }
      }
    }
    
    // Fallback to demo/public API key for unauthenticated users
    return process.env.DEMO_API_KEY || null;
  } catch (error) {
    console.error('Error getting API key:', error);
    return process.env.DEMO_API_KEY || null;
  }
}

// Helper function to make backend request with API key
async function makeBackendRequest(endpoint: string, apiKey: string) {
  const response = await fetch(`${BACKEND_URL}/api/v1${endpoint}`, {
    headers: {
      'X-API-Key': apiKey,
      'Content-Type': 'application/json',
    },
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error?.message || errorData.message || `HTTP ${response.status}`);
  }
  
  return response.json();
}

export async function GET(request: NextRequest) {
  try {
    // Get API key for this request
    const apiKey = await getApiKeyForTerminalRequest(request);
    
    if (!apiKey) {
      return NextResponse.json(
        { 
          error: 'API_KEY_UNAVAILABLE', 
          message: 'Unable to access terminal data. Please create an API key in your dashboard or contact support.',
          requiresAuth: true
        },
        { status: 401 }
      );
    }
    
    // Extract query parameters
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    
    // Make request to backend
    const data = await makeBackendRequest(`/terminals${queryString ? `?${queryString}` : ''}`, apiKey);
    
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Terminal API proxy error:', error);
    
    return NextResponse.json(
      { 
        error: 'TERMINAL_API_ERROR', 
        message: error.message || 'Failed to fetch terminals',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
