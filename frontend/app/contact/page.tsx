'use client';

import { useTranslations } from '@/lib/translations';
import { Mail, MapPin, Clock, Phone } from 'lucide-react';
import Header from '@/components/layout/Header';

export default function ContactPage() {
  const { t } = useTranslations();

  return (
    <div className="min-h-screen bg-base-100">
      <Header />
      {/* Page Header */}
      <div className="bg-primary text-primary-content">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">{t('contact.title')}</h1>
            <p className="text-xl opacity-90">{t('contact.subtitle')}</p>
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 gap-12">
          {/* Contact Details */}
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-semibold mb-6">{t('contact.getInTouch')}</h2>
              <div className="space-y-6">
                {/* Email */}
                <div className="flex items-start space-x-4">
                  <div className="bg-primary/10 p-3 rounded-lg">
                    <Mail className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{t('contact.email.title')}</h3>
                    <p className="text-base-content/70 mb-2">{t('contact.email.description')}</p>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-primary hover:text-primary-focus font-medium"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>

                {/* Business Hours */}
                <div className="flex items-start space-x-4">
                  <div className="bg-primary/10 p-3 rounded-lg">
                    <Clock className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{t('contact.hours.title')}</h3>
                    <p className="text-base-content/70 mb-2">{t('contact.hours.description')}</p>
                    <div className="space-y-1">
                      <p className="font-medium">{t('contact.hours.weekdays')}: 9:00 AM - 6:00 PM (UTC)</p>
                      <p className="font-medium">{t('contact.hours.weekends')}: {t('contact.hours.closed')}</p>
                    </div>
                  </div>
                </div>

                {/* Response Time */}
                <div className="flex items-start space-x-4">
                  <div className="bg-primary/10 p-3 rounded-lg">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{t('contact.response.title')}</h3>
                    <p className="text-base-content/70 mb-2">{t('contact.response.description')}</p>
                    <p className="font-medium">{t('contact.response.time')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-semibold mb-6">{t('contact.support.title')}</h2>
              <div className="bg-base-200 p-6 rounded-lg space-y-4">
                <div>
                  <h3 className="font-semibold text-lg mb-2">{t('contact.support.documentation')}</h3>
                  <p className="text-base-content/70 mb-3">{t('contact.support.docDescription')}</p>
                  <a 
                    href="/docs" 
                    className="btn btn-outline btn-primary"
                  >
                    {t('contact.support.viewDocs')}
                  </a>
                </div>
                
                <div className="divider"></div>
                
                <div>
                  <h3 className="font-semibold text-lg mb-2">{t('contact.support.apiStatus')}</h3>
                  <p className="text-base-content/70 mb-3">{t('contact.support.statusDescription')}</p>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-success rounded-full"></div>
                    <span className="font-medium text-success">{t('contact.support.operational')}</span>
                  </div>
                </div>
                

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}