@import "tailwindcss";
@plugin "daisyui" {
  themes: winter --default, dark --prefersdark;
  root: ":root";
  include: ;
  exclude: ;
  prefix: ;
  logs: true;
}

@plugin "daisyui/theme" {
  name: "winter";
  default: false;
  prefersdark: false;
  color-scheme: "light";
  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(97.466% 0.011 259.822);
  --color-base-300: oklch(93.268% 0.016 262.751);
  --color-base-content: oklch(41.886% 0.053 255.824);
  --color-primary: oklch(56.86% 0.255 257.57);
  --color-primary-content: oklch(91.372% 0.051 257.57);
  --color-secondary: oklch(42.551% 0.161 282.339);
  --color-secondary-content: oklch(98% 0.002 247.839);
  --color-accent: oklch(59.939% 0.191 335.171);
  --color-accent-content: oklch(11.988% 0.038 335.171);
  --color-neutral: oklch(44% 0.043 257.281);
  --color-neutral-content: oklch(98% 0.002 247.839);
  --color-info: oklch(70% 0.165 254.624);
  --color-info-content: oklch(37% 0.044 257.287);
  --color-success: oklch(69% 0.17 162.48);
  --color-success-content: oklch(98% 0.002 247.839);
  --color-warning: oklch(83% 0.128 66.29);
  --color-warning-content: oklch(17.834% 0.009 71.47);
  --color-error: oklch(70% 0.191 22.216);
  --color-error-content: oklch(98% 0.002 247.839);
  --radius-selector: 1rem;
  --radius-field: 0.5rem;
  --radius-box: 1rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 0;
  --noise: 0;
}


@plugin "daisyui/theme" {
  name: "dark";
  default: false;
  prefersdark: true;
  color-scheme: "dark";
  --color-base-100: oklch(22% 0.02 240);
  --color-base-200: oklch(18% 0.02 240);
  --color-base-300: oklch(14% 0.02 240);
  --color-base-content: oklch(85% 0.02 240);
  --color-primary: oklch(56.86% 0.255 257.57);
  --color-primary-content: oklch(91.372% 0.051 257.57);
  --color-secondary: oklch(42.551% 0.161 282.339);
  --color-secondary-content: oklch(98% 0.002 247.839);
  --color-accent: oklch(59.939% 0.191 335.171);
  --color-accent-content: oklch(11.988% 0.038 335.171);
  --color-neutral: oklch(75% 0.06 257.651);
  --color-neutral-content: oklch(25% 0.06 257.651);
  --color-info: oklch(70% 0.165 254.624);
  --color-info-content: oklch(37% 0.044 257.287);
  --color-success: oklch(69% 0.17 162.48);
  --color-success-content: oklch(98% 0.002 247.839);
  --color-warning: oklch(83% 0.128 66.29);
  --color-warning-content: oklch(17.834% 0.009 71.47);
  --color-error: oklch(70% 0.191 22.216);
  --color-error-content: oklch(98% 0.002 247.839);
  --radius-selector: 1rem;
  --radius-field: 0.5rem;
  --radius-box: 1rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 0;
  --noise: 0;
}
