import RegisterForm from '@/components/auth/RegisterForm';
import Link from 'next/link';
import { getTranslations } from '@/lib/translations';
import BackButton from '@/components/ui/BackButton';
import ThemeSwitcher from '@/components/ui/ThemeToggle';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';

export default async function RegisterPage() {
  const { t } = getTranslations();
  
  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center p-4 relative">
      {/* Top Navigation */}
      <div className="absolute top-4 left-4">
        <BackButton showText />
      </div>
      
      <div className="absolute top-4 right-4 flex gap-2">
        <LanguageSwitcher />
        <ThemeSwitcher />
      </div>

      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="btn btn-ghost text-xl font-bold">
            {t('ui.postalApi')}
          </Link>
        </div>

        {/* Register Form */}
        <RegisterForm />
      </div>
    </div>
  );
}