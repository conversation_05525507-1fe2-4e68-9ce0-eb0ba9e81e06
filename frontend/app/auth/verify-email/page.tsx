'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import EmailVerificationForm from '@/components/auth/EmailVerificationForm';
import Link from 'next/link';

function VerifyEmailContent() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const email = searchParams.get('email');

  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="btn btn-ghost text-xl font-bold">
            PostalAPI
          </Link>
        </div>

        {/* Email Verification Form */}
        <EmailVerificationForm token={token || undefined} email={email || undefined} />
      </div>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  );
}