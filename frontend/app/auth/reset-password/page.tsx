'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import ResetPasswordForm from '@/components/auth/ResetPasswordForm';
import Link from 'next/link';
import { useTranslations } from '@/lib/translations';

function ResetPasswordContent() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const { t } = useTranslations();

  if (!token) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body text-center">
              <div className="mx-auto w-16 h-16 bg-error/10 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              
              <h2 className="card-title text-2xl font-bold mb-4">{t('ui.invalidResetLink')}</h2>
              
              <p className="text-base-content/70 mb-6">
                {t('ui.invalidResetLinkMessage')}
              </p>
              
              <div className="space-y-3">
                <Link href="/auth/forgot-password" className="btn btn-primary w-full">
                  {t('ui.requestNewResetLink')}
                </Link>
                <Link href="/auth/login" className="btn btn-outline w-full">
                  {t('ui.backToLogin')}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="btn btn-ghost text-xl font-bold">
            {t('ui.postalApi')}
          </Link>
          <h1 className="text-3xl font-bold mt-4 mb-2">{t('auth.resetPassword.title')}</h1>
          <p className="text-base-content/70">
            {t('auth.resetPassword.subtitle')}
          </p>
        </div>

        {/* Reset Password Form */}
        <ResetPasswordForm token={token} />

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-base-content/70">
            {t('ui.rememberPassword')}{' '}
            <Link href="/auth/login" className="link link-primary">
              {t('ui.signInHere')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    }>
      <ResetPasswordContent />
    </Suspense>
  );
}