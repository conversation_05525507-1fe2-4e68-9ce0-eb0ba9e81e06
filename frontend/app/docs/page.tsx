'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from '@/lib/translations';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { apiSpec } from '@/lib/api-spec';
import EndpointCard from '@/components/docs/EndpointCard';
import ApiKeyInput from '@/components/docs/ApiKeyInput';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export default function DocsPage() {
  const { t } = useTranslations();
  const [apiKey, setApiKey] = useState('');
  const [activeSection, setActiveSection] = useState('getting-started');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Group endpoints by tags
  const terminalEndpoints = Object.entries(apiSpec.paths).filter(([path, methods]) => 
    Object.values(methods as any).some((method: any) => method.tags?.includes('Terminals'))
  );

  const trackingEndpoints = Object.entries(apiSpec.paths).filter(([path, methods]) => 
    Object.values(methods as any).some((method: any) => method.tags?.includes('Tracking'))
  );

  // Navigation items
  const navItems = [
    { id: 'getting-started', label: t('docs.navigation.gettingStarted'), icon: 'M13 10V3L4 14h7v7l9-11h-7z' },
    { id: 'authentication', label: t('docs.navigation.authentication'), icon: 'M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z' },
    { id: 'terminals', label: t('docs.navigation.terminalEndpoints'), icon: 'M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z' },
    { id: 'tracking', label: t('docs.navigation.trackingEndpoints'), icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4' },
    { id: 'errors', label: t('docs.navigation.errorHandling'), icon: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z' },
    { id: 'examples', label: t('docs.navigation.codeExamples'), icon: 'M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4' }
  ];

  // Scroll to section
  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId);
    setSidebarOpen(false);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <div className="min-h-screen bg-base-100">
      <Header />
      
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-20 left-4 z-50">
        <button 
          className="btn btn-circle btn-primary shadow-lg"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>

      <div className="flex">
        {/* Sidebar Navigation */}
        <aside className={`fixed lg:sticky top-0 left-0 z-40 w-72 lg:w-80 h-screen bg-base-200 border-r border-base-300 transition-transform duration-300 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}`}>
          <div className="flex flex-col h-full">
            {/* Sidebar Header */}
            <div className="p-6 border-b border-base-300">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-base-content">{t('docs.sidebar.apiDocumentation')}</h2>
                  <p className="text-sm text-base-content/60">{t('docs.sidebar.postalTerminalApi')}</p>
                </div>
                <button 
                  className="btn btn-ghost btn-sm lg:hidden"
                  onClick={() => setSidebarOpen(false)}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {/* API Key Input in Sidebar */}
              <div className="mt-4">
                <ApiKeyInput value={apiKey} onChange={setApiKey} />
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="flex-1 p-4 overflow-y-auto">
              <ul className="menu menu-lg w-full">
                {navItems.map((item) => (
                  <li key={item.id}>
                    <button
                      className={`flex items-center gap-3 w-full text-left rounded-lg transition-colors ${
                        activeSection === item.id 
                          ? 'bg-primary text-primary-content' 
                          : 'hover:bg-base-300'
                      }`}
                      onClick={() => scrollToSection(item.id)}
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={item.icon} />
                      </svg>
                      {item.label}
                    </button>
                  </li>
                ))}
              </ul>
            </nav>

            {/* Sidebar Footer */}
            <div className="p-4 border-t border-base-300">
              <div className="text-center">
                <div className="stats stats-vertical shadow-sm bg-base-100 w-full">
                  <div className="stat py-2">
                    <div className="stat-title text-xs">{t('docs.sidebar.endpoints')}</div>
                    <div className="stat-value text-lg">{terminalEndpoints.length + trackingEndpoints.length}</div>
                  </div>
                  <div className="stat py-2">
                    <div className="stat-title text-xs">{t('docs.sidebar.rateLimit')}</div>
                    <div className="stat-value text-lg">1k/min</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </aside>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <main className="flex-1 lg:ml-0 min-h-screen min-w-0">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 py-8 min-w-0">

            {/* Getting Started Section */}
            <section id="getting-started" className="mb-16">
              <div className="hero bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl mb-8">
                <div className="hero-content text-center py-12">
                  <div className="max-w-2xl">
                    <div className="badge badge-primary badge-lg mb-4">{t('docs.gettingStarted.title')}</div>
                    <h1 className="text-5xl font-bold text-base-content mb-6">
                      {t('docs.title')}
                    </h1>
                    <p className="text-xl text-base-content/70 mb-8">
                      {t('docs.subtitle')}
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <button 
                        className="btn btn-primary btn-lg"
                        onClick={() => scrollToSection('authentication')}
                      >
                        {t('docs.gettingStarted.startBuilding')}
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </button>
                      <button 
                        className="btn btn-outline btn-lg"
                        onClick={() => scrollToSection('examples')}
                      >
                        {t('docs.gettingStarted.viewExamples')}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="stats stats-vertical sm:stats-horizontal shadow-lg w-full mb-8">
                <div className="stat">
                  <div className="stat-figure text-primary">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div className="stat-title">{t('docs.gettingStarted.totalEndpoints')}</div>
                  <div className="stat-value text-primary">{terminalEndpoints.length + trackingEndpoints.length}</div>
                  <div className="stat-desc">{t('docs.gettingStarted.restfulEndpoints')}</div>
                </div>
                
                <div className="stat">
                  <div className="stat-figure text-secondary">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="stat-title">{t('docs.gettingStarted.rateLimit')}</div>
                  <div className="stat-value text-secondary">1,000</div>
                  <div className="stat-desc">{t('docs.gettingStarted.requestsPerMinute')}</div>
                </div>
                
                <div className="stat">
                  <div className="stat-figure text-accent">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="stat-title">{t('docs.gettingStarted.uptime')}</div>
                  <div className="stat-value text-accent">99.9%</div>
                  <div className="stat-desc">{t('docs.gettingStarted.serviceAvailability')}</div>
                </div>
              </div>

              {/* Quick Start Guide */}
              <div className="card bg-base-200 shadow-xl">
                <div className="card-body">
                  <h3 className="card-title text-2xl mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    {t('docs.gettingStarted.quickStartGuide')}
                  </h3>
                  
                  <div className="steps steps-vertical lg:steps-horizontal w-full mb-6">
                    <div className="step step-primary">{t('docs.gettingStarted.getApiKey')}</div>
                    <div className="step step-primary">{t('docs.gettingStarted.makeRequest')}</div>
                    <div className="step step-primary">{t('docs.gettingStarted.handleResponse')}</div>
                  </div>

                  <div className="mockup-code overflow-x-auto">
                    <pre data-prefix="$"><code>{`curl -X GET "${API_BASE_URL}/api/v1/terminals" \`}</code></pre>
                    <pre data-prefix=">"><code>  -H "Authorization: Bearer YOUR_API_KEY" \</code></pre>
                    <pre data-prefix=">"><code>  -H "Content-Type: application/json"</code></pre>
                  </div>
                </div>
              </div>
            </section>

            {/* Authentication Section */}
            <section id="authentication" className="mb-16">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-primary-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h2 className="text-3xl font-bold text-base-content">
                  {t('docs.authentication')}
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 w-full">
                <div className="card bg-base-200 shadow-lg w-full min-w-0">
                  <div className="card-body p-3 sm:p-4">
                    <h3 className="card-title text-primary text-xs sm:text-base break-words">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      <span className="break-words">{t('docs.authSection.apiKeyRequired')}</span>
                    </h3>
                    <p className="text-xs sm:text-sm text-base-content/70 break-words">
                      {t('docs.authSection.apiKeyDescription')}
                    </p>
                  </div>
                </div>

                <div className="card bg-base-200 shadow-lg w-full">
                  <div className="card-body p-4">
                    <h3 className="card-title text-secondary text-sm sm:text-base break-words">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="break-words">{t('docs.authSection.rateLimits')}</span>
                    </h3>
                    <p className="text-xs sm:text-sm text-base-content/70 break-words">
                      {t('docs.authSection.rateLimitsDescription')}
                    </p>
                  </div>
                </div>

                <div className="card bg-base-200 shadow-lg w-full">
                  <div className="card-body p-4">
                    <h3 className="card-title text-accent text-sm sm:text-base break-words">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z" />
                      </svg>
                      <span className="break-words">{t('docs.authSection.caching')}</span>
                    </h3>
                    <p className="text-xs sm:text-sm text-base-content/70 break-words">
                      {t('docs.authSection.cachingDescription')}
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* Terminal Endpoints */}
            <section id="terminals" className="mb-16">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-primary-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  </svg>
                </div>
                <h2 className="text-3xl font-bold text-base-content">
                  {t('docs.endpoints.terminals')}
                </h2>
              </div>

              <div className="alert alert-info mb-6">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 className="font-bold">{t('docs.terminals.overview')}</h3>
                  <div className="text-sm">{t('docs.terminals.overviewDescription')}</div>
                </div>
              </div>

              <div className="stats stats-vertical sm:stats-horizontal shadow-lg w-full mb-8">
                <div className="stat">
                  <div className="stat-title">{t('docs.terminals.availableEndpoints')}</div>
                  <div className="stat-value text-primary">{terminalEndpoints.length}</div>
                  <div className="stat-desc">{t('docs.terminals.terminalOperations')}</div>
                </div>
                <div className="stat">
                  <div className="stat-title">{t('docs.terminals.responseFormat')}</div>
                  <div className="stat-value text-sm">JSON</div>
                  <div className="stat-desc">{t('docs.terminals.structuredData')}</div>
                </div>
                <div className="stat">
                  <div className="stat-title">{t('docs.terminals.cacheDuration')}</div>
                  <div className="stat-value text-sm">5min</div>
                  <div className="stat-desc">{t('docs.terminals.optimizedPerformance')}</div>
                </div>
              </div>

              <div className="space-y-4 sm:space-y-6 w-full">
                {terminalEndpoints.map(([path, methods]) => 
                  Object.entries(methods as any).map(([method, spec]) => (
                    <EndpointCard
                      key={`${method}-${path}`}
                      method={method.toUpperCase()}
                      path={path}
                      spec={spec as any}
                      apiKey={apiKey}
                    />
                  ))
                )}
              </div>
            </section>

            {/* Tracking Endpoints */}
            <section id="tracking" className="mb-16">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-secondary-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <h2 className="text-3xl font-bold text-base-content">
                  {t('docs.endpoints.tracking')}
                </h2>
              </div>

              <div className="alert alert-warning mb-6">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                <div>
                  <h3 className="font-bold">{t('docs.tracking.features')}</h3>
                  <div className="text-sm">{t('docs.tracking.featuresDescription')}</div>
                </div>
              </div>

              <div className="stats stats-vertical sm:stats-horizontal shadow-lg w-full mb-8">
                <div className="stat">
                  <div className="stat-title">{t('docs.tracking.availableEndpoints')}</div>
                  <div className="stat-value text-secondary">{trackingEndpoints.length}</div>
                  <div className="stat-desc">{t('docs.tracking.trackingOperations')}</div>
                </div>
                <div className="stat">
                  <div className="stat-title">{t('docs.tracking.updateFrequency')}</div>
                  <div className="stat-value text-sm">{t('docs.tracking.realTime')}</div>
                  <div className="stat-desc">{t('docs.tracking.liveTrackingData')}</div>
                </div>
                <div className="stat">
                  <div className="stat-title">{t('docs.tracking.coverage')}</div>
                  <div className="stat-value text-sm">{t('docs.tracking.global')}</div>
                  <div className="stat-desc">{t('docs.tracking.worldwideTracking')}</div>
                </div>
              </div>

              <div className="space-y-4 sm:space-y-6 w-full">
                {trackingEndpoints.map(([path, methods]) => 
                  Object.entries(methods as any).map(([method, spec]) => (
                    <EndpointCard
                      key={`${method}-${path}`}
                      method={method.toUpperCase()}
                      path={path}
                      spec={spec as any}
                      apiKey={apiKey}
                    />
                  ))
                )}
              </div>
            </section>

            {/* Error Responses Section */}
            <section id="errors" className="mb-16">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-error rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-error-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h2 className="text-3xl font-bold text-base-content">
                  {t('docs.navigation.errorHandling')}
                </h2>
              </div>
              
              <div className="card bg-base-200 shadow-lg w-full min-w-0">
                <div className="card-body p-4 sm:p-6">
                  <p className="text-base-content/70 mb-4 text-xs sm:text-sm break-words">
                    {t('docs.errorHandling.description')}
                  </p>
                  
                  <div className="mockup-code text-xs sm:text-sm overflow-x-auto">
                    <pre data-prefix="$"><code>{`{
  "success": false,
  "error": "Error message",
  "message": "Detailed error description",
  "code": "ERROR_CODE"
}`}</code></pre>
                  </div>
                  
                  <div className="mt-4">
                    <h4 className="font-semibold text-base-content mb-2 text-sm sm:text-base break-words">{t('docs.errorHandling.commonStatusCodes')}</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs sm:text-sm">
                      <div className="break-words"><span className="badge badge-success text-xs">200</span> {t('docs.errorHandling.success')}</div>
                      <div className="break-words"><span className="badge badge-error text-xs">400</span> {t('docs.errorHandling.badRequest')}</div>
                      <div className="break-words"><span className="badge badge-error text-xs">401</span> {t('docs.errorHandling.unauthorized')}</div>
                      <div className="break-words"><span className="badge badge-error text-xs">404</span> {t('docs.errorHandling.notFound')}</div>
                      <div className="break-words"><span className="badge badge-warning text-xs">429</span> {t('docs.errorHandling.tooManyRequests')}</div>
                      <div className="break-words"><span className="badge badge-error text-xs">500</span> {t('docs.errorHandling.internalServerError')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Code Examples Section */}
            <section id="examples" className="mb-16">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-accent-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                </div>
                <h2 className="text-3xl font-bold text-base-content">
                  {t('docs.navigation.codeExamples')}
                </h2>
              </div>

              <div className="grid grid-cols-1 xl:grid-cols-2 gap-3 sm:gap-4 w-full">
                <div className="card bg-base-200 shadow-lg w-full min-w-0">
                  <div className="card-body p-4">
                    <h3 className="card-title text-primary mb-4 text-sm sm:text-base break-words">{t('docs.codeExamples.javascript')}</h3>
                    <div className="mockup-code text-xs sm:text-sm overflow-x-auto">
                      <pre data-prefix=">"><code>const response = await fetch(</code></pre>
                      <pre data-prefix=">"><code>{`  '${API_BASE_URL}/api/v1/terminals',`}</code></pre>
                      <pre data-prefix=">"><code>  {`{`}</code></pre>
                      <pre data-prefix=">"><code>    headers: {`{`}</code></pre>
                      <pre data-prefix=">"><code>      'X-API-Key': 'your-api-key'</code></pre>
                      <pre data-prefix=">"><code>    {`}`}</code></pre>
                      <pre data-prefix=">"><code>  {`}`}</code></pre>
                      <pre data-prefix=">"><code>);</code></pre>
                    </div>
                  </div>
                </div>

                <div className="card bg-base-200 shadow-lg w-full min-w-0">
                  <div className="card-body p-4">
                    <h3 className="card-title text-secondary mb-4 text-sm sm:text-base break-words">{t('docs.codeExamples.python')}</h3>
                    <div className="mockup-code text-xs sm:text-sm overflow-x-auto">
                      <pre data-prefix=">"><code>import requests</code></pre>
                      <pre data-prefix=">"><code></code></pre>
                      <pre data-prefix=">"><code>response = requests.get(</code></pre>
                      <pre data-prefix=">"><code>{`  '${API_BASE_URL}/api/v1/terminals',`}</code></pre>
                      <pre data-prefix=">"><code>  headers={`{`}'X-API-Key': 'your-api-key'{`}`}</code></pre>
                      <pre data-prefix=">"><code>)</code></pre>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
         </main>
       </div>
      
      <Footer />
    </div>
  );
}
