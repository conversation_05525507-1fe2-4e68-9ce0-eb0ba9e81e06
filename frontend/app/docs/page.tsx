'use client';

import { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { apiSpec } from '@/lib/api-spec';
import EndpointCard from '@/components/docs/EndpointCard';
import ApiKeyInput from '@/components/docs/ApiKeyInput';

export default function DocsPage() {
  const { t } = useTranslations();
  const [apiKey, setApiKey] = useState('');

  // Group endpoints by tags
  const terminalEndpoints = Object.entries(apiSpec.paths).filter(([path, methods]) => 
    Object.values(methods as any).some((method: any) => method.tags?.includes('Terminals'))
  );

  const trackingEndpoints = Object.entries(apiSpec.paths).filter(([path, methods]) => 
    Object.values(methods as any).some((method: any) => method.tags?.includes('Tracking'))
  );

  return (
    <div className="min-h-screen bg-base-100">
      <Header />
      
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-base-content mb-4">
            {t('docs.title')}
          </h1>
          <p className="text-xl text-base-content/70 max-w-3xl mx-auto mb-8">
            {t('docs.subtitle')}
          </p>
          
          {/* API Key Input */}
          <div className="max-w-md mx-auto">
            <ApiKeyInput value={apiKey} onChange={setApiKey} />
          </div>
        </div>

        {/* API Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-12">
          <div className="card bg-base-200 shadow-lg">
            <div className="card-body">
              <h3 className="card-title text-primary">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                {t('docs.authentication')}
              </h3>
              <p className="text-sm text-base-content/70">
                All endpoints require an API key in the X-API-Key header. Format: ptapi_[64-character-hex-string]
              </p>
            </div>
          </div>

          <div className="card bg-base-200 shadow-lg">
            <div className="card-body">
              <h3 className="card-title text-secondary">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {t('docs.rateLimit')}
              </h3>
              <p className="text-sm text-base-content/70">
                1,000 requests per minute per API key. Rate limit headers included in responses.
              </p>
            </div>
          </div>

          <div className="card bg-base-200 shadow-lg">
            <div className="card-body">
              <h3 className="card-title text-accent">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z" />
                </svg>
                {t('docs.caching')}
              </h3>
              <p className="text-sm text-base-content/70">
                Terminal data cached for 5 minutes, search results for 2 minutes for optimal performance.
              </p>
            </div>
          </div>
        </div>

        {/* Terminal Endpoints */}
        <section className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-primary-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-base-content">
              {t('docs.endpoints.terminals')}
            </h2>
          </div>
          
          <div className="space-y-6">
            {terminalEndpoints.map(([path, methods]) => 
              Object.entries(methods as any).map(([method, spec]) => (
                <EndpointCard
                  key={`${method}-${path}`}
                  method={method.toUpperCase()}
                  path={path}
                  spec={spec as any}
                  apiKey={apiKey}
                />
              ))
            )}
          </div>
        </section>

        {/* Tracking Endpoints */}
        <section className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-secondary-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-base-content">
              {t('docs.endpoints.tracking')}
            </h2>
          </div>
          
          <div className="space-y-6">
            {trackingEndpoints.map(([path, methods]) => 
              Object.entries(methods as any).map(([method, spec]) => (
                <EndpointCard
                  key={`${method}-${path}`}
                  method={method.toUpperCase()}
                  path={path}
                  spec={spec as any}
                  apiKey={apiKey}
                />
              ))
            )}
          </div>
        </section>

        {/* Error Responses Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-base-content mb-6">
            {t('docs.errors')}
          </h2>
          
          <div className="card bg-base-200 shadow-lg">
            <div className="card-body">
              <p className="text-base-content/70 mb-4">
                All endpoints return errors in a consistent format:
              </p>
              
              <div className="mockup-code">
                <pre data-prefix="$"><code>{`{
  "success": false,
  "error": "Error message",
  "message": "Detailed error description",
  "code": "ERROR_CODE"
}`}</code></pre>
              </div>
              
              <div className="mt-4">
                <h4 className="font-semibold text-base-content mb-2">Common HTTP Status Codes:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                  <div><span className="badge badge-success">200</span> Success</div>
                  <div><span className="badge badge-error">400</span> Bad Request</div>
                  <div><span className="badge badge-error">401</span> Unauthorized</div>
                  <div><span className="badge badge-error">404</span> Not Found</div>
                  <div><span className="badge badge-warning">429</span> Too Many Requests</div>
                  <div><span className="badge badge-error">500</span> Internal Server Error</div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
