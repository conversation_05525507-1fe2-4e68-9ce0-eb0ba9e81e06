'use client';

import { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { apiSpec } from '@/lib/api-spec';
import EndpointCard from '@/components/docs/EndpointCard';
import ApiKeyInput from '@/components/docs/ApiKeyInput';

export default function DocsPage() {
  const { t } = useTranslations();
  const [apiKey, setApiKey] = useState('');
  const [activeSection, setActiveSection] = useState('overview');

  // Group endpoints by tags
  const terminalEndpoints = Object.entries(apiSpec.paths).filter(([path, methods]) => 
    Object.values(methods as any).some((method: any) => method.tags?.includes('Terminals'))
  );

  const trackingEndpoints = Object.entries(apiSpec.paths).filter(([path, methods]) => 
    Object.values(methods as any).some((method: any) => method.tags?.includes('Tracking'))
  );

  const navigationItems = [
    { id: 'overview', label: t('docs.nav.overview'), icon: '📋' },
    { id: 'authentication', label: t('docs.nav.authentication'), icon: '🔐' },
    { id: 'terminals', label: t('docs.endpoints.terminals'), icon: '📍' },
    { id: 'tracking', label: t('docs.endpoints.tracking'), icon: '📦' },
    { id: 'errors', label: t('docs.errors'), icon: '⚠️' },
  ];

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <div className="min-h-screen bg-base-100">
      <Header />
      
      <div className="drawer lg:drawer-open">
        <input id="docs-drawer" type="checkbox" className="drawer-toggle" />
        
        {/* Mobile menu button */}
        <div className="drawer-content flex flex-col">
          <div className="navbar bg-base-200 lg:hidden">
            <div className="flex-none">
              <label htmlFor="docs-drawer" className="btn btn-square btn-ghost">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </label>
            </div>
            <div className="flex-1">
              <h1 className="text-xl font-bold">{t('docs.title')}</h1>
            </div>
          </div>
          
          <main className="flex-1 p-4 lg:p-8">
            {/* Hero Section */}
            <div id="overview" className="hero bg-gradient-to-br from-primary/10 to-secondary/10 rounded-box mb-8">
              <div className="hero-content text-center py-12">
                <div className="max-w-4xl">
                  <h1 className="text-5xl font-bold text-base-content mb-6">
                    {t('docs.title')}
                  </h1>
                  <p className="text-xl text-base-content/70 mb-8">
                    {t('docs.subtitle')}
                  </p>
                  
                  {/* Quick stats */}
                  <div className="stats stats-vertical lg:stats-horizontal shadow bg-base-200">
                    <div className="stat">
                      <div className="stat-figure text-primary">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div className="stat-title">Response Time</div>
                      <div className="stat-value text-primary">~200ms</div>
                      <div className="stat-desc">Average API response</div>
                    </div>
                    
                    <div className="stat">
                      <div className="stat-figure text-secondary">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="stat-title">Uptime</div>
                      <div className="stat-value text-secondary">99.9%</div>
                      <div className="stat-desc">Service availability</div>
                    </div>
                    
                    <div className="stat">
                      <div className="stat-figure text-accent">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        </svg>
                      </div>
                      <div className="stat-title">Terminals</div>
                      <div className="stat-value text-accent">10K+</div>
                      <div className="stat-desc">Locations covered</div>
                    </div>
                  </div>
                  
                  {/* API Key Input */}
                  <div className="max-w-md mx-auto mt-8">
                    <ApiKeyInput value={apiKey} onChange={setApiKey} />
                  </div>
                </div>
              </div>
            </div>

            {/* Authentication Section */}
            <section id="authentication" className="mb-12">
              <div className="flex items-center gap-3 mb-6">
                <span className="text-2xl">🔐</span>
                <h2 className="text-3xl font-bold text-base-content">
                  {t('docs.authentication')}
                </h2>
              </div>
              
              <div className="grid gap-6 lg:grid-cols-3">
                {/* Authentication Card */}
                <div className="card bg-base-200 shadow-lg">
                  <div className="card-body">
                    <h3 className="card-title text-primary">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m0 0a2 2 0 01-2 2m2-2a2 2 0 002-2m0 0a2 2 0 00-2-2" />
                      </svg>
                      API Authentication
                    </h3>
                    <p className="text-base-content/70">
                      All endpoints require an API key in the X-API-Key header. Format: ptapi_[64-character-hex-string]
                    </p>
                    <div className="mockup-code mt-4">
                      <pre data-prefix="$"><code>X-API-Key: ptapi_your_key_here</code></pre>
                    </div>
                  </div>
                </div>

                {/* Rate Limits Card */}
                <div className="card bg-base-200 shadow-lg">
                  <div className="card-body">
                    <h3 className="card-title text-secondary">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Rate Limits
                    </h3>
                    <p className="text-base-content/70 mb-4">
                      1,000 requests per minute per API key. Rate limit headers included in responses.
                    </p>
                    <div className="space-y-2">
                      <div className="badge badge-outline">1000/minute</div>
                      <div className="badge badge-outline">Headers included</div>
                    </div>
                  </div>
                </div>

                {/* Caching Card */}
                <div className="card bg-base-200 shadow-lg">
                  <div className="card-body">
                    <h3 className="card-title text-accent">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                      </svg>
                      Caching
                    </h3>
                    <p className="text-base-content/70 mb-4">
                      Terminal data cached for 5 minutes, search results for 2 minutes for optimal performance.
                    </p>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Terminal data:</span>
                        <span className="badge badge-sm">5 minutes</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Search results:</span>
                        <span className="badge badge-sm">2 minutes</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

             {/* Terminal Endpoints */}
             <section id="terminals" className="mb-12">
               <div className="flex items-center gap-3 mb-6">
                 <span className="text-2xl">📍</span>
                 <h2 className="text-3xl font-bold text-base-content">
                   {t('docs.endpoints.terminals')}
                 </h2>
               </div>
               
               <div className="alert alert-info mb-6">
                 <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                 </svg>
                 <span>Terminal endpoints provide access to postal terminal locations, search functionality, and detailed terminal information.</span>
               </div>
               
               <div className="space-y-6">
                 {terminalEndpoints.map(([path, methods]) => 
                   Object.entries(methods as any).map(([method, spec]) => (
                     <EndpointCard
                       key={`${method}-${path}`}
                       method={method.toUpperCase()}
                       path={path}
                       spec={spec as any}
                       apiKey={apiKey}
                     />
                   ))
                 )}
               </div>
             </section>

             {/* Tracking Endpoints */}
             <section id="tracking" className="mb-12">
               <div className="flex items-center gap-3 mb-6">
                 <span className="text-2xl">📦</span>
                 <h2 className="text-3xl font-bold text-base-content">
                   {t('docs.endpoints.tracking')}
                 </h2>
               </div>
               
               <div className="alert alert-success mb-6">
                 <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                 </svg>
                 <span>Tracking endpoints allow you to monitor package delivery status and get real-time updates on shipments.</span>
               </div>
               
               <div className="space-y-6">
                 {trackingEndpoints.map(([path, methods]) => 
                   Object.entries(methods as any).map(([method, spec]) => (
                     <EndpointCard
                       key={`${method}-${path}`}
                       method={method.toUpperCase()}
                       path={path}
                       spec={spec as any}
                       apiKey={apiKey}
                     />
                   ))
                 )}
               </div>
             </section>

             {/* Error Responses Section */}
             <section id="errors" className="mb-12">
               <div className="flex items-center gap-3 mb-6">
                 <span className="text-2xl">⚠️</span>
                 <h2 className="text-3xl font-bold text-base-content">
                   {t('docs.errors')}
                 </h2>
               </div>
               
               <div className="alert alert-warning mb-6">
                 <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                 </svg>
                 <span>All API responses follow a consistent JSON format with detailed error information for debugging.</span>
               </div>
               
               <div className="grid gap-6 lg:grid-cols-2">
                 {/* Success Response */}
                 <div className="card bg-base-200 shadow-lg">
                   <div className="card-body">
                     <h3 className="card-title text-success">
                       <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                       </svg>
                       Success Response
                     </h3>
                     <div className="mockup-code text-xs">
                       <pre data-prefix="{" className="text-success"><code>"success": true,</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>  "data": {</code></pre>
                       <pre data-prefix="" className="text-base-content/60"><code>    // Response data</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>  },</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>  "meta": {</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>    "timestamp": "2024-01-01T00:00:00Z"</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>  }</code></pre>
                       <pre data-prefix="}"><code></code></pre>
                     </div>
                   </div>
                 </div>
                 
                 {/* Error Response */}
                 <div className="card bg-base-200 shadow-lg">
                   <div className="card-body">
                     <h3 className="card-title text-error">
                       <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                       </svg>
                       Error Response
                     </h3>
                     <div className="mockup-code text-xs">
                       <pre data-prefix="{" className="text-error"><code>"success": false,</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>  "error": {</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>    "code": "INVALID_API_KEY",</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>    "message": "Invalid API key"</code></pre>
                       <pre data-prefix="" className="text-base-content"><code>  }</code></pre>
                       <pre data-prefix="}"><code></code></pre>
                     </div>
                   </div>
                 </div>
               </div>
               
               {/* HTTP Status Codes */}
               <div className="mt-8">
                 <h3 className="text-xl font-bold text-base-content mb-4">HTTP Status Codes</h3>
                 <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                   <div className="flex items-center gap-3 p-3 bg-base-200 rounded-lg">
                     <div className="badge badge-success">200</div>
                     <span className="text-sm">OK - Request successful</span>
                   </div>
                   <div className="flex items-center gap-3 p-3 bg-base-200 rounded-lg">
                     <div className="badge badge-warning">400</div>
                     <span className="text-sm">Bad Request - Invalid parameters</span>
                   </div>
                   <div className="flex items-center gap-3 p-3 bg-base-200 rounded-lg">
                     <div className="badge badge-error">401</div>
                     <span className="text-sm">Unauthorized - Invalid API key</span>
                   </div>
                   <div className="flex items-center gap-3 p-3 bg-base-200 rounded-lg">
                     <div className="badge badge-info">404</div>
                     <span className="text-sm">Not Found - Resource not found</span>
                   </div>
                   <div className="flex items-center gap-3 p-3 bg-base-200 rounded-lg">
                     <div className="badge badge-warning">429</div>
                     <span className="text-sm">Rate limit exceeded</span>
                   </div>
                   <div className="flex items-center gap-3 p-3 bg-base-200 rounded-lg">
                     <div className="badge badge-error">500</div>
                     <span className="text-sm">Internal Server Error</span>
                   </div>
                 </div>
               </div>
             </section>
           </main>
         </div>
         
         {/* Sidebar Navigation */}
         <div className="drawer-side">
           <label htmlFor="docs-drawer" className="drawer-overlay"></label>
           <aside className="w-80 min-h-full bg-base-200">
             <div className="p-4">
               <h2 className="text-lg font-bold text-base-content mb-4">Documentation</h2>
               
               {/* Quick Links */}
               <div className="mb-6">
                 <h3 className="text-sm font-semibold text-base-content/70 uppercase tracking-wide mb-2">Quick Start</h3>
                 <div className="space-y-1">
                   <a href="#overview" className="btn btn-ghost btn-sm justify-start w-full">
                     <span className="mr-2">🚀</span>
                     Getting Started
                   </a>
                   <a href="#authentication" className="btn btn-ghost btn-sm justify-start w-full">
                     <span className="mr-2">🔑</span>
                     API Keys
                   </a>
                 </div>
               </div>
               
               {/* Navigation Menu */}
               <div className="menu">
                 <h3 className="text-sm font-semibold text-base-content/70 uppercase tracking-wide mb-2">Navigation</h3>
                 {navigationItems.map((item) => (
                   <li key={item.id}>
                     <button
                       onClick={() => scrollToSection(item.id)}
                       className={`flex items-center gap-3 ${activeSection === item.id ? 'active' : ''}`}
                     >
                       <span>{item.icon}</span>
                       {item.label}
                     </button>
                   </li>
                 ))}
               </div>
               
               {/* Additional Resources */}
               <div className="mt-8">
                 <h3 className="text-sm font-semibold text-base-content/70 uppercase tracking-wide mb-2">Resources</h3>
                 <div className="space-y-1">
                   <a href="/dashboard" className="btn btn-ghost btn-sm justify-start w-full">
                     <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                     </svg>
                     Dashboard
                   </a>
                   <a href="/contact" className="btn btn-ghost btn-sm justify-start w-full">
                     <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                     </svg>
                     Support
                   </a>
                 </div>
               </div>
               
               {/* API Status */}
               <div className="mt-8 p-4 bg-base-300 rounded-lg">
                 <div className="flex items-center gap-2 mb-2">
                   <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                   <span className="text-sm font-medium">API Status</span>
                 </div>
                 <p className="text-xs text-base-content/70">All systems operational</p>
                 <div className="mt-2">
                   <div className="text-xs text-base-content/60">Response time: ~200ms</div>
                   <div className="text-xs text-base-content/60">Uptime: 99.9%</div>
                 </div>
               </div>
             </div>
           </aside>
         </div>
       </div>
       
       <Footer />
     </div>
  );
}