'use client';

import { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { apiSpec } from '@/lib/api-spec';
import EndpointCard from '@/components/docs/EndpointCard';
import ApiKeyInput from '@/components/docs/ApiKeyInput';

export default function DocsPage() {
  const { t } = useTranslations();
  const [apiKey, setApiKey] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  // Group endpoints by tags
  const terminalEndpoints = Object.entries(apiSpec.paths).filter(([path, methods]) => 
    Object.values(methods as any).some((method: any) => method.tags?.includes('Terminals'))
  );

  const trackingEndpoints = Object.entries(apiSpec.paths).filter(([path, methods]) => 
    Object.values(methods as any).some((method: any) => method.tags?.includes('Tracking'))
  );

  return (
    <div className="min-h-screen bg-base-100">
      <Header />
      
      {/* Hero Section */}
      <div className="hero bg-gradient-to-br from-primary/10 to-secondary/10 py-16">
        <div className="hero-content text-center max-w-4xl">
          <div>
            <div className="flex justify-center mb-6">
              <div className="badge badge-primary badge-lg gap-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                API Documentation
              </div>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-base-content mb-6">
              {t('docs.title')}
            </h1>
            <p className="text-xl text-base-content/70 max-w-3xl mx-auto mb-8">
              {t('docs.subtitle')}
            </p>
            
            {/* API Key Input */}
            <div className="max-w-md mx-auto">
              <ApiKeyInput value={apiKey} onChange={setApiKey} />
            </div>
          </div>
        </div>
      </div>
      
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">

        {/* Navigation Tabs */}
        <div className="tabs tabs-boxed bg-base-200 mb-8 justify-center">
          <button 
            className={`tab tab-lg ${activeTab === 'overview' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Overview
          </button>
          <button 
            className={`tab tab-lg ${activeTab === 'terminals' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('terminals')}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            </svg>
            Terminals
          </button>
          <button 
            className={`tab tab-lg ${activeTab === 'tracking' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('tracking')}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            Tracking
          </button>
          <button 
            className={`tab tab-lg ${activeTab === 'errors' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('errors')}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            Errors
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* API Information Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="card bg-base-200 shadow-xl border border-primary/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <h3 className="card-title text-primary">
                      {t('docs.authentication')}
                    </h3>
                  </div>
                  <p className="text-sm text-base-content/70">
                    All endpoints require an API key in the X-API-Key header. Format: ptapi_[64-character-hex-string]
                  </p>
                  <div className="card-actions justify-end mt-4">
                    <div className="badge badge-primary badge-outline">Required</div>
                  </div>
                </div>
              </div>

              <div className="card bg-base-200 shadow-xl border border-secondary/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-secondary/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="card-title text-secondary">
                      {t('docs.rateLimit')}
                    </h3>
                  </div>
                  <p className="text-sm text-base-content/70">
                    1,000 requests per minute per API key. Rate limit headers included in responses.
                  </p>
                  <div className="card-actions justify-end mt-4">
                    <div className="badge badge-secondary badge-outline">1k/min</div>
                  </div>
                </div>
              </div>

              <div className="card bg-base-200 shadow-xl border border-accent/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-accent/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z" />
                      </svg>
                    </div>
                    <h3 className="card-title text-accent">
                      {t('docs.caching')}
                    </h3>
                  </div>
                  <p className="text-sm text-base-content/70">
                    Terminal data cached for 5 minutes, search results for 2 minutes for optimal performance.
                  </p>
                  <div className="card-actions justify-end mt-4">
                    <div className="badge badge-accent badge-outline">5min</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Start Guide */}
            <div className="card bg-base-200 shadow-xl">
              <div className="card-body">
                <h2 className="card-title text-2xl mb-6">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Quick Start
                </h2>
                
                <div className="steps steps-vertical lg:steps-horizontal w-full">
                  <div className="step step-primary">
                    <div className="text-left">
                      <div className="font-semibold">Get API Key</div>
                      <div className="text-sm text-base-content/70">Register and generate your API key</div>
                    </div>
                  </div>
                  <div className="step step-primary">
                    <div className="text-left">
                      <div className="font-semibold">Add Header</div>
                      <div className="text-sm text-base-content/70">Include X-API-Key in requests</div>
                    </div>
                  </div>
                  <div className="step">
                    <div className="text-left">
                      <div className="font-semibold">Make Requests</div>
                      <div className="text-sm text-base-content/70">Start using the API endpoints</div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <div className="mockup-code">
                    <pre data-prefix="$"><code>curl -H "X-API-Key: ptapi_your_key_here" \</code></pre>
                    <pre data-prefix=">"><code>     https://api.postal-terminal.com/v1/terminals</code></pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Terminals Tab */}
        {activeTab === 'terminals' && terminalEndpoints.length > 0 && (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4">
                <svg className="w-8 h-8 inline mr-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                </svg>
                Terminal Endpoints
              </h2>
              <p className="text-base-content/70 max-w-2xl mx-auto">
                Access comprehensive postal terminal data including locations, services, and operational details.
              </p>
            </div>
            
            <div className="stats shadow w-full mb-6">
              <div className="stat">
                <div className="stat-figure text-primary">
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
                  </svg>
                </div>
                <div className="stat-title">Total Endpoints</div>
                <div className="stat-value text-primary">{terminalEndpoints.length}</div>
                <div className="stat-desc">Available terminal operations</div>
              </div>
              
              <div className="stat">
                <div className="stat-figure text-secondary">
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <div className="stat-title">Search Methods</div>
                <div className="stat-value text-secondary">3</div>
                <div className="stat-desc">By location, postal code, name</div>
              </div>
              
              <div className="stat">
                <div className="stat-figure text-accent">
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className="stat-title">Response Time</div>
                <div className="stat-value text-accent">~200ms</div>
                <div className="stat-desc">Average API response</div>
              </div>
            </div>
            
            {terminalEndpoints.map(([path, methods]) => 
              Object.entries(methods as any).map(([method, spec]) => (
                <EndpointCard
                  key={`${method}-${path}`}
                  method={method.toUpperCase()}
                  path={path}
                  spec={spec as any}
                  apiKey={apiKey}
                />
              ))
            )}
          </div>
        )}

        {/* Tracking Tab */}
        {activeTab === 'tracking' && trackingEndpoints.length > 0 && (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4">
                <svg className="w-8 h-8 inline mr-3 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                Tracking Endpoints
              </h2>
              <p className="text-base-content/70 max-w-2xl mx-auto">
                Track packages and shipments across multiple postal providers with real-time status updates.
              </p>
            </div>
            
            <div className="alert alert-info mb-6">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 className="font-bold">Tracking Features</h3>
                <div className="text-sm">Real-time updates, multi-provider support, delivery notifications, and historical tracking data.</div>
              </div>
            </div>
            
            {trackingEndpoints.map(([path, methods]) => 
              Object.entries(methods as any).map(([method, spec]) => (
                <EndpointCard
                  key={`${method}-${path}`}
                  method={method.toUpperCase()}
                  path={path}
                  spec={spec as any}
                  apiKey={apiKey}
                />
              ))
            )}
          </div>
        )}

        {/* Errors Tab */}
        {activeTab === 'errors' && (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4">
                <svg className="w-8 h-8 inline mr-3 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                {t('docs.errorResponses')}
              </h2>
              <p className="text-base-content/70 max-w-2xl mx-auto">
                Understanding API error responses and how to handle them in your applications.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="card bg-base-200 shadow-xl border border-error/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-error/20 rounded-lg flex items-center justify-center">
                      <span className="text-error font-bold text-sm">400</span>
                    </div>
                    <h3 className="card-title text-error">Bad Request</h3>
                  </div>
                  <p className="text-sm text-base-content/70 mb-4">
                    Invalid request parameters or malformed data.
                  </p>
                  <div className="mockup-code text-xs">
                    <pre><code>{JSON.stringify({
                      error: "Bad Request",
                      message: "Invalid postal code format",
                      code: 400
                    }, null, 2)}</code></pre>
                  </div>
                </div>
              </div>

              <div className="card bg-base-200 shadow-xl border border-warning/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-warning/20 rounded-lg flex items-center justify-center">
                      <span className="text-warning font-bold text-sm">401</span>
                    </div>
                    <h3 className="card-title text-warning">Unauthorized</h3>
                  </div>
                  <p className="text-sm text-base-content/70 mb-4">
                    Missing or invalid API key.
                  </p>
                  <div className="mockup-code text-xs">
                    <pre><code>{JSON.stringify({
                      error: "Unauthorized",
                      message: "Invalid API key",
                      code: 401
                    }, null, 2)}</code></pre>
                  </div>
                </div>
              </div>

              <div className="card bg-base-200 shadow-xl border border-info/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-info/20 rounded-lg flex items-center justify-center">
                      <span className="text-info font-bold text-sm">404</span>
                    </div>
                    <h3 className="card-title text-info">Not Found</h3>
                  </div>
                  <p className="text-sm text-base-content/70 mb-4">
                    Requested resource not found.
                  </p>
                  <div className="mockup-code text-xs">
                    <pre><code>{JSON.stringify({
                      error: "Not Found",
                      message: "Terminal not found",
                      code: 404
                    }, null, 2)}</code></pre>
                  </div>
                </div>
              </div>

              <div className="card bg-base-200 shadow-xl border border-error/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-error/20 rounded-lg flex items-center justify-center">
                      <span className="text-error font-bold text-sm">429</span>
                    </div>
                    <h3 className="card-title text-error">Too Many Requests</h3>
                  </div>
                  <p className="text-sm text-base-content/70 mb-4">
                    Rate limit exceeded.
                  </p>
                  <div className="mockup-code text-xs">
                    <pre><code>{JSON.stringify({
                      error: "Too Many Requests",
                      message: "Rate limit exceeded",
                      code: 429,
                      retryAfter: 60
                    }, null, 2)}</code></pre>
                  </div>
                </div>
              </div>
              
              <div className="card bg-base-200 shadow-xl border border-neutral/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-neutral/20 rounded-lg flex items-center justify-center">
                      <span className="text-neutral font-bold text-sm">500</span>
                    </div>
                    <h3 className="card-title text-neutral">Internal Server Error</h3>
                  </div>
                  <p className="text-sm text-base-content/70 mb-4">
                    Unexpected server error occurred.
                  </p>
                  <div className="mockup-code text-xs">
                    <pre><code>{JSON.stringify({
                      error: "Internal Server Error",
                      message: "An unexpected error occurred",
                      code: 500
                    }, null, 2)}</code></pre>
                  </div>
                </div>
              </div>
              
              <div className="card bg-base-200 shadow-xl border border-neutral/20">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-neutral/20 rounded-lg flex items-center justify-center">
                      <span className="text-neutral font-bold text-sm">503</span>
                    </div>
                    <h3 className="card-title text-neutral">Service Unavailable</h3>
                  </div>
                  <p className="text-sm text-base-content/70 mb-4">
                    Service temporarily unavailable.
                  </p>
                  <div className="mockup-code text-xs">
                    <pre><code>{JSON.stringify({
                      error: "Service Unavailable",
                      message: "Service temporarily unavailable",
                      code: 503
                    }, null, 2)}</code></pre>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Error Handling Best Practices */}
            <div className="card bg-base-200 shadow-xl mt-8">
              <div className="card-body">
                <h3 className="card-title text-2xl mb-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  Error Handling Best Practices
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 text-primary">Retry Logic</h4>
                    <ul className="list-disc list-inside text-sm text-base-content/70 space-y-1">
                      <li>Implement exponential backoff for 5xx errors</li>
                      <li>Retry up to 3 times for transient failures</li>
                      <li>Don't retry 4xx errors (client errors)</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2 text-secondary">Rate Limiting</h4>
                    <ul className="list-disc list-inside text-sm text-base-content/70 space-y-1">
                      <li>Check X-RateLimit-* headers</li>
                      <li>Respect Retry-After header for 429 errors</li>
                      <li>Implement client-side rate limiting</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2 text-accent">Logging</h4>
                    <ul className="list-disc list-inside text-sm text-base-content/70 space-y-1">
                      <li>Log all API errors with request context</li>
                      <li>Include correlation IDs when available</li>
                      <li>Monitor error rates and patterns</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2 text-info">User Experience</h4>
                    <ul className="list-disc list-inside text-sm text-base-content/70 space-y-1">
                      <li>Provide meaningful error messages</li>
                      <li>Implement graceful degradation</li>
                      <li>Show loading states during retries</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
