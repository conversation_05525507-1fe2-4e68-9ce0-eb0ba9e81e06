'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from '@/lib/translations';
import { apiClient, TerminalSearchResponse, NearbyTerminalsResponse, PostalTerminal, TerminalSearchResult, NearbyTerminalResult, TerminalListResponse } from '@/lib/api';
import TerminalSearchWidget from '@/components/search/TerminalSearchWidget';
import TerminalCard from '@/components/search/TerminalCard';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import Modal from '@/components/ui/Modal';
import { MapPin, Clock, Building, Package, AlertTriangle, Loader2, X, ExternalLink } from 'lucide-react';

type SearchResult = TerminalSearchResult | NearbyTerminalResult;

interface SearchState {
  results: SearchResult[];
  loading: boolean;
  error: string | null;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  meta?: {
    requestId: string;
    responseTime: number;
    searchQuery?: string;
    searchCenter?: { lat: number; lng: number };
    searchRadius?: number;
    cacheHit: boolean;
  };
}

export default function SearchPage() {
  const { t } = useTranslations();
  const searchParams = useSearchParams();
  const [searchState, setSearchState] = useState<SearchState>({
    results: [],
    loading: false,
    error: null
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('All Providers');
  const [selectedTerminal, setSelectedTerminal] = useState<SearchResult | null>(null);
  const [showTerminalModal, setShowTerminalModal] = useState(false);

  // Debounced search function
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const performSearch = useCallback(async (
    query?: string,
    provider?: string,
    lat?: number,
    lng?: number,
    page: number = 1
  ) => {
    setSearchState(prev => ({ ...prev, loading: true, error: null }));

    try {
      let response: TerminalSearchResponse | NearbyTerminalsResponse;

      if (lat && lng) {
        // Nearby search
        response = await apiClient.getNearbyTerminals({
          lat,
          lng,
          radius: 10,
          limit: 20
        });
      } else if (query) {
        // Text search
        const searchParams: any = {
          q: query,
          page,
          limit: 20
        };

        if (provider && provider !== 'All Providers') {
          // Map provider names to API values
          const providerMap: { [key: string]: string } = {
            'LP Express': 'LP_EXPRESS',
            'Omniva': 'OMNIVA',
            'DPD': 'DPD',
            'Venipak': 'VENIPAK'
          };
          searchParams.provider = providerMap[provider] || provider;
        }

        response = await apiClient.searchTerminals(searchParams);
      } else {
        // General terminal listing
        const listResponse: TerminalListResponse = await apiClient.getTerminals({
          page,
          limit: 20,
          ...(provider && provider !== 'All Providers' && { provider })
        });

        // Convert to search response format
        response = {
          data: listResponse.data,
          pagination: listResponse.pagination,
          meta: listResponse.meta
        };
      }

      setSearchState({
        results: response.data,
        loading: false,
        error: null,
        pagination: 'pagination' in response ? response.pagination : undefined,
        meta: response.meta
      });
    } catch (error: any) {
      console.error('Search error:', error);
      setSearchState(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to search terminals'
      }));
    }
  }, []);

  // Handle search with debouncing
  const handleSearch = useCallback((query: string, provider: string) => {
    setSearchQuery(query);
    setSelectedProvider(provider);
    setCurrentPage(1);

    // Update URL with search parameters
    const searchParams = new URLSearchParams();
    if (query) searchParams.append('q', query);
    if (provider && provider !== 'All Providers') searchParams.append('provider', provider);

    const newUrl = `/search${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    window.history.pushState({}, '', newUrl);

    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout for debounced search
    debounceTimeoutRef.current = setTimeout(() => {
      performSearch(query, provider, undefined, undefined, 1);
    }, 300);
  }, [performSearch]);

  const handleNearbySearch = useCallback((lat: number, lng: number) => {
    setSearchQuery('');
    setSelectedProvider('All Providers');
    setCurrentPage(1);

    // Update URL with location parameters
    const searchParams = new URLSearchParams();
    searchParams.append('lat', lat.toString());
    searchParams.append('lng', lng.toString());
    searchParams.append('radius', '10');

    const newUrl = `/search?${searchParams.toString()}`;
    window.history.pushState({}, '', newUrl);

    performSearch(undefined, undefined, lat, lng, 1);
  }, [performSearch]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    performSearch(searchQuery, selectedProvider, undefined, undefined, page);
  };

  // Initialize search from URL parameters
  useEffect(() => {
    const query = searchParams.get('q');
    const provider = searchParams.get('provider');
    const lat = searchParams.get('lat');
    const lng = searchParams.get('lng');

    if (lat && lng) {
      handleNearbySearch(parseFloat(lat), parseFloat(lng));
    } else if (query) {
      setSearchQuery(query);
      if (provider) setSelectedProvider(provider);
      performSearch(query, provider || 'All Providers');
    } else {
      // Load initial results
      performSearch();
    }
  }, [searchParams, handleNearbySearch, performSearch]);

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const getTerminalTypeIcon = (type: string) => {
    switch (type) {
      case 'PARCEL_LOCKER':
        return <Package className="w-4 h-4" />;
      case 'POST_OFFICE':
        return <Building className="w-4 h-4" />;
      default:
        return <MapPin className="w-4 h-4" />;
    }
  };

  const formatDistance = (distance?: number) => {
    if (!distance) return '';
    return distance < 1 ? `${Math.round(distance * 1000)}m` : `${distance.toFixed(1)}km`;
  };

  const handleViewDetails = (terminal: SearchResult) => {
    setSelectedTerminal(terminal);
    setShowTerminalModal(true);
  };

  const formatTerminalType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="min-h-screen bg-base-100">
      <Header />
      
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Widget */}
        <div className="mb-8">
          <TerminalSearchWidget
            onSearch={handleSearch}
            onNearbySearch={handleNearbySearch}
            size="default"
            initialQuery={searchQuery}
            initialProvider={selectedProvider}
            disabled={searchState.loading}
          />
        </div>

        {/* Search Results */}
        <div className="space-y-6">
          {/* Results Header */}
          {(searchState.results.length > 0 || searchState.loading || searchState.error) && (
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">
                  {searchState.meta?.searchQuery ? `Search Results for "${searchState.meta.searchQuery}"` : 
                   searchState.meta?.searchCenter ? 'Nearby Terminals' : 'All Terminals'}
                </h1>
                {searchState.pagination && (
                  <p className="text-base-content/70 mt-1">
                    {searchState.pagination.total.toLocaleString()} terminals found
                    {searchState.meta?.responseTime && ` (${searchState.meta.responseTime}ms)`}
                  </p>
                )}
              </div>
              
              {searchState.meta?.cacheHit && (
                <div className="badge badge-info gap-2">
                  <Clock className="w-3 h-3" />
                  Cached
                </div>
              )}
            </div>
          )}

          {/* Loading State */}
          {searchState.loading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
                <p className="text-base-content/70">Searching terminals...</p>
              </div>
            </div>
          )}

          {/* Error State */}
          {searchState.error && (
            <div className="alert alert-error">
              <AlertTriangle className="w-5 h-5" />
              <span>{searchState.error}</span>
              <button 
                className="btn btn-sm btn-ghost"
                onClick={() => setSearchState(prev => ({ ...prev, error: null }))}
              >
                Dismiss
              </button>
            </div>
          )}

          {/* Results Grid */}
          {!searchState.loading && !searchState.error && searchState.results.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {searchState.results.map((terminal) => (
                <TerminalCard
                  key={terminal.id}
                  terminal={terminal}
                  onViewDetails={handleViewDetails}
                />
              ))}
            </div>
          )}

          {/* Empty State */}
          {!searchState.loading && !searchState.error && searchState.results.length === 0 && (
            <div className="text-center py-12">
              <Package className="w-16 h-16 mx-auto text-base-content/30 mb-4" />
              <h3 className="text-xl font-semibold mb-2">No terminals found</h3>
              <p className="text-base-content/70 mb-6">
                Try adjusting your search criteria or search in a different area.
              </p>
              <button 
                className="btn btn-primary"
                onClick={() => performSearch()}
              >
                Show All Terminals
              </button>
            </div>
          )}

          {/* Pagination */}
          {searchState.pagination && searchState.pagination.totalPages > 1 && (
            <div className="flex justify-center">
              <div className="join">
                <button 
                  className="join-item btn"
                  disabled={currentPage === 1 || searchState.loading}
                  onClick={() => handlePageChange(currentPage - 1)}
                >
                  «
                </button>
                
                {Array.from({ length: Math.min(5, searchState.pagination.totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <button
                      key={page}
                      className={`join-item btn ${currentPage === page ? 'btn-active' : ''}`}
                      disabled={searchState.loading}
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </button>
                  );
                })}
                
                <button 
                  className="join-item btn"
                  disabled={currentPage === searchState.pagination.totalPages || searchState.loading}
                  onClick={() => handlePageChange(currentPage + 1)}
                >
                  »
                </button>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Terminal Detail Modal */}
      <Modal
        isOpen={showTerminalModal}
        onClose={() => {
          setShowTerminalModal(false);
          setSelectedTerminal(null);
        }}
        title="Terminal Details"
        size="lg"
      >
        {selectedTerminal && (
          <div className="space-y-6">
            {/* Terminal Header */}
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-xl font-bold">{selectedTerminal.name}</h3>
                <p className="text-base-content/70">{selectedTerminal.address}, {selectedTerminal.city}</p>
              </div>
              <div className="flex items-center gap-2">
                {getTerminalTypeIcon(selectedTerminal.terminalType)}
                <span className="badge badge-primary">{formatTerminalType(selectedTerminal.terminalType)}</span>
              </div>
            </div>

            {/* Terminal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-base-content/70">Provider</label>
                  <p className="font-medium">{selectedTerminal.provider}</p>
                </div>

                {selectedTerminal.postalCode && (
                  <div>
                    <label className="text-sm font-medium text-base-content/70">Postal Code</label>
                    <p className="font-medium">{selectedTerminal.postalCode}</p>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-base-content/70">Country</label>
                  <p className="font-medium">{selectedTerminal.countryCode}</p>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-base-content/70">Coordinates</label>
                  <p className="font-medium">{selectedTerminal.latitude.toFixed(6)}, {selectedTerminal.longitude.toFixed(6)}</p>
                </div>

                {'distance' in selectedTerminal && selectedTerminal.distance && (
                  <div>
                    <label className="text-sm font-medium text-base-content/70">Distance</label>
                    <p className="font-medium text-primary">{formatDistance(selectedTerminal.distance)}</p>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-base-content/70">Last Updated</label>
                  <p className="font-medium">{new Date(selectedTerminal.updated).toLocaleDateString()}</p>
                </div>
              </div>
            </div>

            {/* Search Relevance (for search results) */}
            {'relevanceScore' in selectedTerminal && selectedTerminal.relevanceScore && (
              <div className="bg-base-200 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Search Relevance</h4>
                <div className="flex items-center gap-4">
                  <div>
                    <span className="text-sm text-base-content/70">Score: </span>
                    <span className="font-medium">{(selectedTerminal.relevanceScore * 100).toFixed(1)}%</span>
                  </div>
                  {selectedTerminal.matchedFields && selectedTerminal.matchedFields.length > 0 && (
                    <div>
                      <span className="text-sm text-base-content/70">Matched: </span>
                      <span className="font-medium">{selectedTerminal.matchedFields.join(', ')}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-3 pt-4 border-t border-base-300">
              <button
                className="btn btn-primary flex-1"
                onClick={() => {
                  const url = `https://www.google.com/maps?q=${selectedTerminal.latitude},${selectedTerminal.longitude}`;
                  window.open(url, '_blank');
                }}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Open in Maps
              </button>
              <button
                className="btn btn-outline"
                onClick={() => {
                  const coords = `${selectedTerminal.latitude},${selectedTerminal.longitude}`;
                  navigator.clipboard.writeText(coords);
                }}
              >
                Copy Coordinates
              </button>
            </div>
          </div>
        )}
      </Modal>

      <Footer />
    </div>
  );
}
