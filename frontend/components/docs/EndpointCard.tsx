'use client';

import { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import ParameterInput from './ParameterInput';
import ResponseDisplay from './ResponseDisplay';
import CopyButton from './CopyButton';

interface EndpointCardProps {
  method: string;
  path: string;
  spec: any;
  apiKey: string;
}

export default function EndpointCard({ method, path, spec, apiKey }: EndpointCardProps) {
  const { t } = useTranslations();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [response, setResponse] = useState<any>(null);
  const [responseTime, setResponseTime] = useState<number | null>(null);

  const methodColors = {
    GET: 'badge-success',
    POST: 'badge-primary',
    PUT: 'badge-warning',
    DELETE: 'badge-error',
    PATCH: 'badge-info'
  };

  const handleParameterChange = (name: string, value: any) => {
    setParameters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const buildRequestUrl = () => {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    let url = `${baseUrl}${path}`;
    
    // Replace path parameters
    const pathParams = spec.parameters?.filter((p: any) => p.in === 'path') || [];
    pathParams.forEach((param: any) => {
      const value = parameters[param.name];
      if (value) {
        url = url.replace(`{${param.name}}`, encodeURIComponent(value));
      }
    });

    // Add query parameters
    const queryParams = spec.parameters?.filter((p: any) => p.in === 'query') || [];
    const queryString = queryParams
      .filter((param: any) => parameters[param.name] !== undefined && parameters[param.name] !== '')
      .map((param: any) => `${param.name}=${encodeURIComponent(parameters[param.name])}`)
      .join('&');

    if (queryString) {
      url += `?${queryString}`;
    }

    return url;
  };

  const buildCurlCommand = () => {
    const url = buildRequestUrl();
    let curl = `curl -X ${method}`;
    
    if (apiKey) {
      curl += ` -H "X-API-Key: ${apiKey}"`;
    }
    
    curl += ` "${url}"`;
    return curl;
  };

  const executeRequest = async () => {
    if (!apiKey) {
      setResponse({
        error: t('docs.apiKey.required'),
        status: 0
      });
      return;
    }

    setIsLoading(true);
    setResponse(null);
    setResponseTime(null);

    const startTime = Date.now();

    try {
      const url = buildRequestUrl();
      const headers: Record<string, string> = {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      };

      const requestOptions: RequestInit = {
        method,
        headers
      };

      const fetchResponse = await fetch(url, requestOptions);
      const endTime = Date.now();
      setResponseTime(endTime - startTime);

      const responseData = await fetchResponse.json();
      
      setResponse({
        status: fetchResponse.status,
        statusText: fetchResponse.statusText,
        headers: Object.fromEntries(fetchResponse.headers.entries()),
        data: responseData
      });
    } catch (error) {
      const endTime = Date.now();
      setResponseTime(endTime - startTime);
      
      setResponse({
        status: 0,
        error: error instanceof Error ? error.message : t('docs.error'),
        data: null
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTesting = () => {
    setIsTesting(!isTesting);
    if (!isTesting) {
      setResponse(null);
      setResponseTime(null);
    }
  };

  return (
    <div className="card bg-base-200 shadow-lg border border-base-300 w-full min-w-0">
      <div className="card-body p-4 sm:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 min-w-0">
            <span className={`badge ${methodColors[method as keyof typeof methodColors]} font-mono font-bold text-xs flex-shrink-0`}>
              {method}
            </span>
            <code className="text-xs sm:text-sm font-mono bg-base-300 px-2 py-1 rounded break-all">
              {path}
            </code>
          </div>
          
          <div className="flex items-center gap-2 flex-shrink-0">
            <CopyButton text={buildRequestUrl()} label={t('docs.url')} />
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="btn btn-ghost btn-sm"
            >
              {isExpanded ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Summary */}
        <div className="min-w-0">
          <h3 className="text-sm sm:text-lg font-semibold text-base-content mb-2 break-words">
            {spec.summary}
          </h3>
          <p className="text-base-content/70 text-xs sm:text-sm break-words">
            {spec.description}
          </p>
        </div>

        {/* Expanded Content */}
        {isExpanded && (
          <div className="mt-6 space-y-6">
            {/* Parameters */}
            {spec.parameters && spec.parameters.length > 0 && (
              <div className="min-w-0">
                <h4 className="text-sm sm:text-md font-semibold text-base-content mb-3">
                  {t('docs.parameters')}
                </h4>
                <div className="space-y-3 min-w-0">
                  {spec.parameters.map((param: any) => (
                    <ParameterInput
                      key={param.name}
                      parameter={param}
                      value={parameters[param.name]}
                      onChange={(value) => handleParameterChange(param.name, value)}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Try It Out Section */}
            <div className="border-t border-base-300 pt-6 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
                <h4 className="text-sm sm:text-md font-semibold text-base-content">
                  {t('docs.tryItOut')}
                </h4>
                <div className="flex gap-2 flex-shrink-0">
                  <CopyButton text={buildCurlCommand()} label={t('docs.curl')} />
                  <button
                    onClick={toggleTesting}
                    className={`btn btn-sm ${isTesting ? 'btn-outline' : 'btn-primary'}`}
                  >
                    {isTesting ? t('docs.cancel') : t('docs.tryItOut')}
                  </button>
                </div>
              </div>

              {isTesting && (
                <div className="space-y-4 min-w-0">
                  <button
                    onClick={executeRequest}
                    disabled={isLoading || !apiKey}
                    className="btn btn-primary btn-sm"
                  >
                    {isLoading ? (
                      <>
                        <span className="loading loading-spinner loading-sm"></span>
                        {t('docs.loading')}
                      </>
                    ) : (
                      t('docs.execute')
                    )}
                  </button>

                  {response && (
                    <ResponseDisplay 
                      response={response} 
                      responseTime={responseTime}
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
