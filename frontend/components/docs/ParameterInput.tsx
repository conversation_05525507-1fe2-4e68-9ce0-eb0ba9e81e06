'use client';

import { useTranslations } from '@/lib/translations';

interface ParameterInputProps {
  parameter: any;
  value: any;
  onChange: (value: any) => void;
}

export default function ParameterInput({ parameter, value, onChange }: ParameterInputProps) {
  const { t } = useTranslations();

  const getInputType = () => {
    switch (parameter.schema?.type) {
      case 'integer':
      case 'number':
        return 'number';
      case 'boolean':
        return 'checkbox';
      default:
        return 'text';
    }
  };

  const renderInput = () => {
    const inputType = getInputType();
    const isRequired = parameter.required;

    if (parameter.schema?.enum) {
      // Enum/Select input
      return (
        <select
          className="select select-bordered select-sm w-full"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
        >
          <option value="">Select {parameter.name}</option>
          {parameter.schema.enum.map((option: string) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      );
    }

    if (inputType === 'checkbox') {
      // Boolean input
      return (
        <input
          type="checkbox"
          className="checkbox checkbox-primary"
          checked={value || false}
          onChange={(e) => onChange(e.target.checked)}
        />
      );
    }

    // Text/Number input
    return (
      <input
        type={inputType}
        className="input input-bordered input-sm w-full"
        placeholder={parameter.schema?.default ? `Default: ${parameter.schema.default}` : `Enter ${parameter.name}`}
        value={value || ''}
        onChange={(e) => {
          const newValue = inputType === 'number' ? 
            (e.target.value ? Number(e.target.value) : '') : 
            e.target.value;
          onChange(newValue);
        }}
        min={parameter.schema?.minimum}
        max={parameter.schema?.maximum}
        step={parameter.schema?.type === 'number' ? 'any' : undefined}
      />
    );
  };

  const getParameterLocation = () => {
    switch (parameter.in) {
      case 'path':
        return { label: t('docs.path'), class: 'badge-error' };
      case 'query':
        return { label: t('docs.query'), class: 'badge-info' };
      case 'header':
        return { label: t('docs.header'), class: 'badge-warning' };
      default:
        return { label: parameter.in, class: 'badge-neutral' };
    }
  };

  const location = getParameterLocation();

  return (
    <div className="form-control">
      <div className="flex items-center gap-2 mb-2">
        <span className="font-mono text-sm font-semibold text-base-content">
          {parameter.name}
        </span>
        
        <span className={`badge badge-sm ${location.class}`}>
          {location.label}
        </span>
        
        {parameter.required && (
          <span className="badge badge-sm badge-error">
            {t('docs.required')}
          </span>
        )}
        
        {parameter.schema?.type && (
          <span className="badge badge-sm badge-outline">
            {parameter.schema.type}
          </span>
        )}
      </div>
      
      <div className="flex gap-2 items-start">
        <div className="flex-1">
          {renderInput()}
        </div>
      </div>
      
      {parameter.description && (
        <label className="label">
          <span className="label-text-alt text-base-content/60">
            {parameter.description}
          </span>
        </label>
      )}
      
      {parameter.schema?.minimum !== undefined && parameter.schema?.maximum !== undefined && (
        <label className="label">
          <span className="label-text-alt text-base-content/50">
            Range: {parameter.schema.minimum} - {parameter.schema.maximum}
          </span>
        </label>
      )}
      
      {parameter.schema?.default !== undefined && (
        <label className="label">
          <span className="label-text-alt text-base-content/50">
            Default: {parameter.schema.default}
          </span>
        </label>
      )}
    </div>
  );
}
