'use client';

import { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import CopyButton from './CopyButton';

interface ResponseDisplayProps {
  response: any;
  responseTime: number | null;
}

export default function ResponseDisplay({ response, responseTime }: ResponseDisplayProps) {
  const { t } = useTranslations();
  const [activeTab, setActiveTab] = useState<'body' | 'headers'>('body');

  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'badge-success';
    if (status >= 400 && status < 500) return 'badge-warning';
    if (status >= 500) return 'badge-error';
    return 'badge-neutral';
  };

  const formatJson = (obj: any) => {
    try {
      return JSON.stringify(obj, null, 2);
    } catch {
      return String(obj);
    }
  };

  return (
    <div className="card bg-base-300 border border-base-content/20">
      <div className="card-body p-4">
        {/* Response Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <h5 className="font-semibold text-base-content">
              {t('docs.response')}
            </h5>
            
            {response.status > 0 && (
              <span className={`badge ${getStatusColor(response.status)} font-mono`}>
                {response.status} {response.statusText}
              </span>
            )}
            
            {responseTime !== null && (
              <span className="badge badge-outline font-mono">
                {responseTime}ms
              </span>
            )}
          </div>
          
          <div className="flex gap-2">
            {response.data && (
              <CopyButton 
                text={formatJson(response.data)} 
                label={t('docs.copy')}
                size="sm"
              />
            )}
          </div>
        </div>

        {/* Error Display */}
        {response.error && (
          <div className="alert alert-error mb-4">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{response.error}</span>
          </div>
        )}

        {/* Tabs */}
        {response.status > 0 && (
          <div className="tabs tabs-bordered mb-4">
            <button
              className={`tab ${activeTab === 'body' ? 'tab-active' : ''}`}
              onClick={() => setActiveTab('body')}
            >
              {t('docs.responseBody')}
            </button>
            <button
              className={`tab ${activeTab === 'headers' ? 'tab-active' : ''}`}
              onClick={() => setActiveTab('headers')}
            >
              {t('docs.responseHeaders')}
            </button>
          </div>
        )}

        {/* Response Content */}
        {response.status > 0 && (
          <div className="mockup-code text-sm max-h-96 overflow-auto">
            {activeTab === 'body' && (
              <pre className="px-4 py-2">
                <code>
                  {response.data ? formatJson(response.data) : t('docs.noResponse')}
                </code>
              </pre>
            )}
            
            {activeTab === 'headers' && response.headers && (
              <pre className="px-4 py-2">
                <code>
                  {formatJson(response.headers)}
                </code>
              </pre>
            )}
          </div>
        )}

        {/* Rate Limit Info */}
        {response.headers && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-2 text-xs">
            {response.headers['x-ratelimit-limit'] && (
              <div className="stat bg-base-200 rounded p-2">
                <div className="stat-title text-xs">Rate Limit</div>
                <div className="stat-value text-sm">{response.headers['x-ratelimit-limit']}</div>
              </div>
            )}
            
            {response.headers['x-ratelimit-remaining'] && (
              <div className="stat bg-base-200 rounded p-2">
                <div className="stat-title text-xs">Remaining</div>
                <div className="stat-value text-sm">{response.headers['x-ratelimit-remaining']}</div>
              </div>
            )}
            
            {response.headers['cache-control'] && (
              <div className="stat bg-base-200 rounded p-2">
                <div className="stat-title text-xs">Cache</div>
                <div className="stat-value text-sm">{response.headers['cache-control']}</div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
