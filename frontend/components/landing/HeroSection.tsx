'use client';

import Link from 'next/link';
import { useState, useRef, useEffect } from 'react';
import { useTranslations } from '@/lib/translations';
import { useToast } from '@/components/ui/Toast';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export default function HeroSection() {
  const { t } = useTranslations();
  const { showToast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('All Providers');
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearch = () => {
    // Handle search functionality
    console.log('Searching for:', searchQuery, 'Provider:', selectedProvider);
  };

  const handleNearbySearch = () => {
    if (!navigator.geolocation) {
      showToast(t('ui.geolocationNotSupported'), 'error');
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        console.log('Finding nearby terminals at:', latitude, longitude);
        // Here you would call the API endpoint /terminals/nearby
        // with lat, lng, radius, and limit parameters
        showToast(t('ui.locationFound'), 'success');
        setIsGettingLocation(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        showToast(t('ui.locationError'), 'error');
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  return (
    <section className="hero min-h-[80vh] sm:min-h-[85vh] bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-10 sm:top-20 left-5 sm:left-10 w-48 h-48 sm:w-72 sm:h-72 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 sm:bottom-20 right-5 sm:right-10 w-64 h-64 sm:w-96 sm:h-96 bg-secondary/10 rounded-full blur-3xl"></div>
      
      <div className="hero-content text-center relative z-10 px-4">
        <div className="max-w-5xl w-full">
          <div className="mb-6 flex justify-center">
            <div className="badge  badge-soft badge-outline badge-primary badge-lg px-6 py-3 text-sm sm:text-base font-medium shadow-sm cursor-pointer">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              <span>{t('landing.hero.badge')}</span>
            </div>
          </div>
          
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent leading-tight">
            {t('landing.hero.title')}
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl mb-8 sm:mb-10 text-base-content/70 max-w-3xl mx-auto leading-relaxed font-light px-4">
            {t('landing.hero.subtitle')}
          </p>
          
          {/* Enhanced Search Interface */}
          <div className="max-w-4xl mx-auto mb-8 sm:mb-12 px-2 sm:px-4">
            <div className="bg-base-100/95 backdrop-blur-md rounded-2xl sm:rounded-3xl shadow-2xl border border-base-300 p-2 hover:shadow-3xl transition-all duration-300">
              <div className="flex flex-col lg:flex-row gap-2">
                {/* Search Input */}
                <div className="flex-1 relative">
                  <input 
                    type="text" 
                    placeholder={t('landing.hero.searchPlaceholder')}
                    className="input w-full h-12 sm:h-16 pl-4 sm:pl-6 pr-12 sm:pr-14 text-base sm:text-lg border-0 bg-base-200/30 focus:bg-base-200/50 focus:outline-none rounded-xl sm:rounded-2xl placeholder:text-base-content/60 transition-colors duration-200"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <button
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-sm btn-circle hover:bg-primary/10 transition-all duration-200 min-h-[44px] min-w-[44px]"
                    onClick={handleNearbySearch}
                    disabled={isGettingLocation}
                    title={t('accessibility.findNearbyTerminals')}
                    aria-label={t('accessibility.findNearbyTerminals')}
                  >
                    {isGettingLocation ? (
                      <LoadingSpinner size="sm" color="primary" />
                    ) : (
                      <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    )}
                  </button>
                </div>
                
                {/* Provider Dropdown and Search Button - Inline on Mobile */}
                <div className="flex gap-2">
                  {/* Provider Dropdown */}
                  <div className="relative flex-1 lg:flex-none" ref={dropdownRef}>
                    <button
                      className="btn btn-outline h-12 sm:h-16 px-3 sm:px-6 w-full lg:min-w-[160px] rounded-xl sm:rounded-2xl border-base-300 bg-base-200/20 hover:border-primary hover:bg-primary/10 transition-all duration-200 flex items-center justify-between text-sm sm:text-base min-h-[44px]"
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      aria-label={t('accessibility.selectProvider')}
                      aria-expanded={isDropdownOpen}
                      aria-haspopup="listbox"
                    >
                      <span className="text-sm sm:text-base font-medium truncate">{selectedProvider}</span>
                      <svg className={`w-4 h-4 ml-1 sm:ml-2 transition-transform duration-200 flex-shrink-0 ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>

                    {isDropdownOpen && (
                      <div className="absolute top-full left-0 right-0 mt-2 bg-base-100 rounded-2xl shadow-xl border border-base-300 z-50 overflow-hidden">
                        <div className="py-2">
                          {[t('ui.allProviders'), t('ui.lpExpress'), t('ui.omniva'), t('ui.dpd'), t('ui.venipak')].map((provider) => (
                            <button
                              key={provider}
                              className="w-full px-4 py-3 text-left hover:bg-primary/5 transition-colors duration-150 text-sm sm:text-base min-h-[44px] flex items-center"
                              onClick={() => {
                                setSelectedProvider(provider);
                                setIsDropdownOpen(false);
                              }}
                              role="option"
                              aria-selected={selectedProvider === provider}
                            >
                              {provider}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Search Button */}
                  <button
                    className="btn btn-primary h-12 sm:h-16 px-5 sm:px-8 min-w-[100px] sm:min-w-[140px] rounded-xl text-sm sm:text-lg font-semibold shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-200 min-h-[44px] flex-shrink-0"
                    onClick={handleSearch}
                    aria-label={t('accessibility.searchTerminals')}
                  >
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <span className="hidden sm:inline">{t('ui.search')}</span>
                    <span className="sm:hidden">{t('ui.go')}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Feature Badges */}
          <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8 sm:mb-12 px-2">
            <div className="badge badge-soft badge-primary px-4 py-3 text-sm font-medium flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {t('landing.hero.realTimeData') || 'Real-time Data'}
            </div>
            <div className="badge badge-soft badge-accent px-4 py-3 text-sm font-medium flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              {t('landing.hero.packageTracking') || 'Package Tracking'}
            </div>
            <div className="badge badge-soft badge-success px-4 py-3 text-sm font-medium flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {t('landing.hero.fastApi') || 'Fast API'}
            </div>
          </div>
          
          {/* Enhanced CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-6 justify-center items-center px-2 sm:px-4">
            <Link
              href="/auth/register"
              className="btn btn-primary btn-md sm:btn-lg px-6 sm:px-10 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-xl shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 group w-full sm:w-auto min-h-[44px]"
            >
              {t('landing.hero.cta')}
              <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </Link>
            <Link
              href="#docs"
              className="btn btn-outline btn-md sm:btn-lg px-6 sm:px-10 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-xl hover:scale-105 transition-all duration-300 group w-full sm:w-auto min-h-[44px]"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              {t('landing.hero.viewDocs')}
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}