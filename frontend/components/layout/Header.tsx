'use client';

import Link from 'next/link';
import { useAuthStore } from '@/lib/auth-store';
import { useTranslations } from '@/lib/translations';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';

export default function Header() {
  const { t } = useTranslations();
  const { isAuthenticated, user, logout } = useAuthStore();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="bg-base-100 shadow-sm border-b border-base-200 sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="navbar min-h-16 lg:min-h-20">
          {/* Logo */}
          <div className="navbar-start">
            <Link href="/" className="text-lg md:text-xl lg:text-2xl font-bold hover:bg-transparent px-2 lg:px-4">
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                {process.env.NEXT_PUBLIC_APP_NAME || 'Postal Terminal API'}
              </span>
            </Link>
          </div>
          
          {/* Desktop navigation */}
          <div className="navbar-center hidden lg:flex">
            <ul className="menu menu-horizontal px-1 gap-1">
              <li>
                <Link href="/#features" className="btn btn-ghost hover:bg-base-200 transition-colors font-bold">
                  {t('navigation.features')}
                </Link>
              </li>
              <li>
                <Link href="/#pricing" className="btn btn-ghost hover:bg-base-200 transition-colors font-bold">
                  {t('navigation.pricing')}
                </Link>
              </li>
              <li>
                <Link href="/docs" className="btn btn-ghost hover:bg-base-200 transition-colors font-bold">
                  {t('navigation.docs')}
                </Link>
              </li>
              <li>
                <Link href="/contact" className="btn btn-ghost hover:bg-base-200 transition-colors font-bold">
                  {t('navigation.contact')}
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Language switcher, theme toggle, mobile menu and user menu */}
          <div className="navbar-end gap-2">
            <LanguageSwitcher />
            <ThemeToggle />
            {isAuthenticated ? (
              <div className="dropdown dropdown-end">
                <div tabIndex={0} role="button" className="btn btn-ghost btn-circle avatar hover:bg-base-200 transition-colors">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-secondary text-primary-content flex items-center justify-center shadow-md">
                    <span className="text-sm font-semibold">
                      {user?.first_name?.[0]?.toUpperCase()}{user?.last_name?.[0]?.toUpperCase()}
                    </span>
                  </div>
                </div>
                <ul tabIndex={0} className="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1000] mt-3 w-56 p-3 shadow-xl border border-base-300">
                  <li className="mb-2">
                    <div className="text-sm text-base-content/70 px-3 py-2">
                      <div className="font-medium">{user?.first_name} {user?.last_name}</div>
                      <div className="text-xs">{user?.email}</div>
                    </div>
                  </li>
                  <li>
                    <Link href="/dashboard" className="text-base-content hover:bg-base-200 rounded-lg p-3 font-bold flex items-center gap-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h2a2 2 0 012 2v0H8v0z" />
                      </svg>
                      {t('navigation.dashboard')}
                    </Link>
                  </li>
                  <li className="border-t border-base-300 mt-2 pt-2">
                    <a onClick={handleLogout} className="text-error hover:bg-error/10 rounded-lg p-3 font-bold flex items-center gap-2 cursor-pointer">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                      {t('navigation.logout')}
                    </a>
                  </li>
                </ul>
              </div>
            ) : (
              <div className="hidden lg:flex gap-2">
                <Link href="/auth/login" className="btn btn-ghost btn-md hover:bg-base-200 transition-colors font-bold px-6 min-h-[44px]">
                  {t('navigation.login')}
                </Link>
                <Link href="/auth/register" className="btn btn-primary btn-md hover:scale-105 transition-all duration-200 font-bold px-6 shadow-md min-h-[44px]">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                  {t('navigation.register')}
                </Link>
              </div>
            )}
            {/* Mobile menu button */}
            <div className="dropdown dropdown-end lg:hidden">
              <div 
                tabIndex={0} 
                role="button" 
                className="btn btn-ghost btn-square hover:bg-base-200 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </div>
              <ul tabIndex={0} className="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1000] mt-3 w-64 p-3 shadow-xl border border-base-300">
                <li><Link href="/#features" className="text-base-content hover:bg-base-200 rounded-lg p-3 font-bold">{t('navigation.features')}</Link></li>
                <li><Link href="/#pricing" className="text-base-content hover:bg-base-200 rounded-lg p-3 font-bold">{t('navigation.pricing')}</Link></li>
                <li><Link href="/docs" className="text-base-content hover:bg-base-200 rounded-lg p-3 font-bold">{t('navigation.docs')}</Link></li>
                <li><Link href="/contact" className="text-base-content hover:bg-base-200 rounded-lg p-3 font-bold">{t('navigation.contact')}</Link></li>
                {!isAuthenticated && (
                  <>
                    <li className="mt-2 pt-2 border-t border-base-300">
                      <Link href="/auth/login" className="btn btn-ghost btn-sm w-full justify-start min-h-[44px]">
                        {t('navigation.login')}
                      </Link>
                    </li>
                    <li>
                      <Link href="/auth/register" className="btn btn-primary btn-sm w-full justify-start min-h-[44px]">
                        {t('navigation.register')}
                      </Link>
                    </li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}