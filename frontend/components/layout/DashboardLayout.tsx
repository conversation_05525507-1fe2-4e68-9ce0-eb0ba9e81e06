'use client';

import { ReactNode, useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/auth-store';
import { useTranslations } from '@/lib/translations';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';
import {
  LayoutDashboard,
  Key,
  CreditCard,
  BarChart3,
  User,
  FileText,
  LogOut,
  Menu,
  X,
  Settings,
  Users,
  Shield
} from 'lucide-react';

interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { t } = useTranslations();
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout } = useAuthStore();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const isAdmin = user?.role === 'ADMIN';

  const customerNavItems = [
    {
      name: t('navigation.dashboard'),
      href: '/dashboard',
      icon: LayoutDashboard,
      current: pathname === '/dashboard'
    },
    {
      name: t('dashboard.apiKeys'),
      href: '/dashboard/api-keys',
      icon: Key,
      current: pathname.startsWith('/dashboard/api-keys')
    },
    {
      name: 'Subscription',
      href: '/dashboard/subscription',
      icon: CreditCard,
      current: pathname.startsWith('/dashboard/subscription')
    },
    {
      name: 'Analytics',
      href: '/dashboard/analytics',
      icon: BarChart3,
      current: pathname.startsWith('/dashboard/analytics')
    },
    {
      name: 'Profile',
      href: '/dashboard/profile',
      icon: User,
      current: pathname.startsWith('/dashboard/profile')
    }
  ];

  const adminNavItems = [
    {
      name: 'Admin Dashboard',
      href: '/admin',
      icon: Shield,
      current: pathname === '/admin'
    },
    {
      name: 'Users',
      href: '/admin/users',
      icon: Users,
      current: pathname.startsWith('/admin/users')
    },
    {
      name: 'Subscriptions',
      href: '/admin/subscriptions',
      icon: CreditCard,
      current: pathname.startsWith('/admin/subscriptions')
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart3,
      current: pathname.startsWith('/admin/analytics')
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      current: pathname.startsWith('/admin/settings')
    }
  ];

  const navItems = isAdmin && pathname.startsWith('/admin') ? adminNavItems : customerNavItems;

  return (
    <div className="drawer lg:drawer-open">
      <input
        id="drawer-toggle"
        type="checkbox"
        className="drawer-toggle"
        checked={sidebarOpen}
        onChange={(e) => setSidebarOpen(e.target.checked)}
      />

      <div className="drawer-content flex flex-col">
        {/* Top bar */}
        <div className="navbar bg-base-100 border-b border-base-300 lg:hidden">
          <div className="flex-none">
            <label htmlFor="drawer-toggle" className="btn btn-square btn-ghost">
              <Menu className="w-5 h-5" />
            </label>
          </div>
          <div className="flex-1">
            <Link href="/" className="text-xl font-bold text-primary">
              PostalAPI
            </Link>
          </div>
          <div className="flex-none">
            <Link href="/docs" className="btn btn-ghost btn-sm">
              <FileText className="w-4 h-4 mr-2" />
              API Docs
            </Link>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 p-4 lg:p-6">
          {children}
        </main>
      </div>

      <div className="drawer-side">
        <label htmlFor="drawer-toggle" className="drawer-overlay"></label>
        <aside className="min-h-full w-64 bg-base-200">
          {/* Sidebar header */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-base-300">
            <Link href="/" className="text-xl font-bold text-primary">
              PostalAPI
            </Link>
          </div>

          {/* Navigation */}
          <div className="flex flex-col h-full">
            <nav className="flex-1 px-4 py-6">
              <ul className="menu menu-vertical gap-2">
                {navItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className={item.current ? 'active' : ''}
                        onClick={() => setSidebarOpen(false)}
                      >
                        <Icon className="w-5 h-5" />
                        {item.name}
                      </Link>
                    </li>
                  );
                })}
              </ul>

              {/* Switch between customer and admin views */}
              {isAdmin && (
                <div className="pt-4 mt-4 border-t border-base-300">
                  <div className="px-3 py-2 text-xs font-semibold opacity-70 uppercase tracking-wider">
                    Switch View
                  </div>
                  <ul className="menu menu-vertical">
                    <li>
                      <Link
                        href={pathname.startsWith('/admin') ? '/dashboard' : '/admin'}
                        onClick={() => setSidebarOpen(false)}
                      >
                        {pathname.startsWith('/admin') ? (
                          <>
                            <User className="w-5 h-5" />
                            Customer View
                          </>
                        ) : (
                          <>
                            <Shield className="w-5 h-5" />
                            Admin View
                          </>
                        )}
                      </Link>
                    </li>
                  </ul>
                </div>
              )}
            </nav>

            {/* User menu */}
            <div className="p-4 border-t border-base-300">
              <div className="flex items-center gap-3 mb-3">
                <div className="avatar placeholder">
                  <div className="bg-primary text-primary-content rounded-full w-10">
                    <span className="text-sm">
                      {user?.first_name?.[0] || user?.email?.[0] || 'U'}
                    </span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {user?.first_name && user?.last_name
                      ? `${user.first_name} ${user.last_name}`
                      : user?.email
                    }
                  </p>
                  <p className="text-xs opacity-70 truncate">
                    {user?.role === 'ADMIN' ? 'Administrator' : 'Customer'}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ThemeToggle />
                  <LanguageSwitcher />
                </div>
                <button
                  onClick={handleLogout}
                  className="btn btn-ghost btn-sm"
                  title="Sign Out"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </aside>
      </div>
    </div>
  );
}
