'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslations } from '@/lib/translations';
import { ApiClient } from '@/lib/api';

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address')
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordForm() {
  const { t } = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');
  const [submittedEmail, setSubmittedEmail] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema)
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true);
    setError('');

    try {
      const apiClient = new ApiClient();
      await apiClient.forgotPassword(data.email);
      setSubmittedEmail(data.email);
      setIsSuccess(true);
    } catch (err: any) {
      setError(err.message || t('auth.errors.forgotPasswordFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendEmail = async () => {
    if (!submittedEmail) return;
    
    setIsLoading(true);
    setError('');

    try {
      const apiClient = new ApiClient();
      await apiClient.forgotPassword(submittedEmail);
    } catch (err: any) {
      setError(err.message || t('auth.errors.resendFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="card w-full max-w-md bg-base-100 shadow-xl">
        <div className="card-body text-center">
          {/* Success Icon */}
          <div className="mx-auto w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          
          <h2 className="card-title text-2xl font-bold mb-4">
            {t('auth.forgotPassword.successTitle')}
          </h2>
          
          <p className="text-base-content/70 mb-6">
            {t('auth.forgotPassword.successMessage')}
          </p>
          
          <div className="bg-base-200 p-4 rounded-lg mb-6">
            <p className="text-sm text-base-content/70 mb-2">
              {t('auth.forgotPassword.emailSentTo')}
            </p>
            <p className="font-medium">{submittedEmail}</p>
          </div>
          
          {error && (
            <div className="alert alert-error mb-4">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error}</span>
            </div>
          )}
          
          <div className="space-y-3">
            <button
              onClick={handleResendEmail}
              className={`btn btn-outline w-full ${
                isLoading ? 'loading' : ''
              }`}
              disabled={isLoading}
            >
              {isLoading ? '' : t('auth.forgotPassword.resendEmail')}
            </button>
            
            <Link href="/auth/login" className="btn btn-primary w-full">
              {t('auth.forgotPassword.backToLogin')}
            </Link>
          </div>
          
          <div className="text-center mt-6">
            <p className="text-sm text-base-content/70">
              {t('auth.forgotPassword.checkSpam')}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card w-full max-w-md bg-base-100 shadow-xl">
      <div className="card-body">
        <div className="text-center mb-6">
          {/* Lock Icon */}
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          
          <h2 className="card-title text-2xl font-bold">
            {t('auth.forgotPassword.title')}
          </h2>
          <p className="text-base-content/70 mt-2">
            {t('auth.forgotPassword.subtitle')}
          </p>
        </div>
        
        {error && (
          <div className="alert alert-error mb-4">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{error}</span>
          </div>
        )}
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Email Field */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">{t('auth.forgotPassword.email')}</span>
            </label>
            <input
              type="email"
              placeholder={t('auth.forgotPassword.emailPlaceholder')}
              className={`input input-bordered w-full ${
                errors.email ? 'input-error' : ''
              }`}
              {...register('email')}
            />
            {errors.email && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.email.message}
                </span>
              </label>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className={`btn btn-primary w-full ${
              isLoading ? 'loading' : ''
            }`}
            disabled={isLoading}
          >
            {isLoading ? '' : t('auth.forgotPassword.submit')}
          </button>
        </form>

        {/* Back to Login */}
        <div className="text-center mt-6">
          <Link href="/auth/login" className="link link-primary text-sm">
            ← {t('auth.forgotPassword.backToLogin')}
          </Link>
        </div>
        
        {/* Help Text */}
        <div className="bg-base-200 p-4 rounded-lg mt-6">
          <h4 className="font-medium mb-2">
            {t('auth.forgotPassword.helpTitle')}
          </h4>
          <ul className="text-sm text-base-content/70 space-y-1">
            <li>• {t('auth.forgotPassword.helpStep1')}</li>
            <li>• {t('auth.forgotPassword.helpStep2')}</li>
            <li>• {t('auth.forgotPassword.helpStep3')}</li>
          </ul>
        </div>
      </div>
    </div>
  );
}