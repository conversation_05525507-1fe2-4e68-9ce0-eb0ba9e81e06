import { ReactNode } from 'react';
import { useTranslations } from '@/lib/translations';

interface AlertProps {
  children: ReactNode;
  type?: 'info' | 'success' | 'warning' | 'error';
  style?: 'default' | 'outline' | 'dash' | 'soft';
  direction?: 'horizontal' | 'vertical';
  className?: string;
  onClose?: () => void;
}

export const Alert = ({ 
  children, 
  type = 'info', 
  style = 'default',
  direction = 'horizontal',
  className = '',
  onClose 
}: AlertProps) => {
  const { t } = useTranslations();
  const getAlertClasses = () => {
    let classes = 'alert';
    
    if (type !== 'info') {
      classes += ` alert-${type}`;
    }
    
    if (style !== 'default') {
      classes += ` alert-${style}`;
    }
    
    if (direction === 'vertical') {
      classes += ' alert-vertical';
    }
    
    return `${classes} ${className}`;
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'info':
      default:
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  return (
    <div className={getAlertClasses()}>
      {getIcon()}
      <div className="flex-1">
        {children}
      </div>
      {onClose && (
        <button 
          className="btn btn-ghost btn-sm btn-circle"
          onClick={onClose}
          aria-label={t('accessibility.closeAlert')}
        >
          ✕
        </button>
      )}
    </div>
  );
};

// Specific alert components for common use cases
export const InfoAlert = ({ children, ...props }: Omit<AlertProps, 'type'>) => (
  <Alert type="info" {...props}>{children}</Alert>
);

export const SuccessAlert = ({ children, ...props }: Omit<AlertProps, 'type'>) => (
  <Alert type="success" {...props}>{children}</Alert>
);

export const WarningAlert = ({ children, ...props }: Omit<AlertProps, 'type'>) => (
  <Alert type="warning" {...props}>{children}</Alert>
);

export const ErrorAlert = ({ children, ...props }: Omit<AlertProps, 'type'>) => (
  <Alert type="error" {...props}>{children}</Alert>
);
