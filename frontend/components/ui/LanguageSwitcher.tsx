'use client';

import { useLanguageStore, type Language } from '@/lib/language-store';
import { useTranslations } from '@/lib/translations';
import Image from 'next/image';

const languages = [
  { code: 'en' as Language, name: 'English', flag: '/icons/flags/en.svg' },
  { code: 'lt' as Language, name: 'Lietuvių', flag: '/icons/flags/lt.svg' },
];

export default function LanguageSwitcher() {
  const { currentLanguage, setLanguage } = useLanguageStore();
  const { t } = useTranslations();

  const handleLanguageChange = () => {
    const newLanguage = currentLanguage === 'en' ? 'lt' : 'en';
    setLanguage(newLanguage);
  };

  const currentLang = languages.find(lang => lang.code === currentLanguage);
  const otherLang = languages.find(lang => lang.code !== currentLanguage);

  return (
    <label className="swap swap-rotate btn btn-ghost btn-circle hover:bg-base-200 transition-colors">
      <input 
        type="checkbox" 
        onChange={handleLanguageChange}
        checked={currentLanguage === 'lt'}
        className="sr-only"
      />
      
      {/* English flag - shown when Lithuanian is NOT selected */}
      <div className="swap-off flex items-center justify-center">
        <Image
          src="/icons/flags/en.svg"
          alt={t('accessibility.englishFlag')}
          width={20}
          height={20}
          className="rounded-sm"
        />
      </div>
      
      {/* Lithuanian flag - shown when Lithuanian IS selected */}
      <div className="swap-on flex items-center justify-center">
        <Image
          src="/icons/flags/lt.svg"
          alt={t('accessibility.lithuanianFlag')}
          width={20}
          height={20}
          className="rounded-sm"
        />
      </div>
    </label>
  );
}