'use client';

import { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period?: string;
  };
  icon?: LucideIcon;
  iconColor?: string;
  description?: string;
  loading?: boolean;
  className?: string;
  children?: ReactNode;
}

export default function StatsCard({
  title,
  value,
  change,
  icon: Icon,
  iconColor = 'text-primary',
  description,
  loading = false,
  className = '',
  children
}: StatsCardProps) {
  if (loading) {
    return (
      <div className={`card bg-base-100 shadow-xl ${className}`}>
        <div className="card-body">
          <div className="animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="h-4 bg-base-300 rounded w-24"></div>
              <div className="h-8 w-8 bg-base-300 rounded"></div>
            </div>
            <div className="h-8 bg-base-300 rounded w-32 mb-2"></div>
            <div className="h-3 bg-base-300 rounded w-20"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card bg-base-100 shadow-xl ${className}`}>
      <div className="card-body">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium opacity-70">{title}</h3>
          {Icon && (
            <div className={`p-2 rounded-lg bg-base-200 ${iconColor}`}>
              <Icon className="w-5 h-5" />
            </div>
          )}
        </div>

        <div className="flex items-baseline gap-3 mb-2">
          <p className="text-2xl font-bold">{value}</p>
          {change && (
            <div className={`badge ${
              change.type === 'increase' ? 'badge-success' : 'badge-error'
            }`}>
              {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
            </div>
          )}
        </div>

        {(description || change?.period) && (
          <p className="text-sm opacity-60">
            {description}
            {change?.period && ` ${change.period}`}
          </p>
        )}

        {children && (
          <div className="mt-4">
            {children}
          </div>
        )}
      </div>
    </div>
  );
}

// Progress Stats Card
interface ProgressStatsCardProps {
  title: string;
  current: number;
  total: number;
  unit?: string;
  icon?: LucideIcon;
  iconColor?: string;
  progressColor?: string;
  loading?: boolean;
  className?: string;
}

export function ProgressStatsCard({
  title,
  current,
  total,
  unit = '',
  icon: Icon,
  iconColor = 'text-primary',
  progressColor = 'progress-primary',
  loading = false,
  className = ''
}: ProgressStatsCardProps) {
  const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
  
  if (loading) {
    return (
      <div className={`card bg-base-100 shadow-xl ${className}`}>
        <div className="card-body">
          <div className="animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="h-4 bg-base-300 rounded w-24"></div>
              <div className="h-8 w-8 bg-base-300 rounded"></div>
            </div>
            <div className="h-6 bg-base-300 rounded w-32 mb-3"></div>
            <div className="h-2 bg-base-300 rounded w-full mb-2"></div>
            <div className="h-3 bg-base-300 rounded w-20"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card bg-base-100 shadow-xl ${className}`}>
      <div className="card-body">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium opacity-70">{title}</h3>
          {Icon && (
            <div className={`p-2 rounded-lg bg-base-200 ${iconColor}`}>
              <Icon className="w-5 h-5" />
            </div>
          )}
        </div>

        <div className="mb-3">
          <div className="flex items-baseline gap-2">
            <span className="text-2xl font-bold">
              {current.toLocaleString()}
            </span>
            <span className="text-sm opacity-60">
              / {total.toLocaleString()} {unit}
            </span>
          </div>
        </div>

        <div className="mb-2">
          <progress
            className={`progress ${progressColor} w-full`}
            value={current}
            max={total}
          ></progress>
        </div>

        <p className="text-sm opacity-60">
          {percentage}% used
        </p>
      </div>
    </div>
  );
}
