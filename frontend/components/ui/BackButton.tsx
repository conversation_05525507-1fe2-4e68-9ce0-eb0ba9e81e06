'use client';

import { useRouter } from 'next/navigation';
import { useTranslations } from '@/lib/translations';

interface BackButtonProps {
  href?: string;
  className?: string;
  showText?: boolean;
}

export default function BackButton({ href, className = '', showText = false }: BackButtonProps) {
  const router = useRouter();
  const { t } = useTranslations();

  const handleBack = () => {
    if (href) {
      router.push(href as any);
    } else {
      router.back();
    }
  };

  return (
    <button
      onClick={handleBack}
      className={`btn btn-ghost ${showText ? 'btn-sm md:btn-md' : 'btn-circle'} ${className}`}
      aria-label="Go back"
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
      </svg>
      {showText && (
        <span className="hidden md:inline ml-2">{t('common.back')}</span>
      )}
    </button>
  );
}