interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'accent' | 'neutral' | 'info' | 'success' | 'warning' | 'error';
  className?: string;
}

export const LoadingSpinner = ({ 
  size = 'md', 
  color = 'primary', 
  className = '' 
}: LoadingSpinnerProps) => {
  return (
    <span className={`loading loading-spinner loading-${size} text-${color} ${className}`} />
  );
};

export const LoadingDots = ({ 
  size = 'md', 
  color = 'primary', 
  className = '' 
}: LoadingSpinnerProps) => {
  return (
    <span className={`loading loading-dots loading-${size} text-${color} ${className}`} />
  );
};

export const LoadingBars = ({ 
  size = 'md', 
  color = 'primary', 
  className = '' 
}: LoadingSpinnerProps) => {
  return (
    <span className={`loading loading-bars loading-${size} text-${color} ${className}`} />
  );
};

export const LoadingRing = ({ 
  size = 'md', 
  color = 'primary', 
  className = '' 
}: LoadingSpinnerProps) => {
  return (
    <span className={`loading loading-ring loading-${size} text-${color} ${className}`} />
  );
};

export const LoadingBall = ({ 
  size = 'md', 
  color = 'primary', 
  className = '' 
}: LoadingSpinnerProps) => {
  return (
    <span className={`loading loading-ball loading-${size} text-${color} ${className}`} />
  );
};

export const LoadingInfinity = ({ 
  size = 'md', 
  color = 'primary', 
  className = '' 
}: LoadingSpinnerProps) => {
  return (
    <span className={`loading loading-infinity loading-${size} text-${color} ${className}`} />
  );
};
