'use client';

import { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import { Key, AlertTriangle, ExternalLink, Copy, Check } from 'lucide-react';
import Link from 'next/link';

interface ApiKeyPromptProps {
  onApiKeySet: (apiKey: string) => void;
  error?: string;
}

export default function ApiKeyPrompt({ onApiKeySet, error }: ApiKeyPromptProps) {
  const { t } = useTranslations();
  const { user } = useAuthStore();
  const [apiKey, setApiKey] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!apiKey.trim()) return;

    setIsValidating(true);
    try {
      // Set the API key and test it
      apiClient.setApiKeyForTerminalSearch(apiKey.trim());
      onApiKeySet(apiKey.trim());
    } catch (error) {
      console.error('API key validation failed:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const clearApiKey = () => {
    setApiKey('');
    apiClient.clearApiKey();
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="card bg-base-100 shadow-xl border border-base-300">
        <div className="card-body">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-warning/10 rounded-lg">
              <Key className="w-6 h-6 text-warning" />
            </div>
            <div>
              <h3 className="text-xl font-bold">API Key Required</h3>
              <p className="text-base-content/70">
                Terminal search requires an API key for authentication
              </p>
            </div>
          </div>

          {error && (
            <div className="alert alert-error mb-4">
              <AlertTriangle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          )}

          {user ? (
            // Authenticated user - show API key input and dashboard link
            <div className="space-y-4">
              <div className="bg-info/10 p-4 rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="p-1 bg-info/20 rounded">
                    <AlertTriangle className="w-4 h-4 text-info" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-info">How to get your API key:</p>
                    <ol className="text-sm text-info/80 mt-2 space-y-1 list-decimal list-inside">
                      <li>Go to your <Link href="/dashboard/api-keys" className="link link-info">API Keys dashboard</Link></li>
                      <li>Create a new API key or copy an existing one</li>
                      <li>Paste it in the field below</li>
                    </ol>
                  </div>
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Your API Key</span>
                    {apiKey && (
                      <button
                        type="button"
                        onClick={clearApiKey}
                        className="label-text-alt text-error hover:text-error/80 transition-colors"
                      >
                        Clear
                      </button>
                    )}
                  </label>
                  <div className="join">
                    <input
                      type={showApiKey ? 'text' : 'password'}
                      placeholder="ptapi_..."
                      className="input input-bordered join-item flex-1"
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      required
                    />
                    <button
                      type="button"
                      className="btn btn-outline join-item"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? '👁️' : '👁️‍🗨️'}
                    </button>
                    {apiKey && (
                      <button
                        type="button"
                        className="btn btn-outline join-item"
                        onClick={() => copyToClipboard(apiKey)}
                      >
                        {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                      </button>
                    )}
                  </div>
                  <label className="label">
                    <span className="label-text-alt text-base-content/60">
                      API keys start with "ptapi_" and are 64+ characters long
                    </span>
                  </label>
                </div>

                <div className="flex gap-3">
                  <button
                    type="submit"
                    className="btn btn-primary flex-1"
                    disabled={!apiKey.trim() || isValidating}
                  >
                    {isValidating ? (
                      <>
                        <span className="loading loading-spinner loading-sm"></span>
                        Validating...
                      </>
                    ) : (
                      'Use This API Key'
                    )}
                  </button>
                  <Link href="/dashboard/api-keys" className="btn btn-outline">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Manage Keys
                  </Link>
                </div>
              </form>
            </div>
          ) : (
            // Unauthenticated user - show login prompt
            <div className="space-y-4">
              <div className="bg-warning/10 p-4 rounded-lg">
                <p className="text-sm text-warning">
                  You need to be logged in and have an API key to search terminals.
                </p>
              </div>

              <div className="flex gap-3">
                <Link href="/auth/login" className="btn btn-primary flex-1">
                  Login to Continue
                </Link>
                <Link href="/auth/register" className="btn btn-outline">
                  Create Account
                </Link>
              </div>

              <div className="divider">OR</div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">Enter API Key Directly</span>
                  </label>
                  <div className="join">
                    <input
                      type={showApiKey ? 'text' : 'password'}
                      placeholder="ptapi_..."
                      className="input input-bordered join-item flex-1"
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      required
                    />
                    <button
                      type="button"
                      className="btn btn-outline join-item"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? '👁️' : '👁️‍🗨️'}
                    </button>
                  </div>
                  <label className="label">
                    <span className="label-text-alt text-base-content/60">
                      If you already have an API key, you can use it directly
                    </span>
                  </label>
                </div>

                <button
                  type="submit"
                  className="btn btn-primary w-full"
                  disabled={!apiKey.trim() || isValidating}
                >
                  {isValidating ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Validating...
                    </>
                  ) : (
                    'Use API Key'
                  )}
                </button>
              </form>
            </div>
          )}

          <div className="mt-6 p-4 bg-base-200 rounded-lg">
            <h4 className="font-medium mb-2">Why do I need an API key?</h4>
            <p className="text-sm text-base-content/70">
              API keys help us provide reliable service by preventing abuse and ensuring fair usage. 
              They're free to create and allow you to search through thousands of postal terminals 
              across Lithuania.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
