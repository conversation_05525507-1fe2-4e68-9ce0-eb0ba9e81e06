'use client';

import { MapPin, Building, Package, ExternalLink } from 'lucide-react';
import { PostalTerminal, TerminalSearchResult, NearbyTerminalResult } from '@/lib/api';

type TerminalCardProps = {
  terminal: TerminalSearchResult | NearbyTerminalResult;
  onViewDetails: (terminal: TerminalSearchResult | NearbyTerminalResult) => void;
};

export default function TerminalCard({ terminal, onViewDetails }: TerminalCardProps) {
  const getTerminalTypeIcon = (type: string) => {
    switch (type) {
      case 'PARCEL_LOCKER':
        return <Package className="w-4 h-4" />;
      case 'POST_OFFICE':
        return <Building className="w-4 h-4" />;
      default:
        return <MapPin className="w-4 h-4" />;
    }
  };

  const formatDistance = (distance?: number) => {
    if (!distance) return '';
    return distance < 1 ? `${Math.round(distance * 1000)}m` : `${distance.toFixed(1)}km`;
  };

  const formatTerminalType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="card bg-base-100 shadow-xl border border-base-300 hover:shadow-2xl transition-shadow duration-200">
      <div className="card-body">
        <div className="flex items-start justify-between mb-3">
          <h3 className="card-title text-lg">{terminal.name}</h3>
          <div className="flex items-center gap-1 text-primary">
            {getTerminalTypeIcon(terminal.terminalType)}
          </div>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-base-content/50" />
            <span>{terminal.address}, {terminal.city}</span>
          </div>
          
          {terminal.postalCode && (
            <div className="flex items-center gap-2">
              <span className="w-4 h-4 text-center text-xs font-mono text-base-content/50">#</span>
              <span>{terminal.postalCode}</span>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            <Building className="w-4 h-4 text-base-content/50" />
            <span className="badge badge-outline badge-sm">{terminal.provider}</span>
          </div>
          
          {'distance' in terminal && terminal.distance && (
            <div className="flex items-center gap-2">
              <span className="w-4 h-4 text-center text-xs text-base-content/50">📍</span>
              <span className="font-medium text-primary">{formatDistance(terminal.distance)}</span>
            </div>
          )}

          {'relevanceScore' in terminal && terminal.relevanceScore && (
            <div className="flex items-center gap-2">
              <span className="w-4 h-4 text-center text-xs text-base-content/50">⭐</span>
              <span className="text-xs text-base-content/70">
                {(terminal.relevanceScore * 100).toFixed(0)}% match
              </span>
            </div>
          )}
        </div>
        
        <div className="card-actions justify-between mt-4">
          <div className="badge badge-ghost badge-sm">
            {formatTerminalType(terminal.terminalType)}
          </div>
          <div className="flex gap-2">
            <button 
              className="btn btn-ghost btn-sm"
              onClick={() => {
                const url = `https://www.google.com/maps?q=${terminal.latitude},${terminal.longitude}`;
                window.open(url, '_blank');
              }}
              title="Open in Google Maps"
            >
              <ExternalLink className="w-3 h-3" />
            </button>
            <button 
              className="btn btn-primary btn-sm"
              onClick={() => onViewDetails(terminal)}
            >
              Details
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
