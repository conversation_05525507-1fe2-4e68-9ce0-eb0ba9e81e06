# Setup Demo API Key

To enable public terminal search functionality, you need to create a demo API key in the backend and add it to your environment variables.

## Option 1: Create Demo API Key via Backend Script

If the backend has a script to create API keys:

```bash
cd backend
npm run create-api-key -- --name "Demo Frontend Key" --rate-limit 1000
```

## Option 2: Create Demo API Key via Database

If you need to create it directly in the database:

```sql
-- Connect to your PostgreSQL database
-- Replace 'your_demo_key_hash' with the actual hash of your demo key

INSERT INTO api_keys (
  user_id, 
  created_by_user_id, 
  key_hash, 
  name, 
  description,
  rate_limit_per_minute, 
  rate_limit_per_day, 
  rate_limit_burst,
  is_active
) VALUES (
  '********-0000-0000-0000-********0000', -- Default user ID for demo
  '********-0000-0000-0000-********0000', -- Created by system
  'your_demo_key_hash_here', -- Hash of your demo API key
  'Demo Frontend Key',
  'Demo API key for public terminal search functionality',
  1000, -- 1000 requests per minute
  50000, -- 50000 requests per day
  100, -- Burst limit
  true -- Active
);
```

## Option 3: Use Existing User API Key

1. Create a user account in your application
2. Login and create an API key through the dashboard
3. Copy the API key and add it to your `.env.local` file

## Environment Setup

Once you have a demo API key, add it to your `.env.local` file:

```bash
# Copy .env.example to .env.local
cp .env.example .env.local

# Edit .env.local and add your demo API key
DEMO_API_KEY=ptapi_your_actual_demo_key_here
NEXT_PUBLIC_API_URL=http://localhost:3000
```

## Testing

After setting up the demo API key, test the terminal search functionality:

1. Start the frontend: `npm run dev`
2. Go to `http://localhost:3001/find-terminals`
3. Try searching for terminals or using nearby search
4. Check the browser console and server logs for any errors

## Security Notes

- The demo API key is only used server-side in Next.js API routes
- It's never exposed to the client-side code
- Consider setting appropriate rate limits for the demo key
- Monitor usage to prevent abuse
