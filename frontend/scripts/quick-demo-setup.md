# Quick Demo API Key Setup

Since the API key creation script requires a user_id, here are the quickest ways to get a demo API key:

## Option 1: Create User Account and API Key via Frontend

1. **Start the frontend and backend**:
   ```bash
   # Terminal 1 - Backend
   cd backend
   npm run dev
   
   # Terminal 2 - Frontend  
   cd frontend
   npm run dev
   ```

2. **Create a user account**:
   - Go to `http://localhost:3001/auth/register`
   - Create an account with email: `<EMAIL>`
   - Verify email if required

3. **Create API key**:
   - Login and go to `http://localhost:3001/dashboard/api-keys`
   - Click "Create API Key"
   - Name it "Demo Frontend Key"
   - Copy the generated API key

4. **Add to environment**:
   ```bash
   # Add to frontend/.env.local
   DEMO_API_KEY=ptapi_your_copied_key_here
   ```

## Option 2: Use SQL to Create Everything

Run this SQL in your PostgreSQL database:

```sql
-- Create demo user
INSERT INTO users (
  id, email, first_name, last_name, password_hash, 
  email_verified, is_active, role
) VALUES (
  '********-1111-1111-1111-************',
  '<EMAIL>', 'Demo', 'User',
  '$2b$10$dummy.hash.that.wont.work.for.login',
  true, true, 'USER'
) ON CONFLICT (email) DO NOTHING;

-- Create subscription for demo user
INSERT INTO subscriptions (
  user_id, plan_id, status, 
  current_period_start, current_period_end
) VALUES (
  '********-1111-1111-1111-************',
  (SELECT id FROM subscription_plans WHERE name ILIKE '%free%' LIMIT 1),
  'ACTIVE', NOW(), NOW() + INTERVAL '1 year'
) ON CONFLICT DO NOTHING;

-- Generate and insert API key
DO $$
DECLARE
    demo_key TEXT := 'ptapi_demo_' || encode(gen_random_bytes(28), 'hex');
    key_hash TEXT := encode(digest(demo_key, 'sha256'), 'hex');
BEGIN
    INSERT INTO api_keys (
        user_id, created_by_user_id, key_hash, name,
        rate_limit_per_minute, rate_limit_per_day, rate_limit_burst,
        is_active
    ) VALUES (
        '********-1111-1111-1111-************',
        '********-1111-1111-1111-************',
        key_hash, 'Demo Frontend Key',
        1000, 50000, 2000, true
    );
    
    RAISE NOTICE 'Demo API Key: %', demo_key;
    RAISE NOTICE 'Add to frontend/.env.local: DEMO_API_KEY=%', demo_key;
END $$;
```

## Option 3: Temporary Workaround

For immediate testing, you can temporarily modify the API proxy to skip authentication:

1. **Edit the proxy files** to return mock data
2. **Test the UI functionality** without real API calls
3. **Set up proper API key later**

## Recommended: Option 1

**Option 1 is recommended** because:
- ✅ Uses the proper user registration flow
- ✅ Creates valid subscription and API key relationships  
- ✅ Tests the full authentication system
- ✅ Provides a real API key for testing

Once you have the API key, add it to `frontend/.env.local` and the terminal search will work!
