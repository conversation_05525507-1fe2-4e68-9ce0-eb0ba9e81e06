-- Create Demo API Key for Frontend
-- Run this SQL script in your PostgreSQL database

-- First, create a system user for demo API keys (if it doesn't exist)
INSERT INTO users (
  id,
  email,
  first_name,
  last_name,
  password_hash,
  email_verified,
  is_active,
  role,
  created_at,
  updated_at
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  '<EMAIL>',
  'System',
  'Demo',
  '$2b$10$dummy.hash.for.system.user.that.cannot.login',
  true,
  true,
  'USER',
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create a subscription for the system user (if needed)
INSERT INTO subscriptions (
  id,
  user_id,
  plan_id,
  status,
  current_period_start,
  current_period_end,
  created_at,
  updated_at
) VALUES (
  '00000000-0000-0000-0000-000000000001',
  '00000000-0000-0000-0000-000000000000',
  (SELECT id FROM subscription_plans WHERE name = 'Free' LIMIT 1),
  'ACTIVE',
  NOW(),
  NOW() + INTERVAL '1 year',
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Now create the demo API key
-- Replace 'your_demo_key_hash_here' with the actual hash of your demo key
-- You can generate a key using: node -e "const crypto = require('crypto'); console.log('ptapi_' + crypto.randomBytes(32).toString('hex'));"

DO $$
DECLARE
    demo_key_secret TEXT := 'ptapi_' || encode(gen_random_bytes(32), 'hex');
    demo_key_hash TEXT;
BEGIN
    -- Hash the key (you'll need to implement the same hashing as your backend)
    -- For now, we'll use a simple approach
    demo_key_hash := encode(digest(demo_key_secret, 'sha256'), 'hex');
    
    -- Insert the API key
    INSERT INTO api_keys (
        user_id,
        created_by_user_id,
        key_hash,
        name,
        description,
        rate_limit_per_minute,
        rate_limit_per_day,
        rate_limit_burst,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000',
        demo_key_hash,
        'Demo Frontend Key',
        'Demo API key for public terminal search functionality',
        1000,
        50000,
        2000,
        true,
        NOW(),
        NOW()
    );
    
    -- Output the generated key
    RAISE NOTICE 'Demo API Key created: %', demo_key_secret;
    RAISE NOTICE 'Add this to your frontend/.env.local file:';
    RAISE NOTICE 'DEMO_API_KEY=%', demo_key_secret;
END $$;
