import enMessages from '../messages/en.json';
import ltMessages from '../messages/lt.json';
import { useLanguageStore, type Language } from './language-store';

type Messages = typeof enMessages;
type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`;
}[keyof ObjectType & (string | number)];

type TranslationKey = NestedKeyOf<Messages>;

const messages = {
  en: enMessages,
  lt: ltMessages,
};

// Server-side function for getting translations with explicit language
export function getTranslations(language: Language = 'en') {
  const t = (key: TranslationKey): string => {
    const keys = key.split('.');
    let value: any = messages[language];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };

  return { t, currentLanguage: language };
}

// Client-side hook for reactive translations
export function useTranslations() {
  // Check if we're on the client side
  if (typeof window === 'undefined') {
    // Server-side fallback to English
    return getTranslations('en');
  }
  
  // Client-side with reactive language store
  const { currentLanguage } = useLanguageStore();
  return getTranslations(currentLanguage);
}

// Export messages for direct access if needed
export { messages };