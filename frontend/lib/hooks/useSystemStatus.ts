'use client';

import { useState, useEffect, useCallback } from 'react';

interface HealthCheck {
  database: 'healthy' | 'unhealthy';
  cache: 'healthy' | 'unhealthy';
  apiKeys: 'healthy' | 'unhealthy';
}

interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  checks: HealthCheck;
}

interface SystemStatus {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'loading' | 'error';
  message: string;
  isLoading: boolean;
  error: string | null;
  lastChecked: Date | null;
  checks?: HealthCheck;
}

const HEALTH_ENDPOINT = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/v1/health`;
const CHECK_INTERVAL = 30000; // 30 seconds
const TIMEOUT_DURATION = 10000; // 10 seconds

export function useSystemStatus() {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    status: 'loading',
    message: 'Checking system status...',
    isLoading: true,
    error: null,
    lastChecked: null
  });

  const checkSystemHealth = useCallback(async () => {
    try {
      setSystemStatus(prev => ({ ...prev, isLoading: true, error: null }));

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_DURATION);

      const response = await fetch(HEALTH_ENDPOINT, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status} ${response.statusText}`);
      }

      const healthData: HealthResponse = await response.json();
      
      let message = 'All systems operational';
      
      if (healthData.status === 'degraded') {
        message = 'Some services experiencing issues';
      } else if (healthData.status === 'unhealthy') {
        message = 'System experiencing issues';
      }

      setSystemStatus({
        status: healthData.status,
        message,
        isLoading: false,
        error: null,
        lastChecked: new Date(),
        checks: healthData.checks
      });

    } catch (error) {
      let errorMessage = 'Unable to check system status';
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Health check timed out';
        } else {
          errorMessage = error.message;
        }
      }

      setSystemStatus({
        status: 'error',
        message: 'System status unavailable',
        isLoading: false,
        error: errorMessage,
        lastChecked: new Date()
      });
    }
  }, []);

  useEffect(() => {
    // Initial check
    checkSystemHealth();

    // Set up periodic checks
    const interval = setInterval(checkSystemHealth, CHECK_INTERVAL);

    // Cleanup
    return () => {
      clearInterval(interval);
    };
  }, [checkSystemHealth]);

  // Manual refresh function
  const refreshStatus = useCallback(() => {
    checkSystemHealth();
  }, [checkSystemHealth]);

  return {
    ...systemStatus,
    refreshStatus
  };
}
