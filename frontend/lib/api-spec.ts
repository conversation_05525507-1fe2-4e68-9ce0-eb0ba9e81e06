export const apiSpec = {
  openapi: "3.0.0",
  info: {
    title: "Postal Terminal API",
    description: "Access comprehensive postal terminal location data and package tracking across Lithuania",
    version: "1.0.0",
    contact: {
      name: "API Support",
      email: "<EMAIL>"
    }
  },
  servers: [
    {
      url: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001",
      description: "API Server"
    }
  ],
  security: [
    {
      ApiKeyAuth: []
    }
  ],
  components: {
    securitySchemes: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "X-API-Key",
        description: "API key for authentication. Format: ptapi_[64-character-hex-string]"
      }
    },
    schemas: {
      Terminal: {
        type: "object",
        properties: {
          id: { type: "string", description: "Unique terminal identifier" },
          name: { type: "string", description: "Terminal name" },
          address: { type: "string", description: "Full address" },
          city: { type: "string", description: "City name" },
          provider: { 
            type: "string", 
            enum: ["LP_EXPRESS", "OMNIVA", "DPD", "VENIPAK"],
            description: "Service provider"
          },
          terminalType: { type: "string", description: "Type of terminal" },
          coordinates: {
            type: "object",
            properties: {
              lat: { type: "number", description: "Latitude" },
              lng: { type: "number", description: "Longitude" }
            }
          },
          workingHours: { type: "string", description: "Operating hours" },
          active: { type: "boolean", description: "Terminal status" }
        }
      },
      TrackingInfo: {
        type: "object",
        properties: {
          trackingNumber: { type: "string", description: "Package tracking number" },
          provider: { 
            type: "string", 
            enum: ["LP_EXPRESS", "OMNIVA", "DPD", "VENIPAK"],
            description: "Service provider"
          },
          status: { type: "string", description: "Current package status" },
          events: {
            type: "array",
            items: {
              type: "object",
              properties: {
                timestamp: { type: "string", format: "date-time" },
                status: { type: "string" },
                location: { type: "string" },
                description: { type: "string" }
              }
            }
          },
          estimatedDelivery: { type: "string", format: "date-time", nullable: true }
        }
      },
      Error: {
        type: "object",
        properties: {
          success: { type: "boolean", example: false },
          error: { type: "string", description: "Error message" },
          message: { type: "string", description: "Detailed error description" },
          code: { type: "string", description: "Error code", nullable: true }
        }
      },
      PaginatedResponse: {
        type: "object",
        properties: {
          data: { type: "array" },
          pagination: {
            type: "object",
            properties: {
              page: { type: "integer" },
              limit: { type: "integer" },
              total: { type: "integer" },
              totalPages: { type: "integer" },
              hasNext: { type: "boolean" },
              hasPrev: { type: "boolean" }
            }
          }
        }
      }
    }
  },
  paths: {
    "/api/v1/terminals": {
      get: {
        summary: "List postal terminals",
        description: "Get a paginated list of postal terminals with optional filtering",
        tags: ["Terminals"],
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "page",
            in: "query",
            description: "Page number",
            schema: { type: "integer", default: 1, minimum: 1 }
          },
          {
            name: "limit",
            in: "query", 
            description: "Items per page",
            schema: { type: "integer", default: 50, minimum: 1, maximum: 100 }
          },
          {
            name: "city",
            in: "query",
            description: "Filter by city name",
            schema: { type: "string" }
          },
          {
            name: "provider",
            in: "query",
            description: "Filter by service provider",
            schema: { 
              type: "string",
              enum: ["LP_EXPRESS", "OMNIVA", "DPD", "VENIPAK"]
            }
          },
          {
            name: "terminalType",
            in: "query",
            description: "Filter by terminal type",
            schema: { type: "string" }
          },
          {
            name: "active",
            in: "query",
            description: "Filter by active status",
            schema: { type: "boolean" }
          },
          {
            name: "sort",
            in: "query",
            description: "Sort field and order (e.g., 'name:asc', 'city:desc')",
            schema: { type: "string" }
          }
        ],
        responses: {
          "200": {
            description: "List of terminals",
            content: {
              "application/json": {
                schema: {
                  allOf: [
                    { $ref: "#/components/schemas/PaginatedResponse" },
                    {
                      type: "object",
                      properties: {
                        data: {
                          type: "array",
                          items: { $ref: "#/components/schemas/Terminal" }
                        }
                      }
                    }
                  ]
                }
              }
            }
          },
          "401": {
            description: "Unauthorized - Invalid API key",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          },
          "429": {
            description: "Too Many Requests - Rate limit exceeded",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          }
        }
      }
    },
    "/api/v1/terminals/{id}": {
      get: {
        summary: "Get terminal by ID",
        description: "Get detailed information about a specific terminal",
        tags: ["Terminals"],
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "id",
            in: "path",
            required: true,
            description: "Terminal ID",
            schema: { type: "string" }
          }
        ],
        responses: {
          "200": {
            description: "Terminal details",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Terminal" }
              }
            }
          },
          "404": {
            description: "Terminal not found",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          },
          "401": {
            description: "Unauthorized - Invalid API key",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          }
        }
      }
    },
    "/api/v1/terminals/nearby": {
      get: {
        summary: "Find nearby terminals",
        description: "Find terminals near specified coordinates",
        tags: ["Terminals"],
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "lat",
            in: "query",
            required: true,
            description: "Latitude coordinate",
            schema: { type: "number", format: "double" }
          },
          {
            name: "lng",
            in: "query",
            required: true,
            description: "Longitude coordinate",
            schema: { type: "number", format: "double" }
          },
          {
            name: "radius",
            in: "query",
            description: "Search radius in meters",
            schema: { type: "integer", default: 5000, minimum: 100, maximum: 50000 }
          },
          {
            name: "limit",
            in: "query",
            description: "Maximum number of results",
            schema: { type: "integer", default: 20, minimum: 1, maximum: 100 }
          }
        ],
        responses: {
          "200": {
            description: "List of nearby terminals with distances",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    data: {
                      type: "array",
                      items: {
                        allOf: [
                          { $ref: "#/components/schemas/Terminal" },
                          {
                            type: "object",
                            properties: {
                              distance: { type: "number", description: "Distance in meters" }
                            }
                          }
                        ]
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            description: "Bad Request - Invalid coordinates",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          },
          "401": {
            description: "Unauthorized - Invalid API key",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          }
        }
      }
    },
    "/api/v1/terminals/search": {
      get: {
        summary: "Search terminals",
        description: "Search terminals by text query",
        tags: ["Terminals"],
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "q",
            in: "query",
            required: true,
            description: "Search query (terminal name, address, city)",
            schema: { type: "string", minLength: 1 }
          },
          {
            name: "limit",
            in: "query",
            description: "Maximum number of results",
            schema: { type: "integer", default: 20, minimum: 1, maximum: 100 }
          }
        ],
        responses: {
          "200": {
            description: "List of matching terminals",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    data: {
                      type: "array",
                      items: { $ref: "#/components/schemas/Terminal" }
                    }
                  }
                }
              }
            }
          },
          "400": {
            description: "Bad Request - Missing or invalid query",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          },
          "401": {
            description: "Unauthorized - Invalid API key",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          }
        }
      }
    },
    "/api/v1/track": {
      get: {
        summary: "Track package",
        description: "Track package by provider and tracking number",
        tags: ["Tracking"],
        security: [{ ApiKeyAuth: [] }],
        parameters: [
          {
            name: "provider",
            in: "query",
            required: true,
            description: "Shipping provider",
            schema: {
              type: "string",
              enum: ["LP_EXPRESS", "OMNIVA", "DPD", "VENIPAK"]
            }
          },
          {
            name: "trackingNumber",
            in: "query",
            required: true,
            description: "Package tracking number",
            schema: { type: "string", minLength: 1 }
          },
          {
            name: "refresh",
            in: "query",
            description: "Force refresh from provider",
            schema: { type: "boolean", default: false }
          }
        ],
        responses: {
          "200": {
            description: "Package tracking information",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/TrackingInfo" }
              }
            }
          },
          "400": {
            description: "Bad Request - Missing or invalid parameters",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          },
          "404": {
            description: "Package not found",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          },
          "401": {
            description: "Unauthorized - Invalid API key",
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/Error" }
              }
            }
          }
        }
      }
    }
  }
};
