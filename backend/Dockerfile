# Use the latest Node.js 22 Alpine image to meet engine requirements
FROM node:22.13-alpine

# Set the working directory inside the container
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install ALL dependencies (including dev dependencies for build)
RUN npm ci --silent

# Copy the source code, then remove the DPD scraper micro-service (built and deployed separately)
# This keeps the image small and avoids including Puppeteer/Chromium assets.
COPY . .
RUN rm -rf ./dpd-scraper

# Build the TypeScript application
RUN npm run build

# Remove dev dependencies and source files after build
RUN npm prune --production --silent && \
    rm -rf src tsconfig.json .eslintrc* *.config.js && \
    npm cache clean --force

# Create a non-root user to run the application
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Create necessary directories and set permissions
RUN mkdir -p /app/data/downloads && \
    chown -R nodejs:nodejs /app/data

# Switch to the non-root user
USER nodejs

# Expose the port the app runs on
EXPOSE 3000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/v1/health || exit 1

# Start the application directly
CMD ["node", "dist/index.js"]
