module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint'],
  extends: [
    'eslint:recommended',
  ],
  root: true,
  env: {
    node: true,
    es2020: true,
  },
  ignorePatterns: ['.eslintrc.js', 'dist/**', 'node_modules/**', 'scripts/**/*.js'],
  rules: {
    // TypeScript specific rules - More lenient for framework integration
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/no-explicit-any': ['warn', { 
      ignoreRestArgs: true,
      fixToUnknown: false 
    }],
    
    // General rules - Production-friendly settings
    'no-console': ['warn', { 
      allow: ['warn', 'error', 'log', 'info'] 
    }],
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-template': 'error',
    'no-unused-vars': 'off', // Disable base rule as it can report incorrect errors
    
    // Code quality - Appropriate for API/middleware code
    'complexity': ['warn', 30],
    'max-depth': ['warn', 7],
    'max-lines-per-function': ['warn', 200],
    'no-duplicate-imports': 'error',
    'no-fallthrough': 'error',
    'no-useless-escape': 'error',
    'no-case-declarations': 'error',
    
    // Security
    'no-eval': 'error',
    'no-implied-eval': 'error',
  },
  overrides: [
    {
      // CLI scripts can use console freely and have more complexity
      files: ['src/scripts/**/*.ts'],
      rules: {
        'no-console': 'off',
        'complexity': 'off',
        'max-lines-per-function': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
      }
    },
    {
      // Middleware files often need any types for framework integration
      files: ['src/middleware/**/*.ts', 'src/routes/**/*.ts'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off', // Framework integration requires any
        'complexity': ['warn', 35],
        'max-lines-per-function': ['warn', 250],
      }
    },
    {
      // Type definition files can use any appropriately
      files: ['src/types/**/*.ts'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off', // Type definitions legitimately use any
      }
    },
    {
      // Service files may have complex business logic and database mapping
      files: ['src/services/**/*.ts'],
      rules: {
        'complexity': ['warn', 35],
        'max-lines-per-function': ['warn', 200],
        '@typescript-eslint/no-explicit-any': ['warn', { 
          ignoreRestArgs: true,
          fixToUnknown: false 
        }],
      }
    },
    {
      // Data-collection adapters involve heavy parsing; allow higher complexity
      files: ['src/services/data-collection/**/*.ts'],
      rules: {
        'complexity': ['warn', 50],
        'max-lines-per-function': ['warn', 300]
      }
    },
    {
      // Database and utility modules often work with untyped data
      files: ['src/database/**/*.ts', 'src/utils/**/*.ts', '**/logger.ts', '**/logging.ts'],
      rules: {
        'no-console': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
      }
    }
  ]
}; 