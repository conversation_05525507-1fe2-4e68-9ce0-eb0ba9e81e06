# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Build output
dist

# Documentation
docs

# Test files
tests
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Development tools
.eslintrc.*
.prettierrc.*

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Deployment scripts
scripts/deploy.sh

# OpenCart plugin (not needed in API container)
opencart-postal-terminal-plugin
api-test.html

# Temporary files
tmp
temp

# Cache
.cache

# Performance test configs (causing Node version conflicts)
artillery-*.yml
tests/performance/

# Additional config files not needed in production
ecosystem.config.js
.eslintrc*
.prettierrc*

# Local provider data/cache – not needed in image
data/downloads/
data/*.json

# Exclude DPD scraper micro-service (separate container)
dpd-scraper/