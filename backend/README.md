# Postal Terminal API

High-performance postal terminal API for Lithuania with sub-50ms response times.

## Features

- 🚀 **High Performance**: Sub-50ms response times with PostgreSQL + PostGIS
- 🔐 **Secure Authentication**: API key-based authentication with rate limiting
- 🗺️ **Geographic Search**: Find terminals by location with radius search
- 🔍 **Full-Text Search**: Multi-field search with Lithuanian language support
- 📊 **Monitoring**: Health checks and Prometheus metrics
- 🏗️ **Production Ready**: PM2 deployment with comprehensive logging

## Quick Start

### Prerequisites

- Node.js 18+ (tested with v22)
- PostgreSQL 15+ with PostGIS extension
- npm or yarn

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd postal-terminal-api

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local
```

### 2. Database Setup

Edit `.env.local` with your database connection:

```env
DATABASE_URL=postgresql://user:password@localhost:5432/postal_terminals
API_KEY_SECRET=your-secret-key-for-hashing-api-keys-change-in-production
```

Set up PostGIS and create tables:

```bash
# Make setup script executable
chmod +x scripts/setup.sh

# Run database setup
./scripts/setup.sh
```

### 3. Build and Start

```bash
# Build TypeScript
npm run build

# Create an API key
npm run create-api-key -- --name "Development Key"

# Start development server
npm run dev
```

The API will be available at `http://localhost:3000`

## API Endpoints

### Core Endpoints (Authentication Required)

- `GET /api/v1/terminals` - List terminals with filtering
- `GET /api/v1/terminals/:id` - Get terminal details
- `GET /api/v1/terminals/nearby` - Find nearby terminals
- `GET /api/v1/terminals/search` - Search terminals

### Health Endpoints (No Authentication)

- `GET /api/v1/health` - Health check
- `GET /api/v1/metrics` - Prometheus metrics

## Authentication

All API endpoints (except health) require an API key in the header:

You can supply the key in either of two headers:

1. **Custom** `X-API-Key` (back-compat)
2. **Standard** `Authorization: Bearer <key>`

Example:

```bash
# Custom header
curl -H "X-API-Key: ptapi_your_api_key_here" \
     http://localhost:3000/api/v1/terminals

# OR

# Standard Bearer header
curl -H "Authorization: Bearer ptapi_your_api_key_here" \
     http://localhost:3000/api/v1/terminals
```

## Development

### Scripts

```bash
npm run dev          # Start development server
npm run build        # Build TypeScript
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run lint         # Lint code
npm run create-api-key # Create new API key
```

### Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- validation.test.ts
```

### Project Structure

```
src/
├── config/          # Configuration management
├── database/        # Database connection and queries
├── middleware/      # Fastify middleware
├── routes/          # API route handlers
├── scripts/         # Utility scripts
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
└── test/            # Test setup and utilities
```

## Production Deployment

### Using PM2

```bash
# Build for production
npm run build

# Start with PM2
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save
pm2 startup
```

### Environment Variables

Required:
- `DATABASE_URL` - PostgreSQL connection string
- `API_KEY_SECRET` - Secret for API key hashing

Optional:
- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment (development/production)
- `LOG_LEVEL` - Logging level (debug/info/warn/error)

## Performance

- **Response Time**: <50ms for 95% of requests
- **Throughput**: >1000 requests/second
- **Cache Hit Rate**: >85%
- **Memory Usage**: <256MB

## Data Sources

- **LP Express**: CSV data from api-esavitarna.post.lt (~447 terminals)
- **Omniva**: JSON data from omniva.lt (~548 terminals)
- **DPD**: Excel files with geocoding (~400 terminals)
- **Venipak**: JSON API from go.venipak.lt (~528 terminals)

## License

MIT License - see LICENSE file for details.
