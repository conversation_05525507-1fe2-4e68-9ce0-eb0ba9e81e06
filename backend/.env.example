# Development Environment Configuration
NODE_ENV=development
PORT=3000
APP_VERSION=2.1.0

#CLOUDFLARE_PROXY=true

# Database Configuration
DATABASE_URL=postgresql://user:pass@localhost:5432/dbname

DATABASE_SSL=false

# API Security
API_KEY_SECRET=97abd5aertetye14cdd84ef906cd34db5302019a180a566dfd269f5076ee51721ecf1d33c

# JWT Authentication (SaaS)
JWT_SECRET=your-jwt-secret-key-change-in-production-min-32-chars
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# Logging
LOG_LEVEL=info

# Database Configuration
DB_MAX_CONNECTIONS=20
DB_QUERY_TIMEOUT=30000
DB_STATEMENT_TIMEOUT=60000

# Performance Settings
CACHE_SIZE=100000
MAX_CONCURRENT_QUERIES=100
QUERY_COMPLEXITY_LIMIT=1000

# Rate Limiting
DEFAULT_RATE_LIMIT=1000
BURST_LIMIT=2000
RATE_WINDOW_SIZE=60
ROOT_ENDPOINT_RATE_LIMIT=100
STRICT_RATE_LIMIT_MULTIPLIER=0.1
HEALTH_RATE_LIMIT_MULTIPLIER=2.0

# Data Synchronization - Smart Sync Settings
AUTO_UPDATE_ENABLED=true             # Enable automatic sync
RUN_INITIAL_SYNC=true                # Sync on startup
UPDATE_FREQUENCY=weekly              # weekly or monthly
DATA_SYNC_SCHEDULE="0 2 * * 1"       # Weekly sync on Monday at 2 AM (respects UPDATE_FREQUENCY)

# File Cache Settings
FILE_CACHE_DURATION_DAYS=30          # Auto-refresh every 30 days
FORCE_DOWNLOAD_ON_SYNC=false

# Smart Sync Configuration (NEW!)
ENABLE_SMART_SYNC=true               # Enable smart sync optimization
SKIP_SYNC_WHEN_NO_CHANGES=true      # Skip processing when files haven't changed

# Smart Geocoding Configuration
SMART_GEOCODING_LIMIT=10             # Max terminals to geocode during sync
SMART_GEOCODING_RATE_DELAY=800       # Delay between geocoding requests (ms)
GEOCODING_RATE_LIMIT_DELAY=1000      # Delay for full preprocessing (ms)

# Data Source URLs (configurable overrides)
LP_EXPRESS_CSV_URL=https://api-esavitarna.post.lt/terminal/list/csv
OMNIVA_JSON_URL=https://www.omniva.lt/locations.json
DPD_EXCEL_URL=https://www.dpd.com/lt/content/dam/dpd_lt/documents/pickup_locations.xlsx
VENIPAK_JSON_URL=https://go.venipak.lt/ws/get_pickup_points

# Tracking Provider URLs
LP_EXPRESS_TRACKING_URL=https://api-esavitarna.post.lt/tracking
OMNIVA_TRACKING_URL=https://mano.omniva.lt/api/track/shipment
VENIPAK_TRACKING_URL=https://venipak.com/lt/wp-json/v1/shipment-tracker
DPD_TRACKING_URL=https://www.dpdgroup.com/lt/mydpd/my-parcels/incoming

# Tracking Configuration
TRACKING_DEFAULT_TIMEOUT=10000
DPD_TRACKING_TIMEOUT=60000
ENABLE_DPD_TRACKING=false

# Geocoding Settings
GEOCODING_PROVIDER=nominatim
GEOCODING_ENABLED=true
GEOCODING_RATE_LIMIT=1000
GEOCODING_TIMEOUT=10000
NOMINATIM_BASE_URL=https://nominatim.openstreetmap.org/search
GEOCODING_RATE_LIMIT_DELAY=1000

# Search Settings
SEARCH_MIN_LENGTH=2
SEARCH_MAX_RESULTS=100
SEARCH_TIMEOUT=5000
TRIGRAM_THRESHOLD=0.3
NEARBY_MAX_RADIUS=100
NEARBY_DEFAULT_RADIUS=10

# Notification Settings
ADMIN_EMAIL=<EMAIL>
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_WEBHOOK_NOTIFICATIONS=true
WEBHOOK_URL=https://hooks.slack.com/your-webhook-url

# SMTP Settings (if using email)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Email Notification Configuration
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_RATE_LIMIT_PER_HOUR=50

# ================================================================================
# CONFIGURATION MANAGEMENT IMPROVEMENTS (v2.1.0)
# ================================================================================

# Cache Configuration
DEFAULT_CACHE_TTL_SECONDS=300
TERMINAL_DETAIL_CACHE_TTL=600
TERMINAL_LIST_CACHE_TTL=300
CACHE_CLEANUP_SCHEDULE="0 3 * * *"

# Validation Limits
MAX_PAGE_LIMIT=1000
MAX_RESULTS_PER_PAGE=100
MAX_TERMINAL_ID_LENGTH=255
MAX_CITY_NAME_LENGTH=255
MAX_SEARCH_QUERY_LENGTH=255
MIN_SEARCH_RADIUS=0.1
MAX_SEARCH_RADIUS=100

# Performance Thresholds
SLOW_REQUEST_THRESHOLD_MS=1000
VERY_SLOW_REQUEST_THRESHOLD_MS=5000
SLOW_DB_QUERY_THRESHOLD_MS=100
VERY_SLOW_DB_QUERY_THRESHOLD_MS=500

# File Download Configuration
FILE_DOWNLOAD_TIMEOUT_MS=60000
FILE_DOWNLOAD_MAX_RETRIES=3
FILE_DOWNLOAD_RETRY_BASE_DELAY_MS=1000
FILE_DOWNLOAD_USER_AGENT="PostalTerminalAPI/2.1.0"

# Tracking Service Configuration
TRACKING_DELIVERED_CACHE_TTL=86400
TRACKING_ACTIVE_CACHE_TTL=300
TRACKING_MAX_FAILURE_COUNT=5
TRACKING_CIRCUIT_BREAKER_TIMEOUT_MS=300000

# Geocoding Configuration
GEOCODING_RESULT_LIMIT=5

# Webhook Configuration
WEBHOOK_TIMEOUT_MS=10000

# Sync Configuration
INITIAL_SYNC_DELAY_MS=5000

# ================================================================================
# EMAIL NOTIFICATION & SECURITY IMPROVEMENTS (v2.1.0)
# ================================================================================

# Security Alert Thresholds
SECURITY_ALERT_THRESHOLD=10
HIGH_ERROR_RATE_THRESHOLD=5.0
CIRCUIT_BREAKER_NOTIFICATION_ENABLED=true

# Tracking Service Notifications
TRACKING_FAILURE_NOTIFICATION_THRESHOLD=3
TRACKING_CIRCUIT_BREAKER_NOTIFICATION=true

# ================================================================================
# EMAIL NOTIFICATION BATCHING (v2.1.0)
# ================================================================================

# Email Batching Configuration
ENABLE_EMAIL_BATCHING=true
MAX_BATCH_SIZE=50
MAX_BATCH_WAIT_TIME_MS=3600000

# Batching Intervals by Severity (milliseconds)
BATCH_INTERVAL_INFO_MS=900000      # 15 minutes for info notifications
BATCH_INTERVAL_WARNING_MS=300000   # 5 minutes for warning notifications
BATCH_INTERVAL_ERROR_MS=60000      # 1 minute for error notifications
BATCH_INTERVAL_CRITICAL_MS=0       # 0 = immediate for critical notifications

# ================================================================================
# SAAS AUTHENTICATION & BILLING (v2.1.0+)
# ================================================================================

# Stripe Payment Integration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback

# Email Service Provider (Alternative to SMTP)
RESEND_API_KEY=re_your_resend_api_key_here

# Additional Database Connection Pool Settings
DB_MIN_CONNECTIONS=2
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=300000

# Security Headers and CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
ALLOWED_HOSTS=localhost

# Trusted Proxies (if behind load balancer)
TRUSTED_PROXIES=

# Security Monitoring
ENABLE_SECURITY_LOGGING=true
SECURITY_LOG_LEVEL=INFO
