module.exports = {
  apps: [{
    name: 'postal-terminal-api',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      // Core Application
      NODE_ENV: 'production',
      PORT: process.env.PORT || 3000,
      APP_VERSION: process.env.APP_VERSION || '1.0.0',

      // Database Configuration
      DATABASE_URL: process.env.DATABASE_URL,
      DB_MAX_CONNECTIONS: process.env.DB_MAX_CONNECTIONS || '20',
      DB_MIN_CONNECTIONS: process.env.DB_MIN_CONNECTIONS || '2',
      DB_QUERY_TIMEOUT: process.env.DB_QUERY_TIMEOUT || '30000',
      DB_STATEMENT_TIMEOUT: process.env.DB_STATEMENT_TIMEOUT || '60000',
      DB_IDLE_TIMEOUT: process.env.DB_IDLE_TIMEOUT || '300000',
      DB_CONNECTION_TIMEOUT: process.env.DB_CONNECTION_TIMEOUT || '30000',

      // API Security
      API_KEY_SECRET: process.env.API_KEY_SECRET,

      // Logging
      LOG_LEVEL: process.env.LOG_LEVEL || 'info',

      // Performance Settings
      CACHE_SIZE: process.env.CACHE_SIZE || '100000',
      MAX_CONCURRENT_QUERIES: process.env.MAX_CONCURRENT_QUERIES || '100',
      QUERY_COMPLEXITY_LIMIT: process.env.QUERY_COMPLEXITY_LIMIT || '1000',

      // Rate Limiting
      DEFAULT_RATE_LIMIT: process.env.DEFAULT_RATE_LIMIT || '1000',
      BURST_LIMIT: process.env.BURST_LIMIT || '2000',
      RATE_WINDOW_SIZE: process.env.RATE_WINDOW_SIZE || '60',

      // Data Synchronization - Smart Sync Settings
      AUTO_UPDATE_ENABLED: process.env.AUTO_UPDATE_ENABLED || 'true',
      RUN_INITIAL_SYNC: process.env.RUN_INITIAL_SYNC || 'false', // false for production
      UPDATE_FREQUENCY: process.env.UPDATE_FREQUENCY || 'weekly',
      DATA_SYNC_SCHEDULE: process.env.DATA_SYNC_SCHEDULE || '0 2 * * 1',

      // File Cache Settings
      FILE_CACHE_DURATION_DAYS: process.env.FILE_CACHE_DURATION_DAYS || '30',
      FORCE_DOWNLOAD_ON_SYNC: process.env.FORCE_DOWNLOAD_ON_SYNC || 'false',

      // Smart Sync Configuration
      ENABLE_SMART_SYNC: process.env.ENABLE_SMART_SYNC || 'true',
      SKIP_SYNC_WHEN_NO_CHANGES: process.env.SKIP_SYNC_WHEN_NO_CHANGES || 'true',

      // Smart Geocoding Configuration
      SMART_GEOCODING_LIMIT: process.env.SMART_GEOCODING_LIMIT || '10',
      SMART_GEOCODING_RATE_DELAY: process.env.SMART_GEOCODING_RATE_DELAY || '800',
      GEOCODING_RATE_LIMIT_DELAY: process.env.GEOCODING_RATE_LIMIT_DELAY || '1000',

      // Data Source URLs (configurable overrides)
      LP_EXPRESS_CSV_URL: process.env.LP_EXPRESS_CSV_URL || 'https://api-esavitarna.post.lt/terminal/list/csv',
      OMNIVA_JSON_URL: process.env.OMNIVA_JSON_URL || 'https://www.omniva.lt/locations.json',
      DPD_EXCEL_URL: process.env.DPD_EXCEL_URL || 'https://www.dpd.com/lt/content/dam/dpd_lt/documents/pickup_locations.xlsx',
      VENIPAK_JSON_URL: process.env.VENIPAK_JSON_URL || 'https://go.venipak.lt/ws/get_pickup_points',

      // Geocoding Settings
      GEOCODING_PROVIDER: process.env.GEOCODING_PROVIDER || 'nominatim',
      GEOCODING_ENABLED: process.env.GEOCODING_ENABLED || 'true',
      GEOCODING_RATE_LIMIT: process.env.GEOCODING_RATE_LIMIT || '1000',
      GEOCODING_TIMEOUT: process.env.GEOCODING_TIMEOUT || '10000',
      NOMINATIM_BASE_URL: process.env.NOMINATIM_BASE_URL || 'https://nominatim.openstreetmap.org/search',

      // Search Settings
      SEARCH_MIN_LENGTH: process.env.SEARCH_MIN_LENGTH || '2',
      SEARCH_MAX_RESULTS: process.env.SEARCH_MAX_RESULTS || '100',
      SEARCH_TIMEOUT: process.env.SEARCH_TIMEOUT || '5000',
      TRIGRAM_THRESHOLD: process.env.TRIGRAM_THRESHOLD || '0.3',
      NEARBY_MAX_RADIUS: process.env.NEARBY_MAX_RADIUS || '100',
      NEARBY_DEFAULT_RADIUS: process.env.NEARBY_DEFAULT_RADIUS || '10',

      // Notification Settings
      ADMIN_EMAIL: process.env.ADMIN_EMAIL || '<EMAIL>',
      ENABLE_EMAIL_NOTIFICATIONS: process.env.ENABLE_EMAIL_NOTIFICATIONS || 'false',
      ENABLE_WEBHOOK_NOTIFICATIONS: process.env.ENABLE_WEBHOOK_NOTIFICATIONS || 'true',
      WEBHOOK_URL: process.env.WEBHOOK_URL,

      // SMTP Settings (if using email)
      SMTP_HOST: process.env.SMTP_HOST,
      SMTP_PORT: process.env.SMTP_PORT || '587',
      SMTP_USER: process.env.SMTP_USER,
      SMTP_PASSWORD: process.env.SMTP_PASSWORD
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G', // Increased for geocoding operations
    node_args: '--max-old-space-size=1024', // Increased for better performance
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'coverage', 'data/downloads'],
    min_uptime: '10s',
    max_restarts: 10,
    autorestart: true,
    restart_delay: 4000, // Wait 4s between restarts
    kill_timeout: 5000 // Allow 5s for graceful shutdown
  }]
};
