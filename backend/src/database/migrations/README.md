# Database Migrations

This directory contains the database migrations for the Postal Terminal API.

## Single Migration Approach

Instead of multiple incremental migrations, this project uses a **single comprehensive migration** that sets up the complete database schema from scratch. This approach provides several benefits:

- **Simplicity**: One file contains everything needed
- **Reliability**: No dependency chains or migration order issues  
- **Performance**: Faster setup for new environments
- **Maintenance**: Easier to understand and modify

## Files

- `000_complete_schema.sql` - Complete database schema with PostGIS setup
- `index.ts` - Migration runner and management system

## Usage

```bash
# Run migrations
npm run migrate

# Check migration status  
npx tsx src/scripts/migrate.ts status

# Force re-run (drops and recreates everything)
npx tsx src/scripts/migrate.ts reset
```

## What's Included

The complete schema migration includes:

### Extensions
- PostGIS for geographic data
- pg_trgm for fuzzy text search
- uuid-ossp for UUID generation

### Tables
- `terminals` - Main terminal data with PostGIS geography
- `api_keys` - Authentication and rate limiting
- `api_cache` - PostgreSQL-based caching  
- `tenants` - Multi-tenant support

### Performance Features
- Geographic GIST indexes for spatial queries
- Full-text search with GIN indexes
- Trigram indexes for fuzzy matching
- Covering indexes to avoid table lookups

### Database Functions
- Automatic search vector updates
- Distance calculations
- Cache cleanup utilities
- Data normalization functions

### Verification
- Extension availability checks
- PostGIS functionality tests  
- Table creation verification
- Self-diagnostics and error reporting

## For New Environments

The migration is designed to be run on a fresh PostgreSQL database with PostGIS support. It includes all necessary setup and verification steps.

See the [Database Setup Guide](../../../docs/database-setup.md) for detailed installation instructions. 