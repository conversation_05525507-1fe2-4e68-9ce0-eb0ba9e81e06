-- ================================================================================
-- SUBSCRIPTION MANAGEMENT ENHANCEMENT - PHASE 2 SAAS IMPLEMENTATION
-- ================================================================================
-- Version: 2.1.0
-- Created: 2025-07-09
-- Description: Enhance existing subscription management system with additional features
--
-- This migration adds:
-- ✅ Missing columns to existing subscription_plans table
-- ✅ Order tracking and payment history tables
-- ✅ Audit logging for subscription changes
-- ✅ Enhanced Stripe integration support
-- ================================================================================

-- =============================================================================
-- STEP 1: ENHANCE EXISTING SUBSCRIPTION PLANS TABLE
-- =============================================================================

-- Add missing columns to existing subscription_plans table
DO $$
BEGIN
  -- Add display_name column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'display_name') THEN
    ALTER TABLE subscription_plans ADD COLUMN display_name VARCHAR(200);
    -- Set display_name based on existing name, making it more user-friendly
    UPDATE subscription_plans SET display_name =
      CASE
        WHEN name = 'free' THEN 'Free Plan'
        WHEN name = 'starter' THEN 'Starter Plan'
        WHEN name = 'professional' THEN 'Professional Plan'
        WHEN name = 'enterprise' THEN 'Enterprise Plan'
        ELSE INITCAP(REPLACE(name, '_', ' ')) || ' Plan'
      END;
    ALTER TABLE subscription_plans ALTER COLUMN display_name SET NOT NULL;
  END IF;

  -- Add currency column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'currency') THEN
    ALTER TABLE subscription_plans ADD COLUMN currency CHAR(3) DEFAULT 'EUR' CHECK (currency ~ '^[A-Z]{3}$');
  END IF;

  -- Add API request limits if they don't exist (map from existing rate limits)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'api_requests_per_month') THEN
    ALTER TABLE subscription_plans ADD COLUMN api_requests_per_month INTEGER;
    -- Calculate monthly limits from daily limits (assuming 30 days)
    UPDATE subscription_plans SET api_requests_per_month = rate_limit_per_day * 30;
    ALTER TABLE subscription_plans ALTER COLUMN api_requests_per_month SET NOT NULL;
    ALTER TABLE subscription_plans ADD CONSTRAINT positive_monthly_requests CHECK (api_requests_per_month > 0);
  END IF;

  -- Rename rate_limit_per_minute to api_requests_per_minute for consistency
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'rate_limit_per_minute')
     AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'api_requests_per_minute') THEN
    ALTER TABLE subscription_plans RENAME COLUMN rate_limit_per_minute TO api_requests_per_minute;
  END IF;

  -- Add Stripe integration columns if they don't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'stripe_price_id_monthly') THEN
    ALTER TABLE subscription_plans ADD COLUMN stripe_price_id_monthly VARCHAR(255);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'stripe_price_id_yearly') THEN
    ALTER TABLE subscription_plans ADD COLUMN stripe_price_id_yearly VARCHAR(255);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'stripe_product_id') THEN
    ALTER TABLE subscription_plans ADD COLUMN stripe_product_id VARCHAR(255);
  END IF;

  -- Add is_public column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'is_public') THEN
    ALTER TABLE subscription_plans ADD COLUMN is_public BOOLEAN DEFAULT true;
  END IF;

  -- Keep price_eur column name for now (will be renamed later if needed)
  -- This ensures compatibility with existing data and applications

  -- Add price_yearly column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'price_yearly') THEN
    ALTER TABLE subscription_plans ADD COLUMN price_yearly DECIMAL(10,2) CHECK (price_yearly >= 0);
    -- Set yearly price as 10x monthly (common discount)
    UPDATE subscription_plans SET price_yearly = price_eur * 10 WHERE price_eur > 0;
  END IF;

END $$;

-- =============================================================================
-- STEP 2: ENHANCE EXISTING USER SUBSCRIPTIONS TABLE
-- =============================================================================

-- Add missing columns to existing user_subscriptions table
DO $$
BEGIN
  -- Add billing_cycle column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_subscriptions' AND column_name = 'billing_cycle') THEN
    ALTER TABLE user_subscriptions ADD COLUMN billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly'));
  END IF;

  -- Add usage tracking columns if they don't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_subscriptions' AND column_name = 'api_requests_used') THEN
    ALTER TABLE user_subscriptions ADD COLUMN api_requests_used INTEGER DEFAULT 0 CHECK (api_requests_used >= 0);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_subscriptions' AND column_name = 'api_requests_reset_at') THEN
    ALTER TABLE user_subscriptions ADD COLUMN api_requests_reset_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;

  -- Update status constraint to include 'paused' if not already present
  IF EXISTS (SELECT 1 FROM information_schema.table_constraints tc
             JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
             WHERE tc.constraint_name = 'valid_status'
             AND tc.table_name = 'user_subscriptions'
             AND cc.check_clause NOT LIKE '%paused%') THEN
    ALTER TABLE user_subscriptions DROP CONSTRAINT valid_status;
    ALTER TABLE user_subscriptions ADD CONSTRAINT valid_status CHECK (
      status IN ('active', 'canceled', 'past_due', 'unpaid', 'paused', 'trialing')
    );
  END IF;

END $$;

-- =============================================================================
-- STEP 3: ORDER MANAGEMENT
-- =============================================================================

-- Orders table for tracking all payment transactions
CREATE TABLE IF NOT EXISTS orders (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE SET NULL,

  -- Order details
  order_number VARCHAR(50) NOT NULL UNIQUE,
  status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (
    status IN ('pending', 'processing', 'completed', 'failed', 'canceled', 'refunded')
  ),

  -- Financial information
  subtotal DECIMAL(10,2) NOT NULL CHECK (subtotal >= 0),
  tax_amount DECIMAL(10,2) DEFAULT 0 CHECK (tax_amount >= 0),
  discount_amount DECIMAL(10,2) DEFAULT 0 CHECK (discount_amount >= 0),
  total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
  currency CHAR(3) DEFAULT 'EUR' CHECK (currency ~ '^[A-Z]{3}$'),

  -- Stripe integration
  stripe_payment_intent_id VARCHAR(255),
  stripe_invoice_id VARCHAR(255),

  -- Order metadata
  billing_address JSONB,
  metadata JSONB DEFAULT '{}'::jsonb,

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,

  -- Constraints
  CONSTRAINT valid_amounts CHECK (
    total_amount = subtotal + tax_amount - discount_amount
  )
);

-- =============================================================================
-- STEP 4: AUDIT LOGGING
-- =============================================================================

-- Audit log for subscription changes and important events
CREATE TABLE IF NOT EXISTS subscription_audit_log (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE SET NULL,
  order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
  
  -- Event details
  event_type VARCHAR(100) NOT NULL,
  event_description TEXT NOT NULL,
  
  -- Change tracking
  old_values JSONB,
  new_values JSONB,
  
  -- Context
  performed_by UUID REFERENCES users(id) ON DELETE SET NULL,
  ip_address INET,
  user_agent TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- STEP 5: INDEXES FOR PERFORMANCE
-- =============================================================================

-- Additional subscription plans indexes (some may already exist)
CREATE INDEX IF NOT EXISTS idx_subscription_plans_public ON subscription_plans(is_public) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_subscription_plans_stripe_monthly ON subscription_plans(stripe_price_id_monthly) WHERE stripe_price_id_monthly IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_subscription_plans_stripe_yearly ON subscription_plans(stripe_price_id_yearly) WHERE stripe_price_id_yearly IS NOT NULL;

-- Additional user subscriptions indexes (some may already exist)
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_billing_cycle ON user_subscriptions(billing_cycle);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_usage_reset ON user_subscriptions(api_requests_reset_at) WHERE api_requests_used > 0;

-- Orders indexes
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_orders_stripe_payment ON orders(stripe_payment_intent_id) WHERE stripe_payment_intent_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_orders_subscription ON orders(subscription_id) WHERE subscription_id IS NOT NULL;

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON subscription_audit_log(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_audit_log_subscription_id ON subscription_audit_log(subscription_id) WHERE subscription_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_audit_log_event_type ON subscription_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON subscription_audit_log(created_at DESC);

-- =============================================================================
-- STEP 6: TRIGGERS AND FUNCTIONS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at columns (create only if they don't exist)
DO $$
BEGIN
  -- Subscription plans trigger
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_subscription_plans_updated_at') THEN
    CREATE TRIGGER update_subscription_plans_updated_at
        BEFORE UPDATE ON subscription_plans
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;

  -- User subscriptions trigger
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_subscriptions_updated_at') THEN
    CREATE TRIGGER update_user_subscriptions_updated_at
        BEFORE UPDATE ON user_subscriptions
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;

  -- Orders trigger
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_orders_updated_at') THEN
    CREATE TRIGGER update_orders_updated_at
        BEFORE UPDATE ON orders
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

-- =============================================================================
-- STEP 7: UPDATE EXISTING SUBSCRIPTION PLANS
-- =============================================================================

-- Update existing subscription plans with enhanced features
DO $$
BEGIN
  -- Update existing plans with new features and pricing structure
  UPDATE subscription_plans SET
    features = CASE
      WHEN name = 'free' THEN '{"support": "community", "analytics": false, "webhooks": false}'::jsonb
      WHEN name = 'starter' THEN '{"support": "email", "analytics": true, "webhooks": true, "priority_support": false}'::jsonb
      WHEN name = 'professional' THEN '{"support": "priority", "analytics": true, "webhooks": true, "priority_support": true, "custom_limits": true}'::jsonb
      WHEN name = 'enterprise' THEN '{"support": "dedicated", "analytics": true, "webhooks": true, "priority_support": true, "custom_limits": true, "sla": true}'::jsonb
      ELSE features
    END,
    currency = 'EUR',
    is_public = true
  WHERE name IN ('free', 'starter', 'professional', 'enterprise');

  -- Insert any missing default plans (include all required columns)
  INSERT INTO subscription_plans (
    name, display_name, description,
    price_eur, billing_interval,
    api_requests_per_month, api_requests_per_minute,
    features, sort_order
  ) VALUES
    (
      'free', 'Free Plan', 'Perfect for testing and small projects',
      0.00, 'monthly',
      10000, 100,
      '{"support": "community", "analytics": false, "webhooks": false}'::jsonb,
      1
    ),
    (
      'starter', 'Starter Plan', 'Great for growing applications',
      29.00, 'monthly',
      100000, 500,
      '{"support": "email", "analytics": true, "webhooks": true, "priority_support": false}'::jsonb,
      2
    ),
    (
      'professional', 'Professional Plan', 'For high-volume applications',
      99.00, 'monthly',
      1000000, 2000,
      '{"support": "priority", "analytics": true, "webhooks": true, "priority_support": true, "custom_limits": true}'::jsonb,
      3
    ),
    (
      'enterprise', 'Enterprise Plan', 'Custom solutions for large organizations',
      299.00, 'monthly',
      10000000, 10000,
      '{"support": "dedicated", "analytics": true, "webhooks": true, "priority_support": true, "custom_limits": true, "sla": true}'::jsonb,
      4
    )
  ON CONFLICT (name) DO NOTHING;

END $$;

-- =============================================================================
-- STEP 8: VERIFICATION
-- =============================================================================

-- Verify tables were created successfully
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name IN ('subscription_plans', 'user_subscriptions', 'orders', 'subscription_audit_log');
    
    IF table_count != 4 THEN
        RAISE EXCEPTION 'Subscription management tables creation failed. Expected 4 tables, found %', table_count;
    END IF;
    
    RAISE INFO 'Subscription management schema created successfully with % tables', table_count;
END $$;
