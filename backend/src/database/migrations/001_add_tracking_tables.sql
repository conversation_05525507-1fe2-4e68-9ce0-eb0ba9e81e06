-- Tracking tables

CREATE TABLE IF NOT EXISTS tracking_snapshots (
  provider          VARCHAR(32) NOT NULL,
  tracking_number   VARCHAR(64) NOT NULL,
  status            VARCHAR(128) NOT NULL,
  last_event_time   TIMESTAMPTZ NOT NULL,
  snapshot          JSONB NOT NULL,
  updated_at        TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (provider, tracking_number)
);

CREATE TABLE IF NOT EXISTS tracking_events (
  id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider          VARCHAR(32) NOT NULL,
  tracking_number   VARCHAR(64) NOT NULL,
  status            VARCHAR(128) NOT NULL,
  location          VARCHAR(128),
  event_time        TIMESTAMPTZ NOT NULL,
  recorded_at       TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_tracking_events_number ON tracking_events(tracking_number); 