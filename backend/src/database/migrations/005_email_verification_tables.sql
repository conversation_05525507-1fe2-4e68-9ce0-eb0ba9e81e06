-- Migration: Email Verification and Password Reset Tables
-- Description: Verify and ensure email verification and password reset tables exist with correct schema
-- Note: Tables already exist from previous migrations, this migration ensures compatibility

-- Check if tables exist and have correct structure
DO $$
BEGIN
    -- Email verification tokens table already exists, verify structure
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'email_verification_tokens') THEN
        CREATE TABLE email_verification_tokens (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            token_hash TEXT NOT NULL UNIQUE,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            used_at TIMESTAMP WITH TIME ZONE
        );
    END IF;

    -- Password reset tokens table already exists, verify structure
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'password_reset_tokens') THEN
        CREATE TABLE password_reset_tokens (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            token_hash TEXT NOT NULL UNIQUE,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            used_at TIMESTAMP WITH TIME ZONE
        );
    END IF;
END $$;

-- Ensure indexes exist (using IF NOT EXISTS to avoid conflicts)
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_token_hash ON email_verification_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_email_verification_expires ON email_verification_tokens(expires_at) WHERE used_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON password_reset_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_password_reset_token_hash ON password_reset_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_expires ON password_reset_tokens(expires_at) WHERE used_at IS NULL;

-- Ensure triggers exist (function already exists from previous migrations)
DO $$
BEGIN
    -- Check if triggers exist, create if missing
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_email_verification_tokens_updated_at') THEN
        CREATE TRIGGER update_email_verification_tokens_updated_at
            BEFORE UPDATE ON email_verification_tokens
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_password_reset_tokens_updated_at') THEN
        CREATE TRIGGER update_password_reset_tokens_updated_at
            BEFORE UPDATE ON password_reset_tokens
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Ensure cleanup function exists (may already exist from previous migrations)
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Delete expired email verification tokens older than 7 days
    DELETE FROM email_verification_tokens
    WHERE expires_at < NOW() - INTERVAL '7 days' AND used_at IS NULL;

    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    -- Delete expired password reset tokens older than 1 day
    DELETE FROM password_reset_tokens
    WHERE expires_at < NOW() - INTERVAL '1 day' AND used_at IS NULL;

    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Success message
SELECT 'Email verification and password reset tables verified and updated' as status;
