-- Migration: 004_add_usage_analytics.sql
-- Description: Add comprehensive usage analytics and monitoring tables
-- Date: 2025-01-09

-- =============================================================================
-- <PERSON><PERSON> ANALYTICS TABLES
-- =============================================================================

-- API Request Analytics - Detailed tracking of every API request
CREATE TABLE IF NOT EXISTS api_request_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Request identification
    api_key_id UUID NOT NULL REFERENCES api_keys(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    request_id VARCHAR(255) NOT NULL,
    
    -- Request details
    endpoint VARCHAR(500) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER NOT NULL,
    request_size_bytes INTEGER DEFAULT 0,
    response_size_bytes INTEGER DEFAULT 0,
    
    -- Client information
    user_agent TEXT,
    ip_address INET,
    country_code VARCHAR(2),
    city VARCHAR(100),
    
    -- Timing information
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage Aggregations - Pre-computed usage statistics for performance
CREATE TABLE IF NOT EXISTS usage_aggregations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Aggregation scope
    api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Time period (minute, hour, day, month)
    period_type VARCHAR(10) NOT NULL CHECK (period_type IN ('minute', 'hour', 'day', 'month')),
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Usage metrics
    total_requests INTEGER NOT NULL DEFAULT 0,
    successful_requests INTEGER NOT NULL DEFAULT 0,
    failed_requests INTEGER NOT NULL DEFAULT 0,
    avg_response_time_ms DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_data_transferred_bytes BIGINT NOT NULL DEFAULT 0,
    
    -- Endpoint breakdown (JSON)
    endpoint_stats JSONB DEFAULT '{}',
    
    -- Status code breakdown (JSON)
    status_code_stats JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint to prevent duplicates
    UNIQUE(api_key_id, user_id, period_type, period_start)
);

-- Usage Alerts - Track usage thresholds and notifications
CREATE TABLE IF NOT EXISTS usage_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Alert scope
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
    
    -- Alert configuration
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('usage_threshold', 'rate_limit_exceeded', 'quota_exceeded', 'unusual_activity')),
    threshold_type VARCHAR(20) NOT NULL CHECK (threshold_type IN ('requests_per_minute', 'requests_per_day', 'requests_per_month', 'data_transfer')),
    threshold_value INTEGER NOT NULL,
    current_value INTEGER NOT NULL,
    
    -- Alert details
    message TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'critical')) DEFAULT 'warning',
    is_resolved BOOLEAN NOT NULL DEFAULT false,
    
    -- Notification tracking
    notification_sent BOOLEAN NOT NULL DEFAULT false,
    notification_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- System Performance Metrics - Track overall system health
CREATE TABLE IF NOT EXISTS system_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Time period
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Performance metrics
    total_requests INTEGER NOT NULL DEFAULT 0,
    avg_response_time_ms DECIMAL(10,2) NOT NULL DEFAULT 0,
    p95_response_time_ms DECIMAL(10,2) NOT NULL DEFAULT 0,
    p99_response_time_ms DECIMAL(10,2) NOT NULL DEFAULT 0,
    error_rate DECIMAL(5,4) NOT NULL DEFAULT 0, -- Percentage as decimal (0.05 = 5%)
    
    -- Resource utilization
    active_users INTEGER NOT NULL DEFAULT 0,
    active_api_keys INTEGER NOT NULL DEFAULT 0,
    
    -- Top endpoints (JSON)
    top_endpoints JSONB DEFAULT '[]',
    
    -- Error breakdown (JSON)
    error_breakdown JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Unique constraint
    UNIQUE(period_start, period_end)
);

-- Usage Quotas - Track subscription-based usage limits
CREATE TABLE IF NOT EXISTS usage_quotas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Quota scope
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE SET NULL,
    
    -- Quota period
    quota_period VARCHAR(10) NOT NULL CHECK (quota_period IN ('daily', 'monthly', 'yearly')),
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Quota limits
    requests_limit INTEGER NOT NULL,
    data_transfer_limit_bytes BIGINT NOT NULL DEFAULT 0,
    
    -- Current usage
    requests_used INTEGER NOT NULL DEFAULT 0,
    data_transfer_used_bytes BIGINT NOT NULL DEFAULT 0,
    
    -- Status
    is_exceeded BOOLEAN NOT NULL DEFAULT false,
    exceeded_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint
    UNIQUE(user_id, quota_period, period_start)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- API Request Analytics indexes
CREATE INDEX IF NOT EXISTS idx_api_request_analytics_api_key_created ON api_request_analytics(api_key_id, created_at);
CREATE INDEX IF NOT EXISTS idx_api_request_analytics_user_created ON api_request_analytics(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_api_request_analytics_endpoint ON api_request_analytics(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_request_analytics_status ON api_request_analytics(status_code);
CREATE INDEX IF NOT EXISTS idx_api_request_analytics_created_at ON api_request_analytics(created_at);

-- Usage Aggregations indexes
CREATE INDEX IF NOT EXISTS idx_usage_aggregations_api_key_period ON usage_aggregations(api_key_id, period_type, period_start);
CREATE INDEX IF NOT EXISTS idx_usage_aggregations_user_period ON usage_aggregations(user_id, period_type, period_start);
CREATE INDEX IF NOT EXISTS idx_usage_aggregations_period_start ON usage_aggregations(period_start);

-- Usage Alerts indexes
CREATE INDEX IF NOT EXISTS idx_usage_alerts_user_created ON usage_alerts(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_usage_alerts_api_key_created ON usage_alerts(api_key_id, created_at);
CREATE INDEX IF NOT EXISTS idx_usage_alerts_type_severity ON usage_alerts(alert_type, severity);
CREATE INDEX IF NOT EXISTS idx_usage_alerts_unresolved ON usage_alerts(is_resolved, created_at) WHERE is_resolved = false;

-- System Performance Metrics indexes
CREATE INDEX IF NOT EXISTS idx_system_performance_metrics_period ON system_performance_metrics(period_start, period_end);

-- Usage Quotas indexes
CREATE INDEX IF NOT EXISTS idx_usage_quotas_user_period ON usage_quotas(user_id, quota_period, period_start);
CREATE INDEX IF NOT EXISTS idx_usage_quotas_subscription ON usage_quotas(subscription_id);
CREATE INDEX IF NOT EXISTS idx_usage_quotas_exceeded ON usage_quotas(is_exceeded, period_start) WHERE is_exceeded = true;

-- =============================================================================
-- FUNCTIONS AND TRIGGERS
-- =============================================================================

-- Function to update usage aggregations
CREATE OR REPLACE FUNCTION update_usage_aggregations()
RETURNS TRIGGER AS $$
BEGIN
    -- Update minute aggregation
    INSERT INTO usage_aggregations (
        api_key_id, user_id, period_type, period_start, period_end,
        total_requests, successful_requests, failed_requests,
        avg_response_time_ms, total_data_transferred_bytes
    ) VALUES (
        NEW.api_key_id, NEW.user_id, 'minute',
        date_trunc('minute', NEW.created_at),
        date_trunc('minute', NEW.created_at) + INTERVAL '1 minute',
        1,
        CASE WHEN NEW.status_code < 400 THEN 1 ELSE 0 END,
        CASE WHEN NEW.status_code >= 400 THEN 1 ELSE 0 END,
        NEW.response_time_ms,
        NEW.request_size_bytes + NEW.response_size_bytes
    )
    ON CONFLICT (api_key_id, user_id, period_type, period_start)
    DO UPDATE SET
        total_requests = usage_aggregations.total_requests + 1,
        successful_requests = usage_aggregations.successful_requests + 
            CASE WHEN NEW.status_code < 400 THEN 1 ELSE 0 END,
        failed_requests = usage_aggregations.failed_requests + 
            CASE WHEN NEW.status_code >= 400 THEN 1 ELSE 0 END,
        avg_response_time_ms = (
            (usage_aggregations.avg_response_time_ms * usage_aggregations.total_requests + NEW.response_time_ms) / 
            (usage_aggregations.total_requests + 1)
        ),
        total_data_transferred_bytes = usage_aggregations.total_data_transferred_bytes + 
            NEW.request_size_bytes + NEW.response_size_bytes,
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update aggregations
CREATE TRIGGER trigger_update_usage_aggregations
    AFTER INSERT ON api_request_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_usage_aggregations();

-- Function to update usage quotas
CREATE OR REPLACE FUNCTION update_usage_quotas()
RETURNS TRIGGER AS $$
DECLARE
    quota_record RECORD;
BEGIN
    -- Update daily quota
    SELECT * INTO quota_record
    FROM usage_quotas
    WHERE user_id = NEW.user_id 
    AND quota_period = 'daily'
    AND period_start <= NEW.created_at 
    AND period_end > NEW.created_at;
    
    IF FOUND THEN
        UPDATE usage_quotas SET
            requests_used = requests_used + 1,
            data_transfer_used_bytes = data_transfer_used_bytes + 
                NEW.request_size_bytes + NEW.response_size_bytes,
            is_exceeded = (requests_used + 1) > requests_limit,
            exceeded_at = CASE 
                WHEN (requests_used + 1) > requests_limit AND exceeded_at IS NULL 
                THEN NOW() 
                ELSE exceeded_at 
            END,
            updated_at = NOW()
        WHERE id = quota_record.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update quotas
CREATE TRIGGER trigger_update_usage_quotas
    AFTER INSERT ON api_request_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_usage_quotas();

-- =============================================================================
-- INITIAL DATA AND CLEANUP
-- =============================================================================

-- Create initial usage quotas for existing users with active subscriptions
INSERT INTO usage_quotas (user_id, subscription_id, quota_period, period_start, period_end, requests_limit)
SELECT 
    us.user_id,
    us.id,
    'daily',
    date_trunc('day', NOW()),
    date_trunc('day', NOW()) + INTERVAL '1 day',
    sp.rate_limit_per_day
FROM user_subscriptions us
JOIN subscription_plans sp ON us.plan_id = sp.id
WHERE us.status = 'active'
ON CONFLICT (user_id, quota_period, period_start) DO NOTHING;

-- Add monthly quotas
INSERT INTO usage_quotas (user_id, subscription_id, quota_period, period_start, period_end, requests_limit)
SELECT
    us.user_id,
    us.id,
    'monthly',
    date_trunc('month', NOW()),
    date_trunc('month', NOW()) + INTERVAL '1 month',
    sp.rate_limit_per_day * 30
FROM user_subscriptions us
JOIN subscription_plans sp ON us.plan_id = sp.id
WHERE us.status = 'active'
ON CONFLICT (user_id, quota_period, period_start) DO NOTHING;
