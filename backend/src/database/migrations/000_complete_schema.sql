-- ================================================================================
-- POSTAL TERMINAL API - COMPLETE DATABASE SCHEMA
-- ================================================================================
-- Version: 2.1.0
-- Created: 2025-06-24
-- Description: Complete database setup for Postal Terminal API with PostGIS support
-- 
-- This single migration replaces all previous migrations and provides:
-- ✅ PostGIS and extensions setup
-- ✅ Complete table schema with all columns
-- ✅ Comprehensive indexes for performance
-- ✅ Database functions and triggers
-- ✅ Data validation and constraints
-- ✅ Self-verification and error checking
-- ================================================================================

-- =============================================================================
-- STEP 1: EXTENSIONS AND PREREQUISITES
-- =============================================================================

-- Enable PostGIS for geographic data support
CREATE EXTENSION IF NOT EXISTS postgis;
-- Enable trigram extension for fuzzy text search
CREATE EXTENSION IF NOT EXISTS pg_trgm;
-- Enable UUID generation functions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Verify critical extensions are installed
DO $$
BEGIN
    -- Check PostGIS
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'postgis') THEN
        RAISE EXCEPTION 'PostGIS extension failed to install. Please ensure PostGIS is available.';
    END IF;
    
    -- Check pg_trgm
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_trgm') THEN
        RAISE EXCEPTION 'pg_trgm extension failed to install. Please ensure PostgreSQL contrib package is installed.';
    END IF;
    
    -- Verify PostGIS functions are available
    PERFORM PostGIS_Version();
    
    RAISE INFO 'Extensions verified successfully: PostGIS %, pg_trgm', PostGIS_Version();
END $$;

-- =============================================================================
-- STEP 2: CORE TABLES
-- =============================================================================

-- Main terminals table with PostGIS geography support
CREATE TABLE IF NOT EXISTS terminals (
  -- Primary identification
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(500) NOT NULL,
  city VARCHAR(255) NOT NULL,
  address TEXT NOT NULL,
  postal_code VARCHAR(20),
  
  -- Geographic data using PostGIS
  coordinates GEOGRAPHY(POINT, 4326) NOT NULL,
  
  -- Metadata and classification
  country_code CHAR(2) DEFAULT 'LT',
  provider VARCHAR(50) DEFAULT 'UNKNOWN',
  terminal_type VARCHAR(50) DEFAULT 'PARCEL_LOCKER',
  metadata JSONB DEFAULT '{}'::jsonb,
  
  -- Status and lifecycle
  is_active BOOLEAN DEFAULT true,
  updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Performance and analytics
  search_vector tsvector,
  popularity_score INTEGER DEFAULT 0,
  last_accessed TIMESTAMP WITH TIME ZONE,
  
  -- Data integrity constraints
  CONSTRAINT valid_coordinates CHECK (ST_IsValid(coordinates::geometry)),
  CONSTRAINT valid_country_code CHECK (country_code ~ '^[A-Z]{2}$'),
  CONSTRAINT valid_popularity CHECK (popularity_score >= 0),
  CONSTRAINT valid_postal_code CHECK (postal_code IS NULL OR postal_code ~ '^\d{4,6}$'),
  CONSTRAINT valid_provider CHECK (provider IN ('LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK', 'UNKNOWN')),
  CONSTRAINT valid_terminal_type CHECK (terminal_type IN ('PARCEL_LOCKER', 'PICKUP_POINT', 'POST_OFFICE'))
);

-- API keys table for authentication and rate limiting
CREATE TABLE IF NOT EXISTS api_keys (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  key_hash VARCHAR(255) UNIQUE NOT NULL,
  tenant_id UUID DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
  name VARCHAR(255) NOT NULL,
  
  -- Status and subscription
  is_active BOOLEAN DEFAULT true,
  subscription_tier VARCHAR(50) DEFAULT 'FREE',
  
  -- Rate limiting configuration
  rate_limit_per_minute INTEGER DEFAULT 1000,
  rate_limit_per_day INTEGER DEFAULT 50000,
  rate_limit_burst INTEGER DEFAULT 2000,
  
  -- Usage tracking
  total_requests BIGINT DEFAULT 0,
  requests_this_month BIGINT DEFAULT 0,
  last_reset_date DATE DEFAULT CURRENT_DATE,
  
  -- Security and access control
  allowed_ips INET[],
  allowed_domains TEXT[],
  
  -- Audit and lifecycle
  last_used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  
  -- Constraints
  CONSTRAINT valid_subscription_tier CHECK (subscription_tier IN ('FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE')),
  CONSTRAINT valid_rate_limits CHECK (
    rate_limit_per_minute > 0 AND 
    rate_limit_per_day > 0 AND 
    rate_limit_burst > 0
  )
);

-- PostgreSQL-based cache table
CREATE TABLE IF NOT EXISTS api_cache (
  -- Composite primary key for multi-tenant caching
  cache_key VARCHAR(255) NOT NULL,
  tenant_id UUID DEFAULT '00000000-0000-0000-0000-000000000000'::uuid,
  
  -- Cache data and expiration
  cache_value JSONB NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  PRIMARY KEY (cache_key, tenant_id),
  
  -- Ensure expiration is in the future
  CONSTRAINT valid_expiration CHECK (expires_at > created_at)
);

-- Multi-tenant support table (future-proofing)
CREATE TABLE IF NOT EXISTS tenants (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  
  -- Subscription management
  subscription_tier VARCHAR(50) DEFAULT 'FREE',
  subscription_status VARCHAR(50) DEFAULT 'ACTIVE',
  subscription_start_date DATE DEFAULT CURRENT_DATE,
  subscription_end_date DATE,
  
  -- Billing information
  billing_email VARCHAR(255),
  billing_address JSONB,
  
  -- Configuration
  settings JSONB DEFAULT '{}'::jsonb,
  custom_rate_limits JSONB DEFAULT '{}'::jsonb,
  
  -- Audit
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_subscription_tier CHECK (subscription_tier IN ('FREE', 'BASIC', 'PREMIUM', 'ENTERPRISE')),
  CONSTRAINT valid_subscription_status CHECK (subscription_status IN ('ACTIVE', 'SUSPENDED', 'CANCELLED')),
  CONSTRAINT valid_slug CHECK (slug ~ '^[a-z0-9-]+$')
);

-- =============================================================================
-- STEP 3: DATABASE FUNCTIONS
-- =============================================================================

-- Function to automatically update search vector for full-text search
CREATE OR REPLACE FUNCTION update_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := to_tsvector('simple',
    COALESCE(NEW.name, '') || ' ' ||
    COALESCE(NEW.city, '') || ' ' ||
    COALESCE(NEW.address, '') || ' ' ||
    COALESCE(NEW.postal_code, '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to clean expired cache entries
CREATE OR REPLACE FUNCTION clean_expired_cache()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM api_cache WHERE expires_at < NOW();
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update tenant timestamp automatically
CREATE OR REPLACE FUNCTION update_tenant_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to increment API key usage counters
CREATE OR REPLACE FUNCTION increment_api_key_usage(
  key_id UUID,
  increment_total BOOLEAN DEFAULT true,
  increment_monthly BOOLEAN DEFAULT true
)
RETURNS VOID AS $$
BEGIN
  UPDATE api_keys
  SET
    total_requests = CASE WHEN increment_total THEN total_requests + 1 ELSE total_requests END,
    requests_this_month = CASE WHEN increment_monthly THEN requests_this_month + 1 ELSE requests_this_month END,
    last_used_at = NOW()
  WHERE id = key_id;
END;
$$ LANGUAGE plpgsql;

-- Function to reset monthly usage counters (for scheduled tasks)
CREATE OR REPLACE FUNCTION reset_monthly_usage()
RETURNS INTEGER AS $$
DECLARE
  reset_count INTEGER;
BEGIN
  UPDATE api_keys
  SET
    requests_this_month = 0,
    last_reset_date = CURRENT_DATE
  WHERE last_reset_date < CURRENT_DATE;
  
  GET DIAGNOSTICS reset_count = ROW_COUNT;
  RETURN reset_count;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate distance between terminal and search point
CREATE OR REPLACE FUNCTION get_terminal_distance(
  terminal_coords GEOGRAPHY,
  search_lat DOUBLE PRECISION,
  search_lng DOUBLE PRECISION
)
RETURNS DOUBLE PRECISION AS $$
BEGIN
  RETURN ST_Distance(
    terminal_coords,
    ST_SetSRID(ST_MakePoint(search_lng, search_lat), 4326)::geography
  ) / 1000; -- Convert to kilometers
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to standardize postal codes
CREATE OR REPLACE FUNCTION standardize_postal_code(
  postal_code TEXT,
  provider TEXT DEFAULT 'UNKNOWN'
)
RETURNS TEXT AS $$
DECLARE
  cleaned TEXT;
BEGIN
  IF postal_code IS NULL OR postal_code = '' THEN
    RETURN NULL;
  END IF;
  
  cleaned := trim(postal_code);
  
  -- Remove country prefix for DPD data
  IF provider = 'DPD' AND cleaned LIKE 'LT-%' THEN
    cleaned := substring(cleaned from 4);
  ELSIF provider = 'DPD' AND cleaned LIKE 'LT%' AND length(cleaned) > 2 THEN
    cleaned := substring(cleaned from 3);
  END IF;
  
  -- Remove any remaining non-numeric characters
  cleaned := regexp_replace(cleaned, '[^0-9]', '', 'g');
  
  -- Validate length
  IF length(cleaned) < 4 OR length(cleaned) > 6 THEN
    RETURN NULL;
  END IF;
  
  RETURN cleaned;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =============================================================================
-- STEP 4: TRIGGERS
-- =============================================================================

-- Trigger to automatically update search vector on terminal changes
DROP TRIGGER IF EXISTS trigger_update_search_vector ON terminals;
CREATE TRIGGER trigger_update_search_vector
  BEFORE INSERT OR UPDATE ON terminals
  FOR EACH ROW EXECUTE FUNCTION update_search_vector();

-- Trigger to automatically update tenant timestamp
DROP TRIGGER IF EXISTS trigger_update_tenant_timestamp ON tenants;
CREATE TRIGGER trigger_update_tenant_timestamp
  BEFORE UPDATE ON tenants
  FOR EACH ROW EXECUTE FUNCTION update_tenant_timestamp();

-- =============================================================================
-- STEP 5: PERFORMANCE INDEXES
-- =============================================================================

-- Core geographic and search indexes
CREATE INDEX IF NOT EXISTS idx_terminals_coordinates_gist 
ON terminals USING GIST (coordinates) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_terminals_search_vector 
ON terminals USING GIN (search_vector) WHERE is_active = true;

-- Covering indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_terminals_city_covering 
ON terminals (country_code, city, is_active) 
INCLUDE (id, name, address, postal_code, updated) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_terminals_provider_covering
ON terminals (provider, is_active)
INCLUDE (id, name, city, address, postal_code, coordinates, updated) WHERE is_active = true;

-- Trigram indexes for fuzzy search
CREATE INDEX IF NOT EXISTS idx_terminals_name_trgm 
ON terminals USING GIN (name gin_trgm_ops) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_terminals_city_trgm 
ON terminals USING GIN (city gin_trgm_ops) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_terminals_address_trgm 
ON terminals USING GIN (address gin_trgm_ops) WHERE is_active = true;

-- API keys indexes
CREATE INDEX IF NOT EXISTS idx_api_keys_hash ON api_keys (key_hash);
CREATE INDEX IF NOT EXISTS idx_api_keys_tenant_active 
ON api_keys (tenant_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_api_keys_usage 
ON api_keys (subscription_tier, requests_this_month);

-- Cache indexes
CREATE INDEX IF NOT EXISTS idx_cache_tenant_expires 
ON api_cache (tenant_id, expires_at);
CREATE INDEX IF NOT EXISTS idx_cache_expires 
ON api_cache (expires_at);

-- Tenant indexes
CREATE INDEX IF NOT EXISTS idx_tenants_slug 
ON tenants (slug) WHERE subscription_status = 'ACTIVE';
CREATE INDEX IF NOT EXISTS idx_tenants_subscription 
ON tenants (subscription_tier, subscription_status);

-- =============================================================================
-- STEP 6: DATA SEEDING (DEFAULT TENANT)
-- =============================================================================

-- Insert default tenant for single-tenant deployments
INSERT INTO tenants (id, name, slug, subscription_tier, subscription_status)
VALUES (
  '00000000-0000-0000-0000-000000000000'::uuid,
  'Default Tenant',
  'default',
  'ENTERPRISE',
  'ACTIVE'
) ON CONFLICT (id) DO NOTHING;

-- =============================================================================
-- STEP 7: VERIFICATION AND HEALTH CHECKS
-- =============================================================================

-- Verify all tables were created successfully
DO $$
DECLARE
    table_count INTEGER;
    missing_tables TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- Check terminals table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'terminals') THEN
        missing_tables := array_append(missing_tables, 'terminals');
    END IF;
    
    -- Check api_keys table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'api_keys') THEN
        missing_tables := array_append(missing_tables, 'api_keys');
    END IF;
    
    -- Check api_cache table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'api_cache') THEN
        missing_tables := array_append(missing_tables, 'api_cache');
    END IF;
    
    -- Check tenants table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tenants') THEN
        missing_tables := array_append(missing_tables, 'tenants');
    END IF;
    
    -- Report results
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION 'Database schema verification failed! Missing tables: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE INFO 'Database schema verification successful! All tables created.';
    END IF;
END $$;

-- Verify PostGIS functionality with a test query
DO $$
DECLARE
    test_point GEOGRAPHY;
BEGIN
    -- Test PostGIS point creation and distance calculation
    test_point := ST_SetSRID(ST_MakePoint(25.2797, 54.6872), 4326)::geography;
    
    -- Test distance calculation (should not throw error)
    PERFORM ST_Distance(test_point, test_point);
    
    RAISE INFO 'PostGIS functionality verified successfully!';
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'PostGIS verification failed: %', SQLERRM;
END $$;

-- Final success message
SELECT 
  'Postal Terminal API Database Schema' as component,
  'SUCCESS' as status,
  'Complete schema with PostGIS, indexes, and functions ready for production!' as message,
  NOW() as completed_at; 