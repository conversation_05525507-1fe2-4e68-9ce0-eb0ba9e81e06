-- ================================================================================
-- SAAS AUTHENTICATION SYSTEM - PHASE 1
-- ================================================================================
-- Version: 1.0
-- Created: 2025-07-09
-- Description: Core authentication system for SaaS transformation
-- 
-- This migration adds:
-- ✅ Users table with email/password and Google OAuth support
-- ✅ JWT refresh tokens table
-- ✅ Enhanced API keys linking to users
-- ✅ Comprehensive indexes for performance
-- ✅ Database functions for user management
-- ================================================================================

-- =============================================================================
-- STEP 1: USER MANAGEMENT TABLES
-- =============================================================================

-- Update existing users table for SaaS authentication
-- The users table already exists, so we'll modify it to add the role column
DO $$
BEGIN
  -- Add role column if it doesn't exist (convert from is_admin)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'role') THEN
    -- Add role column
    ALTER TABLE users ADD COLUMN role TEXT;

    -- Set role based on existing is_admin column
    UPDATE users SET role = CASE WHEN is_admin = TRUE THEN 'ADMIN' ELSE 'CUSTOMER' END;

    -- Make role NOT NULL and add constraint
    ALTER TABLE users ALTER COLUMN role SET NOT NULL;
    ALTER TABLE users ADD CONSTRAINT users_role_check CHECK (role IN ('ADMIN', 'CUSTOMER'));

    -- Set default for new users
    ALTER TABLE users ALTER COLUMN role SET DEFAULT 'CUSTOMER';
  END IF;

  -- Ensure other required columns exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_login_at') THEN
    ALTER TABLE users ADD COLUMN last_login_at TIMESTAMPTZ;
  END IF;

  -- Update password constraint to allow OAuth users
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'password_or_oauth') THEN
    ALTER TABLE users ADD CONSTRAINT password_or_oauth CHECK (password_hash IS NOT NULL OR google_id IS NOT NULL);
  END IF;
END $$;

-- JWT refresh tokens for secure session management
CREATE TABLE IF NOT EXISTS refresh_tokens (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Token data
  token_hash TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMPTZ NOT NULL,
  
  -- Security tracking
  created_at TIMESTAMPTZ DEFAULT NOW(),
  last_used_at TIMESTAMPTZ,
  user_agent TEXT,
  ip_address INET,
  
  -- Status
  is_revoked BOOLEAN DEFAULT FALSE,
  revoked_at TIMESTAMPTZ,
  revoked_reason TEXT,
  
  -- Constraints
  CONSTRAINT valid_expiration CHECK (expires_at > created_at)
);

-- Email verification tokens
CREATE TABLE IF NOT EXISTS email_verification_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  token_hash TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  used_at TIMESTAMPTZ,
  
  CONSTRAINT valid_token_expiration CHECK (expires_at > created_at)
);

-- Password reset tokens
CREATE TABLE IF NOT EXISTS password_reset_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  token_hash TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  used_at TIMESTAMPTZ,
  
  CONSTRAINT valid_reset_expiration CHECK (expires_at > created_at)
);

-- =============================================================================
-- STEP 2: ENHANCE EXISTING API KEYS TABLE
-- =============================================================================

-- Link existing api_keys to users (preserve existing functionality)
DO $$
BEGIN
  -- Add user relationship columns if they don't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'user_id') THEN
    ALTER TABLE api_keys ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE SET NULL;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'created_by_user_id') THEN
    ALTER TABLE api_keys ADD COLUMN created_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL;
  END IF;
  
  -- Add description field for better API key management
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'description') THEN
    ALTER TABLE api_keys ADD COLUMN description TEXT;
  END IF;
END $$;

-- =============================================================================
-- STEP 3: DATABASE FUNCTIONS
-- =============================================================================

-- Function to automatically update user timestamp
CREATE OR REPLACE FUNCTION update_user_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to clean expired tokens
CREATE OR REPLACE FUNCTION clean_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
  refresh_deleted INTEGER;
  verification_deleted INTEGER;
  reset_deleted INTEGER;
BEGIN
  -- Clean expired refresh tokens
  DELETE FROM refresh_tokens WHERE expires_at < NOW() OR is_revoked = TRUE;
  GET DIAGNOSTICS refresh_deleted = ROW_COUNT;
  
  -- Clean expired email verification tokens
  DELETE FROM email_verification_tokens WHERE expires_at < NOW() OR used_at IS NOT NULL;
  GET DIAGNOSTICS verification_deleted = ROW_COUNT;
  
  -- Clean expired password reset tokens
  DELETE FROM password_reset_tokens WHERE expires_at < NOW() OR used_at IS NOT NULL;
  GET DIAGNOSTICS reset_deleted = ROW_COUNT;
  
  deleted_count := refresh_deleted + verification_deleted + reset_deleted;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to revoke all user tokens (for security)
CREATE OR REPLACE FUNCTION revoke_all_user_tokens(target_user_id UUID, reason TEXT DEFAULT 'Security revocation')
RETURNS INTEGER AS $$
DECLARE
  revoked_count INTEGER;
BEGIN
  UPDATE refresh_tokens 
  SET 
    is_revoked = TRUE,
    revoked_at = NOW(),
    revoked_reason = reason
  WHERE user_id = target_user_id AND is_revoked = FALSE;
  
  GET DIAGNOSTICS revoked_count = ROW_COUNT;
  
  RETURN revoked_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- STEP 4: TRIGGERS
-- =============================================================================

-- Trigger to automatically update user timestamp
DROP TRIGGER IF EXISTS trigger_update_user_timestamp ON users;
CREATE TRIGGER trigger_update_user_timestamp
  BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_user_timestamp();

-- =============================================================================
-- STEP 5: PERFORMANCE INDEXES
-- =============================================================================

-- User authentication indexes (only create if users table and columns exist)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
    -- Check each column exists before creating index
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'email') THEN
      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email) WHERE is_active = TRUE;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'google_id') THEN
      CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id) WHERE google_id IS NOT NULL;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'role')
       AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_active') THEN
      CREATE INDEX IF NOT EXISTS idx_users_role_active ON users(role, is_active);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'email_verified')
       AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_active') THEN
      CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified, is_active);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'created_at') THEN
      CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
    END IF;
  END IF;
END $$;

-- Refresh token indexes (only create if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'refresh_tokens') THEN
    CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id);
    CREATE INDEX IF NOT EXISTS idx_refresh_tokens_token_hash ON refresh_tokens(token_hash);
    CREATE INDEX IF NOT EXISTS idx_refresh_tokens_expires_at ON refresh_tokens(expires_at);
    CREATE INDEX IF NOT EXISTS idx_refresh_tokens_active ON refresh_tokens(user_id, is_revoked) WHERE is_revoked = FALSE;
  END IF;
END $$;

-- Email verification token indexes (only create if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'email_verification_tokens') THEN
    CREATE INDEX IF NOT EXISTS idx_email_verification_user_id ON email_verification_tokens(user_id);
    CREATE INDEX IF NOT EXISTS idx_email_verification_token_hash ON email_verification_tokens(token_hash);
    CREATE INDEX IF NOT EXISTS idx_email_verification_expires ON email_verification_tokens(expires_at) WHERE used_at IS NULL;
  END IF;
END $$;

-- Password reset token indexes (only create if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'password_reset_tokens') THEN
    CREATE INDEX IF NOT EXISTS idx_password_reset_user_id ON password_reset_tokens(user_id);
    CREATE INDEX IF NOT EXISTS idx_password_reset_token_hash ON password_reset_tokens(token_hash);
    CREATE INDEX IF NOT EXISTS idx_password_reset_expires ON password_reset_tokens(expires_at) WHERE used_at IS NULL;
  END IF;
END $$;

-- Enhanced API key indexes for user relationships (only create if columns exist)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'user_id') THEN
    CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id) WHERE user_id IS NOT NULL;
  END IF;

  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'created_by_user_id') THEN
    CREATE INDEX IF NOT EXISTS idx_api_keys_created_by ON api_keys(created_by_user_id) WHERE created_by_user_id IS NOT NULL;
  END IF;
END $$;

-- =============================================================================
-- STEP 6: SEED DATA
-- =============================================================================

-- Create default admin user (password: admin123 - should be changed in production)
-- Only insert if role column exists and no admin user exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'role') THEN
    INSERT INTO users (
      email,
      password_hash,
      role,
      is_active,
      email_verified,
      first_name,
      last_name
    ) VALUES (
      '<EMAIL>',
      '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlDO', -- bcrypt hash of 'admin123'
      'ADMIN',
      TRUE,
      TRUE,
      'System',
      'Administrator'
    ) ON CONFLICT (email) DO NOTHING;
  END IF;
END $$;

-- =============================================================================
-- STEP 7: VERIFICATION
-- =============================================================================

-- Verify all tables were created successfully
DO $$
DECLARE
    table_count INTEGER;
    missing_tables TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- Check users table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        missing_tables := array_append(missing_tables, 'users');
    END IF;
    
    -- Check refresh_tokens table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'refresh_tokens') THEN
        missing_tables := array_append(missing_tables, 'refresh_tokens');
    END IF;
    
    -- Check email_verification_tokens table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'email_verification_tokens') THEN
        missing_tables := array_append(missing_tables, 'email_verification_tokens');
    END IF;
    
    -- Check password_reset_tokens table
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'password_reset_tokens') THEN
        missing_tables := array_append(missing_tables, 'password_reset_tokens');
    END IF;
    
    -- Report results
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION 'SaaS Authentication schema verification failed! Missing tables: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE INFO 'SaaS Authentication schema verification successful! All tables created.';
    END IF;
END $$;

-- Final success message
SELECT 
  'SaaS Authentication System' as component,
  'SUCCESS' as status,
  'Core authentication tables, functions, and indexes ready for SaaS transformation!' as message,
  NOW() as completed_at;
