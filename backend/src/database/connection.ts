import { Pool, PoolClient } from 'pg';
import { validateEnvironment, CONFIG } from '../config';

// Logger for structured logging
const logger = {
  info: (message: string) => console.log(`[${new Date().toISOString()}] INFO: ${message}`),
  error: (message: string, error?: any) => console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error),
  debug: (message: string) => console.log(`[${new Date().toISOString()}] DEBUG: ${message}`)
};

// Database connection pool
let pool: Pool | null = null;

// Connection pool statistics
interface PoolStats {
  totalConnections: number;
  idleConnections: number;
  waitingClients: number;
  totalCount: number;
  idleCount: number;
  waitingCount: number;
}

// Track connection events for monitoring
const connectionStats = {
  totalCreated: 0,
  totalAcquired: 0,
  totalReleased: 0,
  totalErrors: 0,
  lastActivity: new Date()
};

export function createDatabasePool(): Pool {
  if (pool) {
    return pool;
  }

  const env = validateEnvironment();

  pool = new Pool({
    connectionString: env.DATABASE_URL,
    max: CONFIG.database.maxConnections,
    min: CONFIG.database.minConnections,
    idleTimeoutMillis: CONFIG.database.idleTimeout,
    connectionTimeoutMillis: CONFIG.database.connectionTimeout,
    statement_timeout: CONFIG.database.statementTimeout,
    query_timeout: CONFIG.database.queryTimeout,
    // SSL configuration - can be controlled via environment variable
    ssl: env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false,
    // Additional pool configuration for better connection management
    allowExitOnIdle: false, // Keep pool alive
    maxUses: 7500, // Maximum uses per connection before replacement
    keepAlive: true, // Enable TCP keep-alive
    keepAliveInitialDelayMillis: 10000 // 10 seconds
  });

  // Handle pool errors
  pool.on('error', async (err) => {
    connectionStats.totalErrors++;
    logger.error('Unexpected error on idle client', err);

    // Notify admin of critical database error before exit
    try {
      const { getNotificationService } = await import('../services/notification-service');
      const notificationService = getNotificationService();
      await notificationService.notifyDatabaseIssue('connection_pool_error', err.message);
    } catch (notificationError) {
      logger.error('Failed to send database error notification:', notificationError);
    }

    process.exit(-1);
  });

  // Enhanced connection event logging
  pool.on('connect', () => {
    connectionStats.totalCreated++;
    connectionStats.lastActivity = new Date();

    // Only log new physical connections in development, and less frequently
    if (env.NODE_ENV === 'development') {
      // Log every 10th connection, or if it's been more than 2 minutes since last log
      const timeSinceLastLog = Date.now() - connectionStats.lastActivity.getTime();
      const shouldLog = connectionStats.totalCreated % 10 === 1 || timeSinceLastLog > 120000;

      if (shouldLog) {
        logger.info(`🔗 Database connection pool: ${connectionStats.totalCreated} total connections created (pool expanding due to load)`);
      }
    }
  });

  pool.on('acquire', () => {
    connectionStats.totalAcquired++;
    connectionStats.lastActivity = new Date();
  });

  pool.on('release', () => {
    connectionStats.totalReleased++;
    connectionStats.lastActivity = new Date();
  });

  // Log initial pool creation
  logger.info(`🏊 Database connection pool created (max: ${CONFIG.database.maxConnections}, min: ${CONFIG.database.minConnections}, idle timeout: ${CONFIG.database.idleTimeout}ms)`);

  return pool;
}

export function getDatabasePool(): Pool {
  if (!pool) {
    throw new Error('Database pool not initialized. Call createDatabasePool() first.');
  }
  return pool;
}

export async function closeDatabasePool(): Promise<void> {
  if (pool) {
    const stats = getConnectionStats();
    logger.info(`🔌 Database pool closing - Final stats: Created: ${stats.totalCreated}, Acquired: ${stats.totalAcquired}, Released: ${stats.totalReleased}, Errors: ${stats.totalErrors}`);
    await pool.end();
    pool = null;
    logger.info('🔌 Database pool closed');
  }
}

// Get current connection pool statistics
export function getPoolStats(): PoolStats | null {
  if (!pool) {
    return null;
  }

  return {
    totalConnections: pool.totalCount,
    idleConnections: pool.idleCount,
    waitingClients: pool.waitingCount,
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount
  };
}

// Get connection statistics
export function getConnectionStats() {
  return { ...connectionStats };
}

// Log current pool status (useful for debugging)
export function logPoolStatus(): void {
  const poolStats = getPoolStats();
  const connStats = getConnectionStats();

  if (poolStats) {
    logger.info(`📊 Pool Status - Total: ${poolStats.totalCount}, Idle: ${poolStats.idleCount}, Waiting: ${poolStats.waitingCount}`);
    logger.info(`📈 Connection Stats - Created: ${connStats.totalCreated}, Acquired: ${connStats.totalAcquired}, Released: ${connStats.totalReleased}, Errors: ${connStats.totalErrors}`);
  } else {
    logger.info('📊 Pool Status - Pool not initialized');
  }
}

// Helper function for transactions
export async function withTransaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await getDatabasePool().connect();

  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Optimized transaction wrapper for bulk operations
export async function withBulkTransaction<T>(
  callback: (pool: Pool) => Promise<T>
): Promise<T> {
  const pool = getDatabasePool();

  try {
    await pool.query('BEGIN');
    const result = await callback(pool);
    await pool.query('COMMIT');
    return result;
  } catch (error) {
    await pool.query('ROLLBACK');
    throw error;
  }
}

// Health check function with connection reuse optimization
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const pool = getDatabasePool();
    const result = await pool.query('SELECT 1 as health_check');
    return result.rows[0]?.health_check === 1;
  } catch (error) {
    connectionStats.totalErrors++;
    logger.error('Database health check failed:', error);
    return false;
  }
}

// PostGIS health check with connection reuse optimization
export async function checkPostGISHealth(): Promise<boolean> {
  try {
    const pool = getDatabasePool();
    const result = await pool.query('SELECT PostGIS_Version() as version');
    return !!result.rows[0]?.version;
  } catch (error) {
    connectionStats.totalErrors++;
    logger.error('PostGIS health check failed:', error);
    return false;
  }
}

// Combined health check to reduce connection usage
export async function checkDatabaseAndPostGISHealth(): Promise<{ database: boolean; postGIS: boolean }> {
  try {
    const client = await getDatabasePool().connect();
    try {
      // Run both checks in a single connection
      const [dbResult, postGISResult] = await Promise.all([
        client.query('SELECT 1 as health_check'),
        client.query('SELECT PostGIS_Version() as version')
      ]);

      return {
        database: dbResult.rows[0]?.health_check === 1,
        postGIS: !!postGISResult.rows[0]?.version
      };
    } finally {
      client.release();
    }
  } catch (error) {
    connectionStats.totalErrors++;
    logger.error('Combined health check failed:', error);
    return { database: false, postGIS: false };
  }
}
