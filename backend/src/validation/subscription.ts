/**
 * Subscription Management Validation Schemas
 * 
 * Zod validation schemas for subscription plans, user subscriptions,
 * orders, and related API endpoints.
 */

import { z } from 'zod';

// =============================================================================
// SUBSCRIPTION PLANS
// =============================================================================

export const createSubscriptionPlanSchema = z.object({
  name: z.string()
    .min(1, 'Plan name is required')
    .max(100, 'Plan name must be less than 100 characters')
    .regex(/^[a-z0-9_-]+$/, 'Plan name must contain only lowercase letters, numbers, underscores, and hyphens'),
  display_name: z.string()
    .min(1, 'Display name is required')
    .max(200, 'Display name must be less than 200 characters'),
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  price_monthly: z.number()
    .min(0, 'Monthly price must be non-negative')
    .max(99999.99, 'Monthly price is too high'),
  price_yearly: z.number()
    .min(0, 'Yearly price must be non-negative')
    .max(999999.99, 'Yearly price is too high')
    .optional(),
  currency: z.string()
    .length(3, 'Currency must be a 3-letter code')
    .regex(/^[A-Z]{3}$/, 'Currency must be uppercase')
    .default('USD'),
  api_requests_per_month: z.number()
    .int('API requests per month must be an integer')
    .min(1, 'API requests per month must be at least 1')
    .max(1000000000, 'API requests per month limit exceeded'),
  api_requests_per_minute: z.number()
    .int('API requests per minute must be an integer')
    .min(1, 'API requests per minute must be at least 1')
    .max(100000, 'API requests per minute limit exceeded'),
  max_api_keys: z.number()
    .int('Max API keys must be an integer')
    .min(1, 'Max API keys must be at least 1')
    .max(1000, 'Max API keys limit exceeded')
    .default(1),
  features: z.record(z.any()).default({}),
  is_public: z.boolean().default(true),
  sort_order: z.number().int().min(0).default(0)
});

export const updateSubscriptionPlanSchema = z.object({
  display_name: z.string()
    .min(1, 'Display name is required')
    .max(200, 'Display name must be less than 200 characters')
    .optional(),
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  price_monthly: z.number()
    .min(0, 'Monthly price must be non-negative')
    .max(99999.99, 'Monthly price is too high')
    .optional(),
  price_yearly: z.number()
    .min(0, 'Yearly price must be non-negative')
    .max(999999.99, 'Yearly price is too high')
    .optional(),
  api_requests_per_month: z.number()
    .int('API requests per month must be an integer')
    .min(1, 'API requests per month must be at least 1')
    .max(1000000000, 'API requests per month limit exceeded')
    .optional(),
  api_requests_per_minute: z.number()
    .int('API requests per minute must be an integer')
    .min(1, 'API requests per minute must be at least 1')
    .max(100000, 'API requests per minute limit exceeded')
    .optional(),
  max_api_keys: z.number()
    .int('Max API keys must be an integer')
    .min(1, 'Max API keys must be at least 1')
    .max(1000, 'Max API keys limit exceeded')
    .optional(),
  features: z.record(z.any()).optional(),
  is_active: z.boolean().optional(),
  is_public: z.boolean().optional(),
  sort_order: z.number().int().min(0).optional()
});

export const planIdParamSchema = z.object({
  id: z.string().uuid('Invalid plan ID format')
});

// =============================================================================
// USER SUBSCRIPTIONS
// =============================================================================

export const createSubscriptionSchema = z.object({
  plan_id: z.string().uuid('Invalid plan ID format'),
  billing_cycle: z.enum(['monthly', 'yearly'], {
    errorMap: () => ({ message: 'Billing cycle must be monthly or yearly' })
  }),
  trial_days: z.number()
    .int('Trial days must be an integer')
    .min(0, 'Trial days must be non-negative')
    .max(365, 'Trial days cannot exceed 365')
    .optional(),
  stripe_payment_method_id: z.string()
    .min(1, 'Payment method ID is required')
    .optional()
});

export const updateSubscriptionSchema = z.object({
  plan_id: z.string().uuid('Invalid plan ID format').optional(),
  billing_cycle: z.enum(['monthly', 'yearly'], {
    errorMap: () => ({ message: 'Billing cycle must be monthly or yearly' })
  }).optional(),
  status: z.enum(['active', 'canceled', 'past_due', 'unpaid', 'paused', 'trialing'], {
    errorMap: () => ({ message: 'Invalid subscription status' })
  }).optional()
});

export const subscriptionListQuerySchema = z.object({
  status: z.enum(['active', 'canceled', 'past_due', 'unpaid', 'paused', 'trialing']).optional(),
  plan_id: z.string().uuid().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sort_by: z.enum(['created_at', 'updated_at', 'current_period_end']).default('created_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc')
});

// =============================================================================
// ORDERS
// =============================================================================

export const createOrderSchema = z.object({
  subscription_id: z.string().uuid('Invalid subscription ID format').optional(),
  subtotal: z.number()
    .min(0, 'Subtotal must be non-negative')
    .max(999999.99, 'Subtotal is too high'),
  tax_amount: z.number()
    .min(0, 'Tax amount must be non-negative')
    .max(99999.99, 'Tax amount is too high')
    .default(0),
  discount_amount: z.number()
    .min(0, 'Discount amount must be non-negative')
    .max(99999.99, 'Discount amount is too high')
    .default(0),
  billing_address: z.record(z.any()).optional(),
  metadata: z.record(z.any()).default({})
});

export const orderListQuerySchema = z.object({
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'canceled', 'refunded']).optional(),
  user_id: z.string().uuid().optional(),
  subscription_id: z.string().uuid().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sort_by: z.enum(['created_at', 'updated_at', 'completed_at']).default('created_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional()
});

export const orderIdParamSchema = z.object({
  id: z.string().uuid('Invalid order ID format')
});

// =============================================================================
// STRIPE WEBHOOKS
// =============================================================================

export const stripeWebhookSchema = z.object({
  id: z.string(),
  type: z.string(),
  data: z.object({
    object: z.any()
  }),
  created: z.number()
});

// =============================================================================
// USAGE TRACKING
// =============================================================================

export const usageUpdateSchema = z.object({
  api_requests_increment: z.number()
    .int('API requests increment must be an integer')
    .min(0, 'API requests increment must be non-negative')
    .max(1000000, 'API requests increment is too high')
});

// =============================================================================
// AUDIT LOGGING
// =============================================================================

export const createAuditLogSchema = z.object({
  user_id: z.string().uuid().optional(),
  subscription_id: z.string().uuid().optional(),
  order_id: z.string().uuid().optional(),
  event_type: z.string()
    .min(1, 'Event type is required')
    .max(100, 'Event type must be less than 100 characters'),
  event_description: z.string()
    .min(1, 'Event description is required')
    .max(1000, 'Event description must be less than 1000 characters'),
  old_values: z.record(z.any()).optional(),
  new_values: z.record(z.any()).optional(),
  performed_by: z.string().uuid().optional(),
  ip_address: z.string().ip().optional(),
  user_agent: z.string().max(500).optional()
});

// =============================================================================
// TYPE EXPORTS
// =============================================================================

export type CreateSubscriptionPlanRequest = z.infer<typeof createSubscriptionPlanSchema>;
export type UpdateSubscriptionPlanRequest = z.infer<typeof updateSubscriptionPlanSchema>;
export type PlanIdParam = z.infer<typeof planIdParamSchema>;

export type CreateSubscriptionRequest = z.infer<typeof createSubscriptionSchema>;
export type UpdateSubscriptionRequest = z.infer<typeof updateSubscriptionSchema>;
export type SubscriptionListQuery = z.infer<typeof subscriptionListQuerySchema>;

export type CreateOrderRequest = z.infer<typeof createOrderSchema>;
export type OrderListQuery = z.infer<typeof orderListQuerySchema>;
export type OrderIdParam = z.infer<typeof orderIdParamSchema>;

export type StripeWebhookEvent = z.infer<typeof stripeWebhookSchema>;
export type UsageUpdateRequest = z.infer<typeof usageUpdateSchema>;
export type CreateAuditLogRequest = z.infer<typeof createAuditLogSchema>;
