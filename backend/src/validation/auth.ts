/**
 * Authentication Validation Schemas
 * 
 * Zod schemas for validating authentication-related requests and responses.
 * Follows the coding standards and provides comprehensive validation.
 */

import { z } from 'zod';
// UserRole is imported in types but not used in validation schemas

// =============================================================================
// COMMON VALIDATION PATTERNS
// =============================================================================

const emailSchema = z
  .string()
  .email('Invalid email format')
  .min(5, 'Email must be at least 5 characters')
  .max(255, 'Email must not exceed 255 characters')
  .toLowerCase()
  .trim();

const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must not exceed 128 characters')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
    'Password must contain at least one lowercase letter, one uppercase letter, and one number');

const nameSchema = z
  .string()
  .min(1, 'Name cannot be empty')
  .max(100, 'Name must not exceed 100 characters')
  .trim()
  .optional();

const userRoleSchema = z.enum(['ADMIN', 'CUSTOMER'] as const);

const uuidSchema = z
  .string()
  .uuid('Invalid UUID format');

// =============================================================================
// AUTHENTICATION SCHEMAS
// =============================================================================

export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required')
});

export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  first_name: nameSchema,
  last_name: nameSchema
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

export const googleOAuthSchema = z.object({
  code: z.string().min(1, 'Authorization code is required'),
  state: z.string().optional()
});

export const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required')
});

// =============================================================================
// PASSWORD MANAGEMENT SCHEMAS
// =============================================================================

export const forgotPasswordSchema = z.object({
  email: emailSchema
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

// =============================================================================
// EMAIL VERIFICATION SCHEMAS
// =============================================================================

export const emailVerificationSchema = z.object({
  token: z.string().min(1, 'Verification token is required')
});

export const resendVerificationSchema = z.object({
  email: emailSchema
});

// =============================================================================
// USER MANAGEMENT SCHEMAS
// =============================================================================

export const createUserSchema = z.object({
  email: emailSchema,
  password: passwordSchema.optional(),
  google_id: z.string().optional(),
  google_email: emailSchema.optional(),
  first_name: nameSchema,
  last_name: nameSchema,
  role: userRoleSchema.default('CUSTOMER')
}).refine((data) => data.password || data.google_id, {
  message: "Either password or Google ID must be provided",
  path: ["password"]
});

export const updateUserSchema = z.object({
  email: emailSchema.optional(),
  password: passwordSchema.optional(),
  first_name: nameSchema,
  last_name: nameSchema,
  role: userRoleSchema.optional(),
  is_active: z.boolean().optional(),
  email_verified: z.boolean().optional()
});

export const updateUserProfileSchema = z.object({
  email: emailSchema.optional(),
  first_name: nameSchema,
  last_name: nameSchema
});

// =============================================================================
// QUERY SCHEMAS
// =============================================================================

export const userListQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  role: userRoleSchema.optional(),
  is_active: z.coerce.boolean().optional(),
  email_verified: z.coerce.boolean().optional(),
  search: z.string().min(1).max(255).trim().optional(),
  sort_by: z.enum(['created_at', 'updated_at', 'email', 'last_login_at']).default('created_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc')
});

// =============================================================================
// PARAMETER SCHEMAS
// =============================================================================

export const userIdParamSchema = z.object({
  id: uuidSchema
});

export const tokenParamSchema = z.object({
  token: z.string().min(1, 'Token is required')
});

// =============================================================================
// RESPONSE SCHEMAS
// =============================================================================

export const userProfileSchema = z.object({
  id: uuidSchema,
  email: emailSchema,
  first_name: z.string().nullable(),
  last_name: z.string().nullable(),
  role: userRoleSchema,
  is_active: z.boolean(),
  email_verified: z.boolean(),
  created_at: z.string(),
  last_login_at: z.string().nullable()
});

export const authResponseSchema = z.object({
  user: userProfileSchema,
  accessToken: z.string(),
  refreshToken: z.string(),
  expiresIn: z.number()
});

export const refreshTokenResponseSchema = z.object({
  accessToken: z.string(),
  refreshToken: z.string(),
  expiresIn: z.number()
});

// =============================================================================
// JWT PAYLOAD SCHEMA
// =============================================================================

export const jwtPayloadSchema = z.object({
  userId: uuidSchema,
  email: emailSchema,
  role: userRoleSchema,
  iat: z.number(),
  exp: z.number(),
  jti: z.string().optional()
});

// =============================================================================
// VALIDATION HELPERS
// =============================================================================

/**
 * Validates password strength
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
} {
  const errors: string[] = [];
  let score = 0;

  // Length check
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  } else if (password.length >= 12) {
    score += 2;
  } else {
    score += 1;
  }

  // Character variety checks
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
    // Special characters are optional but add to strength
  } else {
    score += 1;
  }

  // Common password patterns
  if (/(.)\1{2,}/.test(password)) {
    errors.push('Password should not contain repeated characters');
    score -= 1;
  }

  if (/123|abc|qwe|password|admin/i.test(password)) {
    errors.push('Password should not contain common patterns');
    score -= 1;
  }

  // Determine strength
  let strength: 'weak' | 'medium' | 'strong';
  if (score < 3) {
    strength = 'weak';
  } else if (score < 5) {
    strength = 'medium';
  } else {
    strength = 'strong';
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength
  };
}

/**
 * Validates email format and domain
 */
export function validateEmailDomain(email: string): {
  isValid: boolean;
  error?: string;
} {
  try {
    emailSchema.parse(email);
    
    // Additional domain checks can be added here
    const domain = email.split('@')[1];

    // Block temporary email domains (basic list)
    const blockedDomains = [
      '10minutemail.com',
      'tempmail.org',
      'guerrillamail.com',
      'mailinator.com'
    ];

    if (domain && blockedDomains.includes(domain.toLowerCase())) {
      return {
        isValid: false,
        error: 'Temporary email addresses are not allowed'
      };
    }
    
    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid email format'
    };
  }
}

// =============================================================================
// EXPORT TYPES FOR INFERENCE
// =============================================================================

export type LoginRequest = z.infer<typeof loginSchema>;
export type RegisterRequest = z.infer<typeof registerSchema>;
export type GoogleOAuthRequest = z.infer<typeof googleOAuthSchema>;
export type RefreshTokenRequest = z.infer<typeof refreshTokenSchema>;
export type ForgotPasswordRequest = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordRequest = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordRequest = z.infer<typeof changePasswordSchema>;
export type EmailVerificationRequest = z.infer<typeof emailVerificationSchema>;
export type ResendVerificationRequest = z.infer<typeof resendVerificationSchema>;
export type CreateUserRequest = z.infer<typeof createUserSchema>;
export type UpdateUserRequest = z.infer<typeof updateUserSchema>;
export type UpdateUserProfileRequest = z.infer<typeof updateUserProfileSchema>;
export type UserListQuery = z.infer<typeof userListQuerySchema>;
export type UserIdParam = z.infer<typeof userIdParamSchema>;
export type TokenParam = z.infer<typeof tokenParamSchema>;
export type UserProfile = z.infer<typeof userProfileSchema>;
export type AuthResponse = z.infer<typeof authResponseSchema>;
export type RefreshTokenResponse = z.infer<typeof refreshTokenResponseSchema>;
export type JWTPayload = z.infer<typeof jwtPayloadSchema>;
