import { z } from 'zod';
import { CONFIG } from '../config';

// Common validation patterns
const POSTAL_CODE_PATTERN = /^\d{4,6}$/;
const COORDINATE_PRECISION = 6; // 6 decimal places for ~0.1m precision

// Base schemas
export const CoordinatesSchema = z.object({
  lat: z.number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90')
    .refine(val => Number.isFinite(val), 'Latitude must be a valid number'),
  lng: z.number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180')
    .refine(val => Number.isFinite(val), 'Longitude must be a valid number')
});

export const PostalCodeSchema = z.string()
  .regex(POSTAL_CODE_PATTERN, 'Postal code must be 4-6 digits')
  .optional();

export const PaginationSchema = z.object({
  page: z.coerce.number()
    .int('Page must be an integer')
    .min(1, 'Page must be at least 1')
    .max(CONFIG.validation.maxPageLimit, `Page cannot exceed ${CONFIG.validation.maxPageLimit}`)
    .default(1),
  limit: z.coerce.number()
    .int('Limit must be an integer')
    .min(1, 'Limit must be at least 1')
    .max(CONFIG.validation.maxResultsPerPage, `Limit cannot exceed ${CONFIG.validation.maxResultsPerPage}`)
    .default(20)
});

export const SortSchema = z.object({
  sortBy: z.enum(['name', 'city', 'updated', 'distance', 'popularity'])
    .default('name'),
  sortOrder: z.enum(['asc', 'desc'])
    .default('asc')
});

// Terminal-specific schemas
export const TerminalIdSchema = z.string()
  .min(1, 'Terminal ID is required')
  .max(CONFIG.validation.maxTerminalIdLength, `Terminal ID too long (max ${CONFIG.validation.maxTerminalIdLength} characters)`)
  .regex(/^[a-zA-Z0-9_-]+$/, 'Terminal ID contains invalid characters');

export const TerminalFilterSchema = z.object({
  city: z.string()
    .min(1, 'City name too short')
    .max(CONFIG.validation.maxCityNameLength, `City name too long (max ${CONFIG.validation.maxCityNameLength} characters)`)
    .optional(),
  postalCode: PostalCodeSchema,
  provider: z.enum(['LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK', 'UNKNOWN'])
    .optional(),
  terminalType: z.enum(['PARCEL_LOCKER', 'PICKUP_POINT', 'POST_OFFICE'])
    .optional(),
  isActive: z.coerce.boolean()
    .default(true)
});

// API endpoint schemas
export const GetTerminalsQuerySchema = PaginationSchema
  .merge(SortSchema)
  .merge(TerminalFilterSchema);

export const GetTerminalParamsSchema = z.object({
  id: TerminalIdSchema
});

export const SearchTerminalsQuerySchema = z.object({
  q: z.string()
    .min(1, 'Search query is required')
    .max(CONFIG.validation.maxSearchQueryLength, `Search query too long (max ${CONFIG.validation.maxSearchQueryLength} characters)`)
    .transform(val => val.trim()),
  ...PaginationSchema.shape,
  ...SortSchema.shape,
  ...TerminalFilterSchema.shape
});

export const NearbyTerminalsQuerySchema = z.object({
  ...CoordinatesSchema.shape,
  radius: z.coerce.number()
    .min(CONFIG.validation.minSearchRadius, `Radius must be at least ${CONFIG.validation.minSearchRadius} km`)
    .max(CONFIG.validation.maxSearchRadius, `Radius cannot exceed ${CONFIG.validation.maxSearchRadius} km`)
    .default(10),
  ...PaginationSchema.shape,
  ...TerminalFilterSchema.shape
}).refine(
  data => data.lat !== undefined && data.lng !== undefined,
  'Both latitude and longitude are required'
);

// Response schemas for validation
export const TerminalResponseSchema = z.object({
  id: z.string(),
  name: z.string(),
  city: z.string(),
  address: z.string(),
  postalCode: z.string().nullable(),
  coordinates: CoordinatesSchema,
  updated: z.string().datetime(),
  provider: z.string(),
  terminalType: z.string(),
  isActive: z.boolean(),
  distance: z.number().optional() // Only present in nearby searches
});

export const PaginatedResponseSchema = z.object({
  data: z.array(TerminalResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean()
  }),
  meta: z.object({
    requestId: z.string(),
    timestamp: z.string().datetime(),
    processingTime: z.number()
  })
});

// Error response schema
export const ErrorResponseSchema = z.object({
  error: z.object({
    code: z.string(),
    message: z.string(),
    requestId: z.string(),
    timestamp: z.string().datetime(),
    details: z.record(z.any()).optional()
  })
});

// Health check schemas
export const HealthCheckResponseSchema = z.object({
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  timestamp: z.string().datetime(),
  version: z.string(),
  uptime: z.number(),
  checks: z.object({
    database: z.object({
      status: z.enum(['healthy', 'unhealthy']),
      responseTime: z.number(),
      details: z.string().optional()
    }),
    cache: z.object({
      status: z.enum(['healthy', 'unhealthy']),
      responseTime: z.number(),
      details: z.string().optional()
    })
  })
});

// Data import/sync schemas (for internal use)
export const TerminalImportSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  city: z.string().min(1, 'City is required'),
  address: z.string().min(1, 'Address is required'),
  postalCode: PostalCodeSchema,
  coordinates: CoordinatesSchema,
  provider: z.enum(['LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK']),
  terminalType: z.enum(['PARCEL_LOCKER', 'PICKUP_POINT', 'POST_OFFICE']).default('PARCEL_LOCKER'),
  metadata: z.record(z.any()).default({})
});

export const BulkImportSchema = z.object({
  terminals: z.array(TerminalImportSchema).min(1, 'At least one terminal is required'),
  provider: z.enum(['LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK']),
  replaceExisting: z.boolean().default(false)
});

// Validation helper functions
export function validateCoordinates(lat: number, lng: number): boolean {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
}

export function validatePostalCode(postalCode: string): boolean {
  return POSTAL_CODE_PATTERN.test(postalCode);
}

export function sanitizeSearchQuery(query: string): string {
  return query
    .trim()
    .replace(/[^\w\s\-.]/g, '') // Remove special characters except word chars, spaces, hyphens, dots
    .replace(/\s+/g, ' ') // Normalize whitespace
    .substring(0, 255); // Limit length
}

export function normalizeCoordinates(lat: number, lng: number): { lat: number; lng: number } {
  return {
    lat: Math.round(lat * Math.pow(10, COORDINATE_PRECISION)) / Math.pow(10, COORDINATE_PRECISION),
    lng: Math.round(lng * Math.pow(10, COORDINATE_PRECISION)) / Math.pow(10, COORDINATE_PRECISION)
  };
}

// Type exports for TypeScript
export type CoordinatesType = z.infer<typeof CoordinatesSchema>;
export type PaginationType = z.infer<typeof PaginationSchema>;
export type SortType = z.infer<typeof SortSchema>;
export type TerminalFilterType = z.infer<typeof TerminalFilterSchema>;
export type GetTerminalsQueryType = z.infer<typeof GetTerminalsQuerySchema>;
export type GetTerminalParamsType = z.infer<typeof GetTerminalParamsSchema>;
export type SearchTerminalsQueryType = z.infer<typeof SearchTerminalsQuerySchema>;
export type NearbyTerminalsQueryType = z.infer<typeof NearbyTerminalsQuerySchema>;
export type TerminalResponseType = z.infer<typeof TerminalResponseSchema>;
export type PaginatedResponseType = z.infer<typeof PaginatedResponseSchema>;
export type ErrorResponseType = z.infer<typeof ErrorResponseSchema>;
export type HealthCheckResponseType = z.infer<typeof HealthCheckResponseSchema>;
export type TerminalImportType = z.infer<typeof TerminalImportSchema>;
export type BulkImportType = z.infer<typeof BulkImportSchema>;
