/**
 * Subscription Management Routes
 * 
 * API endpoints for subscription plans, user subscriptions, orders, and billing.
 * Requires JWT authentication for all endpoints.
 */

import { FastifyPluginAsync } from 'fastify';
import { SubscriptionService } from '../services/SubscriptionService';
import { StripeService } from '../services/StripeService';
import { requireAuth } from '../middleware/jwtAuth';
// Note: Import validation schemas when they are created
// For now, we'll use basic validation

// Lazy initialization to avoid database pool issues
let subscriptionService: SubscriptionService;
function getSubscriptionService(): SubscriptionService {
  if (!subscriptionService) {
    subscriptionService = new SubscriptionService();
  }
  return subscriptionService;
}

// Helper function to ensure user is authenticated
function ensureUser(request: any): { id: string; role: string } {
  if (!request.user) {
    throw new Error('User not authenticated');
  }
  return request.user;
}

export const subscriptionRoutes: FastifyPluginAsync = async (fastify) => {
  // Apply JWT authentication to all routes
  fastify.addHook('preHandler', requireAuth);

  // =============================================================================
  // SUBSCRIPTION PLANS ROUTES
  // =============================================================================

  /**
   * GET /subscriptions/plans
   * Get all active subscription plans
   */
  fastify.get('/plans', async (request, reply) => {
    try {
      const { includePrivate } = request.query as { includePrivate?: boolean };
      const service = getSubscriptionService();
      const plans = await service.getActivePlans(includePrivate);

      return {
        success: true,
        data: plans,
        count: plans.length
      };
    } catch (error) {
      fastify.log.error('Error fetching subscription plans:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch subscription plans'
      });
    }
  });

  /**
   * GET /subscriptions/plans/:id
   * Get subscription plan by ID
   */
  fastify.get('/plans/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const plan = await subscriptionService.getPlanById(id);
      
      if (!plan) {
        return reply.status(404).send({
          success: false,
          error: 'Subscription plan not found'
        });
      }

      return {
        success: true,
        data: plan
      };
    } catch (error) {
      fastify.log.error('Error fetching subscription plan:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch subscription plan'
      });
    }
  });

  /**
   * POST /subscriptions/plans
   * Create new subscription plan (Admin only)
   */
  fastify.post('/plans', async (request, reply) => {
    try {
      // Check if user is admin
      if (!request.user || request.user.role !== 'ADMIN') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        });
      }

      const planData = request.body as any;
      const service = getSubscriptionService();
      const plan = await service.createPlan(planData);

      // Log audit event
      await service.logAuditEvent({
        event_type: 'PLAN_CREATED',
        event_description: `Subscription plan '${plan.name}' created`,
        new_values: plan,
        performed_by: request.user.id,
        ip_address: request.ip,
        user_agent: request.headers['user-agent'] || undefined
      });

      return reply.status(201).send({
        success: true,
        data: plan
      });
    } catch (error) {
      fastify.log.error('Error creating subscription plan:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to create subscription plan'
      });
    }
  });

  /**
   * PUT /subscriptions/plans/:id
   * Update subscription plan (Admin only)
   */
  fastify.put('/plans/:id', async (request, reply) => {
    try {
      // Check if user is admin
      if (!request.user || request.user.role !== 'ADMIN') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        });
      }

      const { id } = request.params as { id: string };
      const updates = request.body as any;
      const service = getSubscriptionService();

      const oldPlan = await service.getPlanById(id);
      if (!oldPlan) {
        return reply.status(404).send({
          success: false,
          error: 'Subscription plan not found'
        });
      }

      const updatedPlan = await service.updatePlan(id, updates);

      // Log audit event
      await service.logAuditEvent({
        event_type: 'PLAN_UPDATED',
        event_description: `Subscription plan '${oldPlan.name}' updated`,
        old_values: oldPlan,
        new_values: updatedPlan,
        performed_by: request.user.id,
        ip_address: request.ip,
        user_agent: request.headers['user-agent'] || undefined
      });

      return {
        success: true,
        data: updatedPlan
      };
    } catch (error) {
      fastify.log.error('Error updating subscription plan:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to update subscription plan'
      });
    }
  });

  /**
   * DELETE /subscriptions/plans/:id
   * Delete subscription plan (Admin only)
   */
  fastify.delete('/plans/:id', async (request, reply) => {
    try {
      const user = ensureUser(request);

      // Check if user is admin
      if (user.role !== 'ADMIN') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        });
      }

      const { id } = request.params as { id: string };
      const service = getSubscriptionService();

      const plan = await service.getPlanById(id);
      if (!plan) {
        return reply.status(404).send({
          success: false,
          error: 'Subscription plan not found'
        });
      }

      const deleted = await service.deletePlan(id);

      if (deleted) {
        // Log audit event
        await service.logAuditEvent({
          event_type: 'PLAN_DELETED',
          event_description: `Subscription plan '${plan.name}' deleted`,
          old_values: plan,
          performed_by: user.id,
          ip_address: request.ip,
          user_agent: request.headers['user-agent'] || undefined
        });

        return {
          success: true,
          message: 'Subscription plan deleted successfully'
        };
      } else {
        return reply.status(500).send({
          success: false,
          error: 'Failed to delete subscription plan'
        });
      }
    } catch (error) {
      fastify.log.error('Error deleting subscription plan:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to delete subscription plan'
      });
    }
  });

  // =============================================================================
  // USER SUBSCRIPTIONS ROUTES
  // =============================================================================

  /**
   * GET /subscriptions/my-subscription
   * Get current user's active subscription
   */
  fastify.get('/my-subscription', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const service = getSubscriptionService();
      const subscription = await service.getUserActiveSubscription(user.id);

      return {
        success: true,
        data: subscription
      };
    } catch (error) {
      fastify.log.error('Error fetching user subscription:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch subscription'
      });
    }
  });

  /**
   * GET /subscriptions/my-history
   * Get current user's subscription history
   */
  fastify.get('/my-history', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const service = getSubscriptionService();
      const history = await service.getUserSubscriptionHistory(user.id);

      return {
        success: true,
        data: history,
        count: history.length
      };
    } catch (error) {
      fastify.log.error('Error fetching subscription history:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch subscription history'
      });
    }
  });

  /**
   * POST /subscriptions/subscribe
   * Create new subscription for current user
   */
  fastify.post('/subscribe', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const service = getSubscriptionService();

      const subscriptionData = {
        ...request.body as any,
        user_id: user.id
      };

      // Check if user already has active subscription
      const existingSubscription = await service.getUserActiveSubscription(user.id);
      if (existingSubscription) {
        return reply.status(400).send({
          success: false,
          error: 'User already has an active subscription'
        });
      }

      // Verify plan exists and is active
      const plan = await service.getPlanById(subscriptionData.plan_id);
      if (!plan || !plan.is_active) {
        return reply.status(400).send({
          success: false,
          error: 'Invalid or inactive subscription plan'
        });
      }

      const subscription = await service.createUserSubscription(subscriptionData);

      // Log audit event
      await service.logAuditEvent({
        user_id: user.id,
        subscription_id: subscription.id,
        event_type: 'SUBSCRIPTION_CREATED',
        event_description: `User subscribed to plan '${plan.name}'`,
        new_values: subscription,
        performed_by: user.id,
        ip_address: request.ip,
        user_agent: request.headers['user-agent'] || undefined
      });

      return reply.status(201).send({
        success: true,
        data: subscription
      });
    } catch (error) {
      fastify.log.error('Error creating subscription:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to create subscription'
      });
    }
  });

  /**
   * PUT /subscriptions/:id/cancel
   * Cancel user subscription
   */
  fastify.put('/:id/cancel', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const service = getSubscriptionService();

      const { id } = request.params as { id: string };
      const { cancelAtPeriodEnd = true } = request.body as { cancelAtPeriodEnd?: boolean };

      // Verify subscription belongs to user
      const subscription = await service.getUserSubscriptionById(id);
      if (!subscription || subscription.user_id !== user.id) {
        return reply.status(404).send({
          success: false,
          error: 'Subscription not found'
        });
      }

      const canceledSubscription = await service.cancelUserSubscription(id, cancelAtPeriodEnd);

      // Log audit event
      await service.logAuditEvent({
        user_id: user.id,
        subscription_id: id,
        event_type: 'SUBSCRIPTION_CANCELED',
        event_description: `Subscription canceled (at period end: ${cancelAtPeriodEnd})`,
        old_values: subscription,
        new_values: canceledSubscription,
        performed_by: user.id,
        ip_address: request.ip,
        user_agent: request.headers['user-agent'] || undefined
      });

      return {
        success: true,
        data: canceledSubscription
      };
    } catch (error) {
      fastify.log.error('Error canceling subscription:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to cancel subscription'
      });
    }
  });

  // =============================================================================
  // ORDERS ROUTES
  // =============================================================================

  /**
   * GET /subscriptions/my-orders
   * Get current user's orders
   */
  fastify.get('/my-orders', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const service = getSubscriptionService();

      const { limit = 50 } = request.query as { limit?: number };
      const orders = await service.getUserOrders(user.id, limit);

      return {
        success: true,
        data: orders,
        count: orders.length
      };
    } catch (error) {
      fastify.log.error('Error fetching user orders:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch orders'
      });
    }
  });

  /**
   * GET /subscriptions/orders/:id
   * Get order by ID (user can only access their own orders)
   */
  fastify.get('/orders/:id', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const service = getSubscriptionService();

      const { id } = request.params as { id: string };
      const order = await service.getOrderById(id);

      if (!order) {
        return reply.status(404).send({
          success: false,
          error: 'Order not found'
        });
      }

      // Check if order belongs to user (or user is admin)
      if (order.user_id !== user.id && user.role !== 'ADMIN') {
        return reply.status(403).send({
          success: false,
          error: 'Access denied'
        });
      }

      return {
        success: true,
        data: order
      };
    } catch (error) {
      fastify.log.error('Error fetching order:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch order'
      });
    }
  });

  /**
   * POST /subscriptions/orders
   * Create new order
   */
  fastify.post('/orders', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const service = getSubscriptionService();

      const orderData = {
        ...request.body as any,
        user_id: user.id,
        order_number: await service.generateOrderNumber(),
        total_amount: (request.body as any).subtotal || 0
      };

      const order = await service.createOrder(orderData);

      // Log audit event
      await service.logAuditEvent({
        user_id: user.id,
        order_id: order.id,
        event_type: 'ORDER_CREATED',
        event_description: `Order ${order.order_number} created`,
        new_values: order,
        performed_by: user.id,
        ip_address: request.ip,
        user_agent: request.headers['user-agent'] || undefined
      });

      return reply.status(201).send({
        success: true,
        data: order
      });
    } catch (error) {
      fastify.log.error('Error creating order:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to create order'
      });
    }
  });

  // =============================================================================
  // ADMIN ROUTES
  // =============================================================================

  /**
   * GET /subscriptions/admin/stats
   * Get subscription statistics (Admin only)
   */
  fastify.get('/admin/stats', async (request, reply) => {
    try {
      const user = ensureUser(request);

      // Check if user is admin
      if (user.role !== 'ADMIN') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        });
      }

      const service = getSubscriptionService();
      const stats = await service.getSubscriptionStats();

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      fastify.log.error('Error fetching subscription stats:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch subscription statistics'
      });
    }
  });

  /**
   * GET /subscriptions/admin/users/:userId/subscriptions
   * Get user's subscriptions (Admin only)
   */
  fastify.get('/admin/users/:userId/subscriptions', async (request, reply) => {
    try {
      const user = ensureUser(request);

      // Check if user is admin
      if (user.role !== 'ADMIN') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        });
      }

      const { userId } = request.params as { userId: string };
      const service = getSubscriptionService();
      const subscriptions = await service.getUserSubscriptionHistory(userId);

      return {
        success: true,
        data: subscriptions,
        count: subscriptions.length
      };
    } catch (error) {
      fastify.log.error('Error fetching user subscriptions:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch user subscriptions'
      });
    }
  });

  /**
   * GET /subscriptions/admin/audit-logs
   * Get audit logs (Admin only)
   */
  fastify.get('/admin/audit-logs', async (request, reply) => {
    try {
      const user = ensureUser(request);

      // Check if user is admin
      if (user.role !== 'ADMIN') {
        return reply.status(403).send({
          success: false,
          error: 'Admin access required'
        });
      }

      const { userId, subscriptionId, limit = 100 } = request.query as {
        userId?: string;
        subscriptionId?: string;
        limit?: number;
      };

      const service = getSubscriptionService();
      let logs: any[] = [];

      if (userId) {
        logs = await service.getUserAuditLogs(userId, limit);
      } else if (subscriptionId) {
        logs = await service.getSubscriptionAuditLogs(subscriptionId, limit);
      }

      return {
        success: true,
        data: logs,
        count: logs.length
      };
    } catch (error) {
      fastify.log.error('Error fetching audit logs:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to fetch audit logs'
      });
    }
  });

  // =============================================================================
  // STRIPE CHECKOUT ENDPOINTS
  // =============================================================================

  /**
   * POST /subscriptions/checkout
   * Create Stripe checkout session for subscription
   */
  fastify.post<{
    Body: {
      planId: string;
      billingCycle?: 'monthly' | 'yearly';
      successUrl: string;
      cancelUrl: string;
    };
  }>('/checkout', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const { planId, billingCycle = 'monthly', successUrl, cancelUrl } = request.body;

      if (!planId || !successUrl || !cancelUrl) {
        return reply.status(400).send({
          success: false,
          error: 'Missing required fields: planId, successUrl, cancelUrl'
        });
      }

      const stripeService = new StripeService();
      const session = await stripeService.createCheckoutSession(
        user.id,
        planId,
        billingCycle,
        successUrl,
        cancelUrl
      );

      return {
        success: true,
        data: {
          sessionId: session.id,
          url: session.url
        }
      };
    } catch (error) {
      fastify.log.error('Error creating checkout session:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to create checkout session'
      });
    }
  });

  /**
   * POST /subscriptions/change-plan
   * Create checkout session for plan change
   */
  fastify.post<{
    Body: {
      newPlanId: string;
      billingCycle?: 'monthly' | 'yearly';
      successUrl: string;
      cancelUrl: string;
    };
  }>('/change-plan', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const { newPlanId, billingCycle = 'monthly', successUrl, cancelUrl } = request.body;

      if (!newPlanId || !successUrl || !cancelUrl) {
        return reply.status(400).send({
          success: false,
          error: 'Missing required fields: newPlanId, successUrl, cancelUrl'
        });
      }

      const stripeService = new StripeService();
      const session = await stripeService.createSubscriptionChangeSession(
        user.id,
        newPlanId,
        billingCycle,
        successUrl,
        cancelUrl
      );

      return {
        success: true,
        data: {
          sessionId: session.id,
          url: session.url
        }
      };
    } catch (error) {
      fastify.log.error('Error creating plan change session:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to create plan change session'
      });
    }
  });

  /**
   * POST /subscriptions/cancel
   * Cancel user's subscription
   */
  fastify.post<{
    Body: {
      cancelAtPeriodEnd?: boolean;
    };
  }>('/cancel', async (request, reply) => {
    try {
      const user = ensureUser(request);
      const { cancelAtPeriodEnd = true } = request.body || {};

      const stripeService = new StripeService();
      const subscription = await stripeService.cancelSubscription(user.id, cancelAtPeriodEnd);

      return {
        success: true,
        data: {
          subscriptionId: subscription.id,
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
          currentPeriodEnd: new Date((subscription as any).current_period_end * 1000)
        }
      };
    } catch (error) {
      fastify.log.error('Error canceling subscription:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to cancel subscription'
      });
    }
  });

  /**
   * POST /subscriptions/reactivate
   * Reactivate canceled subscription
   */
  fastify.post('/reactivate', async (request, reply) => {
    try {
      const user = ensureUser(request);

      const stripeService = new StripeService();
      const subscription = await stripeService.reactivateSubscription(user.id);

      return {
        success: true,
        data: {
          subscriptionId: subscription.id,
          status: subscription.status,
          currentPeriodEnd: new Date((subscription as any).current_period_end * 1000)
        }
      };
    } catch (error) {
      fastify.log.error('Error reactivating subscription:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to reactivate subscription'
      });
    }
  });
};
