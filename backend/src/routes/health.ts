import { FastifyPluginAsync } from 'fastify';
import { checkDatabaseAndPostGISHealth, getPoolStats, getConnectionStats } from '../database/connection';
import { HealthResponse } from '../types/api';
import { validateEnvironment } from '../config';
import { authenticateApi<PERSON><PERSON> } from '../middleware/auth';


export const healthRoutes: FastifyPluginAsync = async (fastify) => {
  // Basic health check with optimized database connection usage - rate limited
  fastify.get('/health', async (_, reply) => {
    const env = validateEnvironment();

    // Use combined health check to reduce connection usage
    const healthResults = await checkDatabaseAndPostGISHealth();

    // Determine overall status
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    if (!healthResults.database) {
      status = 'unhealthy';
    } else if (!healthResults.postGIS) {
      status = 'degraded';
    }

    const response: HealthResponse = {
      status,
      timestamp: new Date(),
      version: env.APP_VERSION,
      checks: {
        database: healthResults.database ? 'healthy' : 'unhealthy',
        cache: healthResults.database ? 'healthy' : 'unhealthy', // Cache uses same DB
        apiKeys: healthResults.database ? 'healthy' : 'unhealthy'
      }
    };

    // Set appropriate status code
    const statusCode = status === 'healthy' ? 200 : status === 'degraded' ? 200 : 503;

    reply.status(statusCode).send(response);
  });
  
  // Metrics endpoint with database connection pool metrics (JSON format) - strict rate limiting
  fastify.get('/metrics', {
    preHandler: authenticateApiKey
  }, async (_, reply) => {
    const poolStats = getPoolStats();
    const connectionStats = getConnectionStats();
    const memoryUsage = process.memoryUsage();

    const metrics = {
      system: {
        health: 1,
        uptime_seconds: Math.floor(process.uptime()),
        memory: {
          heap_used_bytes: memoryUsage.heapUsed,
          heap_total_bytes: memoryUsage.heapTotal,
          rss_bytes: memoryUsage.rss,
          external_bytes: memoryUsage.external
        },
        node_version: process.version,
        platform: process.platform,
        arch: process.arch
      },
      database: {
        connections: {
          total: poolStats?.totalCount || 0,
          idle: poolStats?.idleCount || 0,
          waiting: poolStats?.waitingCount || 0
        },
        stats: {
          created_total: connectionStats.totalCreated,
          acquired_total: connectionStats.totalAcquired,
          released_total: connectionStats.totalReleased,
          errors_total: connectionStats.totalErrors,
          last_activity: connectionStats.lastActivity
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        format: "json"
      }
    };

    reply.send(metrics);
  });

  // Database connection pool status endpoint (for debugging)
  fastify.get('/pool-status', async (_, reply) => {
    const poolStats = getPoolStats();
    const connectionStats = getConnectionStats();

    const status = {
      pool: poolStats,
      connections: connectionStats,
      timestamp: new Date()
    };

    reply.send(status);
  });
};
