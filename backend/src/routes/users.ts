/**
 * User Management Routes
 * 
 * Handles user profile management and admin user operations.
 * Implements JWT authentication and role-based access control.
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../services/UserService';
import { JWTService } from '../services/JWTService';
import { requireAuth, requireAdmin, getAuthenticatedUser } from '../middleware/jwtAuth';
// Schemas are now defined inline as JSON schemas
import {
  UpdateUserProfileRequest,
  CreateUserRequest,
  UpdateUserRequest,
  UserListQuery,
  UserIdParam
} from '../validation/auth';

export async function userRoutes(fastify: FastifyInstance) {
  // Lazy-load services to avoid database pool initialization issues
  const getUserService = () => new UserService();
  const getJWTService = () => new JWTService();

  // =============================================================================
  // USER PROFILE MANAGEMENT
  // =============================================================================

  /**
   * GET /users/me
   * Get current user profile
   */
  fastify.get('/me', {
    preHandler: [requireAuth],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            first_name: { type: 'string', nullable: true },
            last_name: { type: 'string', nullable: true },
            role: { type: 'string' },
            is_active: { type: 'boolean' },
            email_verified: { type: 'boolean' },
            created_at: { type: 'string' },
            last_login_at: { type: 'string', nullable: true }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = getAuthenticatedUser(request);
      if (!user) {
        return reply.status(401).send({
          error: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required',
          requestId: request.id
        });
      }

      return reply.send(user);

    } catch (error) {
      request.log.error('Get user profile error:', error);
      return reply.status(500).send({
        error: 'PROFILE_FETCH_FAILED',
        message: 'Failed to fetch user profile',
        requestId: request.id
      });
    }
  });

  /**
   * PATCH /users/me
   * Update current user profile
   */
  fastify.patch<{ Body: UpdateUserProfileRequest }>('/me', {
    preHandler: [requireAuth],
    schema: {
      body: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          first_name: { type: 'string' },
          last_name: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            first_name: { type: 'string', nullable: true },
            last_name: { type: 'string', nullable: true },
            role: { type: 'string' },
            is_active: { type: 'boolean' },
            email_verified: { type: 'boolean' },
            created_at: { type: 'string' },
            last_login_at: { type: 'string', nullable: true }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: UpdateUserProfileRequest }>, reply: FastifyReply) => {
    try {
      const user = getAuthenticatedUser(request);
      if (!user) {
        return reply.status(401).send({
          error: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required',
          requestId: request.id
        });
      }

      const { email, first_name, last_name } = request.body;

      // Check if email is being changed and if it already exists
      if (email && email !== user.email) {
        const emailExists = await getUserService().emailExists(email, user.id);
        if (emailExists) {
          return reply.status(400).send({
            error: 'EMAIL_ALREADY_EXISTS',
            message: 'An account with this email already exists',
            requestId: request.id
          });
        }
      }

      // Update user profile
      const updatedUser = await getUserService().updateUser(user.id, {
        email: email || null,
        first_name: first_name || null,
        last_name: last_name || null
      });

      if (!updatedUser) {
        return reply.status(404).send({
          error: 'USER_NOT_FOUND',
          message: 'User not found',
          requestId: request.id
        });
      }

      // Return updated profile (without sensitive data)
      const profile = await getUserService().getUserProfile(updatedUser.id);
      return reply.send(profile);

    } catch (error) {
      request.log.error('Update user profile error:', error);
      return reply.status(500).send({
        error: 'PROFILE_UPDATE_FAILED',
        message: 'Failed to update user profile',
        requestId: request.id
      });
    }
  });

  /**
   * DELETE /users/me
   * Delete current user account
   */
  fastify.delete('/me', {
    preHandler: [requireAuth],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = getAuthenticatedUser(request);
      if (!user) {
        return reply.status(401).send({
          error: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required',
          requestId: request.id
        });
      }

      // Revoke all user tokens
      await getJWTService().revokeAllUserTokens(user.id, 'Account deletion');

      // Soft delete user account
      const deleted = await getUserService().deleteUser(user.id);

      if (!deleted) {
        return reply.status(404).send({
          error: 'USER_NOT_FOUND',
          message: 'User not found',
          requestId: request.id
        });
      }

      request.log.info(`User account deleted: ${user.email}`);

      return reply.send({
        success: true,
        message: 'Account deleted successfully'
      });

    } catch (error) {
      request.log.error('Delete user account error:', error);
      return reply.status(500).send({
        error: 'ACCOUNT_DELETION_FAILED',
        message: 'Failed to delete account',
        requestId: request.id
      });
    }
  });

  // =============================================================================
  // ADMIN USER MANAGEMENT
  // =============================================================================

  /**
   * GET /users
   * List all users (Admin only)
   */
  fastify.get<{ Querystring: UserListQuery }>('/', {
    preHandler: [requireAuth, requireAdmin],
    schema: {
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'number', minimum: 1 },
          limit: { type: 'number', minimum: 1, maximum: 100 },
          role: { type: 'string', enum: ['ADMIN', 'CUSTOMER'] },
          is_active: { type: 'boolean' },
          email_verified: { type: 'boolean' },
          search: { type: 'string' },
          sort_by: { type: 'string', enum: ['created_at', 'updated_at', 'email', 'last_login_at'] },
          sort_order: { type: 'string', enum: ['asc', 'desc'] }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            users: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  email: { type: 'string' },
                  first_name: { type: 'string', nullable: true },
                  last_name: { type: 'string', nullable: true },
                  role: { type: 'string' },
                  is_active: { type: 'boolean' },
                  email_verified: { type: 'boolean' },
                  created_at: { type: 'string' },
                  last_login_at: { type: 'string', nullable: true }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'number' },
                limit: { type: 'number' },
                total: { type: 'number' },
                totalPages: { type: 'number' },
                hasNext: { type: 'boolean' },
                hasPrev: { type: 'boolean' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: UserListQuery }>, reply: FastifyReply) => {
    try {
      const result = await getUserService().listUsers({
        ...request.query,
        role: request.query.role || null,
        is_active: request.query.is_active ?? null,
        email_verified: request.query.email_verified ?? null,
        search: request.query.search || null
      });
      return reply.send(result);

    } catch (error) {
      request.log.error('List users error:', error);
      return reply.status(500).send({
        error: 'USER_LIST_FAILED',
        message: 'Failed to fetch users',
        requestId: request.id
      });
    }
  });

  /**
   * GET /users/:id
   * Get specific user (Admin only)
   */
  fastify.get<{ Params: UserIdParam }>('/:id', {
    preHandler: [requireAuth, requireAdmin],
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            first_name: { type: 'string', nullable: true },
            last_name: { type: 'string', nullable: true },
            role: { type: 'string' },
            is_active: { type: 'boolean' },
            email_verified: { type: 'boolean' },
            created_at: { type: 'string' },
            last_login_at: { type: 'string', nullable: true }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: UserIdParam }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const user = await getUserService().getUserProfile(id);

      if (!user) {
        return reply.status(404).send({
          error: 'USER_NOT_FOUND',
          message: 'User not found',
          requestId: request.id
        });
      }

      return reply.send(user);

    } catch (error) {
      request.log.error('Get user error:', error);
      return reply.status(500).send({
        error: 'USER_FETCH_FAILED',
        message: 'Failed to fetch user',
        requestId: request.id
      });
    }
  });

  /**
   * POST /users
   * Create new user (Admin only)
   */
  fastify.post<{ Body: CreateUserRequest }>('/', {
    preHandler: [requireAuth, requireAdmin],
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          google_id: { type: 'string' },
          google_email: { type: 'string', format: 'email' },
          first_name: { type: 'string' },
          last_name: { type: 'string' },
          role: { type: 'string', enum: ['ADMIN', 'CUSTOMER'] }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            first_name: { type: 'string', nullable: true },
            last_name: { type: 'string', nullable: true },
            role: { type: 'string' },
            is_active: { type: 'boolean' },
            email_verified: { type: 'boolean' },
            created_at: { type: 'string' },
            last_login_at: { type: 'string', nullable: true }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: CreateUserRequest }>, reply: FastifyReply) => {
    try {
      const userData = request.body;

      // Check if user already exists
      const existingUser = await getUserService().getUserByEmail(userData.email);
      if (existingUser) {
        return reply.status(400).send({
          error: 'EMAIL_ALREADY_EXISTS',
          message: 'An account with this email already exists',
          requestId: request.id
        });
      }

      // Create user
      const user = await getUserService().createUser({
        ...userData,
        password: userData.password || null,
        first_name: userData.first_name || null,
        last_name: userData.last_name || null,
        google_id: userData.google_id || null,
        google_email: userData.google_email || null
      });
      const profile = await getUserService().getUserProfile(user.id);

      request.log.info(`User created by admin: ${user.email}`);

      return reply.status(201).send(profile);

    } catch (error) {
      request.log.error('Create user error:', error);
      return reply.status(500).send({
        error: 'USER_CREATION_FAILED',
        message: 'Failed to create user',
        requestId: request.id
      });
    }
  });

  /**
   * PATCH /users/:id
   * Update user (Admin only)
   */
  fastify.patch<{ Params: UserIdParam; Body: UpdateUserRequest }>('/:id', {
    preHandler: [requireAuth, requireAdmin],
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid' }
        }
      },
      body: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          first_name: { type: 'string' },
          last_name: { type: 'string' },
          role: { type: 'string', enum: ['ADMIN', 'CUSTOMER'] },
          is_active: { type: 'boolean' },
          email_verified: { type: 'boolean' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            first_name: { type: 'string', nullable: true },
            last_name: { type: 'string', nullable: true },
            role: { type: 'string' },
            is_active: { type: 'boolean' },
            email_verified: { type: 'boolean' },
            created_at: { type: 'string' },
            last_login_at: { type: 'string', nullable: true }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: UserIdParam; Body: UpdateUserRequest }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const updates = request.body;

      // Check if email is being changed and if it already exists
      if (updates.email) {
        const emailExists = await getUserService().emailExists(updates.email, id);
        if (emailExists) {
          return reply.status(400).send({
            error: 'EMAIL_ALREADY_EXISTS',
            message: 'An account with this email already exists',
            requestId: request.id
          });
        }
      }

      // Update user
      const updatedUser = await getUserService().updateUser(id, {
        email: updates.email || null,
        password: updates.password || null,
        first_name: updates.first_name || null,
        last_name: updates.last_name || null,
        role: updates.role || null,
        is_active: updates.is_active ?? null,
        email_verified: updates.email_verified ?? null
      });

      if (!updatedUser) {
        return reply.status(404).send({
          error: 'USER_NOT_FOUND',
          message: 'User not found',
          requestId: request.id
        });
      }

      // If user was deactivated, revoke all their tokens
      if (updates.is_active === false) {
        await getJWTService().revokeAllUserTokens(id, 'Account deactivated by admin');
      }

      const profile = await getUserService().getUserProfile(updatedUser.id);

      request.log.info(`User updated by admin: ${updatedUser.email}`);

      return reply.send(profile);

    } catch (error) {
      request.log.error('Update user error:', error);
      return reply.status(500).send({
        error: 'USER_UPDATE_FAILED',
        message: 'Failed to update user',
        requestId: request.id
      });
    }
  });

  /**
   * DELETE /users/:id
   * Delete user (Admin only)
   */
  fastify.delete<{ Params: UserIdParam }>('/:id', {
    preHandler: [requireAuth, requireAdmin],
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: UserIdParam }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;

      // Get user info for logging
      const user = await getUserService().getUserById(id);
      if (!user) {
        return reply.status(404).send({
          error: 'USER_NOT_FOUND',
          message: 'User not found',
          requestId: request.id
        });
      }

      // Revoke all user tokens
      await getJWTService().revokeAllUserTokens(id, 'Account deleted by admin');

      // Delete user
      const deleted = await getUserService().deleteUser(id);

      if (!deleted) {
        return reply.status(404).send({
          error: 'USER_NOT_FOUND',
          message: 'User not found',
          requestId: request.id
        });
      }

      request.log.info(`User deleted by admin: ${user.email}`);

      return reply.send({
        success: true,
        message: 'User deleted successfully'
      });

    } catch (error) {
      request.log.error('Delete user error:', error);
      return reply.status(500).send({
        error: 'USER_DELETION_FAILED',
        message: 'Failed to delete user',
        requestId: request.id
      });
    }
  });
}
