import { FastifyPluginAsync, FastifyRequest } from 'fastify';
import { authenticate<PERSON><PERSON><PERSON><PERSON> } from '../middleware/auth';
import { TerminalService } from '../services/terminal-service';
import {
  GetTerminalsQuerySchema,
  GetTerminalParamsSchema,
  SearchTerminalsQuerySchema,
  NearbyTerminalsQuerySchema
} from '../validation/schemas';
import { CONFIG } from '../config';


// Type augmentation for request ID
interface RequestWithId extends FastifyRequest {
  requestId?: string;
}

// Convert Zod schemas to JSON Schema for Fastify
const getTerminalsQueryJsonSchema = {
  type: 'object',
  properties: {
    page: { type: 'integer', minimum: 1, default: 1 },
    limit: { type: 'integer', minimum: 1, maximum: CONFIG.validation.maxResultsPerPage, default: 20 },
    sortBy: { type: 'string', enum: ['name', 'city', 'provider', 'updated'], default: 'name' },
    sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'asc' },
    city: { type: 'string', minLength: 1, maxLength: 100 },
    provider: { type: 'string', enum: ['LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK'] },
    terminalType: { type: 'string', enum: ['PARCEL_LOCKER', 'PICKUP_POINT', 'POST_OFFICE'] },
    isActive: { type: 'boolean', default: true }
  },
  additionalProperties: false
};

const searchTerminalsQueryJsonSchema = {
  type: 'object',
  properties: {
    q: { type: 'string', minLength: 1, maxLength: 255 },
    page: { type: 'integer', minimum: 1, default: 1 },
    limit: { type: 'integer', minimum: 1, maximum: CONFIG.validation.maxResultsPerPage, default: 20 },
    sortBy: { type: 'string', enum: ['name', 'city', 'provider', 'updated'], default: 'name' },
    sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'asc' },
    city: { type: 'string', minLength: 1, maxLength: 100 },
    provider: { type: 'string', enum: ['LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK'] },
    terminalType: { type: 'string', enum: ['PARCEL_LOCKER', 'PICKUP_POINT', 'POST_OFFICE'] },
    isActive: { type: 'boolean', default: true }
  },
  required: ['q'],
  additionalProperties: false
};

const nearbyTerminalsQueryJsonSchema = {
  type: 'object',
  properties: {
    lat: { type: 'number', minimum: -90, maximum: 90 },
    lng: { type: 'number', minimum: -180, maximum: 180 },
    radius: { type: 'integer', minimum: CONFIG.validation.minSearchRadius, maximum: CONFIG.validation.maxSearchRadius, default: 10 },
    page: { type: 'integer', minimum: 1, default: 1 },
    limit: { type: 'integer', minimum: 1, maximum: CONFIG.validation.maxResultsPerPage, default: 20 },
    city: { type: 'string', minLength: 1, maxLength: 100 },
    provider: { type: 'string', enum: ['LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK'] },
    terminalType: { type: 'string', enum: ['PARCEL_LOCKER', 'PICKUP_POINT', 'POST_OFFICE'] },
    isActive: { type: 'boolean', default: true }
  },
  required: ['lat', 'lng'],
  additionalProperties: false
};

const terminalParamsJsonSchema = {
  type: 'object',
  properties: {
    id: { type: 'string', minLength: 1, maxLength: 255, pattern: '^[a-zA-Z0-9_-]+$' }
  },
  required: ['id'],
  additionalProperties: false
};

export const terminalRoutes: FastifyPluginAsync = async (fastify) => {
  const terminalService = new TerminalService();

  // GET /api/v1/terminals - List terminals with filtering and rate limiting
  fastify.get('/terminals', {
    preHandler: authenticateApiKey,
    schema: {
      querystring: getTerminalsQueryJsonSchema
    }
  }, async (request, reply) => {
    try {
      const startTime = Date.now();
      const query = GetTerminalsQuerySchema.parse(request.query);
      const result = await terminalService.getTerminals(query);

      // Add cache headers for performance
      reply.header('Cache-Control', 'public, max-age=300'); // 5 minutes
      reply.header('ETag', `"terminals-${query.page}-${query.limit}"`);

      // Update processing time safely
      const responseWithTiming = {
        ...result,
        meta: {
          ...result.meta,
          processingTime: Date.now() - startTime
        }
      };

      return responseWithTiming;
    } catch (error) {
      request.log.error('Error in terminals listing:', error);
      throw error;
    }
  });
  
  // GET /api/v1/terminals/:id - Get specific terminal details
  fastify.get('/terminals/:id', {
    preHandler: authenticateApiKey,
    schema: {
      params: terminalParamsJsonSchema
    }
  }, async (request, reply) => {
    try {
      const { id } = GetTerminalParamsSchema.parse(request.params);
      const terminal = await terminalService.getTerminalById(id);

      if (!terminal) {
        return reply.status(404).send({
          error: {
            code: 'TERMINAL_NOT_FOUND',
            message: 'Terminal not found',
            requestId: (request as RequestWithId).requestId || 'unknown',
            timestamp: new Date().toISOString()
          }
        });
      }

      // Add cache headers
      reply.header('Cache-Control', 'public, max-age=600'); // 10 minutes
      reply.header('ETag', `"terminal-${id}-${terminal.updated}"`);

      return terminal;
    } catch (error) {
      request.log.error('Error fetching terminal details:', error);
      throw error;
    }
  });
  
  // GET /api/v1/terminals/nearby - Find nearest terminals
  fastify.get('/terminals/nearby', {
    preHandler: authenticateApiKey,
    schema: {
      querystring: nearbyTerminalsQueryJsonSchema
    }
  }, async (request, reply) => {
    try {
      const startTime = Date.now();
      const query = NearbyTerminalsQuerySchema.parse(request.query);
      const result = await terminalService.getNearbyTerminals(query);

      // Add cache headers with shorter TTL for location-based queries
      reply.header('Cache-Control', 'public, max-age=180'); // 3 minutes
      reply.header('ETag', `"nearby-${query.lat}-${query.lng}-${query.radius}-${query.page}"`);

      // Update processing time safely
      const responseWithTiming = {
        ...result,
        meta: {
          ...result.meta,
          processingTime: Date.now() - startTime
        }
      };

      return responseWithTiming;
    } catch (error) {
      request.log.error('Error in nearby terminals search:', error);
      throw error;
    }
  });
  
  // GET /api/v1/terminals/search - Comprehensive text search
  fastify.get('/terminals/search', {
    preHandler: authenticateApiKey,
    schema: {
      querystring: searchTerminalsQueryJsonSchema
    }
  }, async (request, reply) => {
    try {
      const startTime = Date.now();
      const query = SearchTerminalsQuerySchema.parse(request.query);
      const result = await terminalService.searchTerminals(query, {
        useFullTextSearch: true,
        useTrigrams: true,
        includeInactive: false
      });

      // Add cache headers with shorter TTL for search queries
      reply.header('Cache-Control', 'public, max-age=120'); // 2 minutes
      reply.header('ETag', `"search-${encodeURIComponent(query.q)}-${query.page}"`);

      // Update processing time safely
      const responseWithTiming = {
        ...result,
        meta: {
          ...result.meta,
          processingTime: Date.now() - startTime
        }
      };

      return responseWithTiming;
    } catch (error) {
      request.log.error('Error in terminal search:', error);
      throw error;
    }
  });
};
