import { FastifyPluginAsync } from 'fastify';
import { z } from 'zod';
import { TrackingService } from '../services/tracking/TrackingService';
import { authenticateA<PERSON><PERSON>ey } from '../middleware/auth';


const QuerySchema = z.object({
  provider: z.enum(['LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK']),
  trackingNumber: z.string().min(6),
  refresh: z.coerce.boolean().optional()
});



export const trackingRoutes: FastifyPluginAsync = async (fastify) => {
  const trackingService = new TrackingService();

  // Main tracking endpoint - now returns standardized format with rate limiting
  fastify.get('/track', {
    preHandler: authenticateApiKey
  }, async (request, reply) => {
    const parseResult = QuerySchema.safeParse(request.query);
    if (!parseResult.success) {
      // Handle validation errors properly
      const issues = parseResult.error.issues;
      const firstError = issues[0];

      if (firstError && firstError.path.includes('provider')) {
        const validProviders = ['LP_EXPRESS', 'OMNIVA', 'DPD', 'VENIPAK'];
        return reply.status(400).send({
          statusCode: 400,
          error: "Bad Request",
          message: `Invalid provider. Expected one of: ${validProviders.join(', ')}`
        });
      }

      if (firstError && firstError.path.includes('trackingNumber')) {
        return reply.status(400).send({
          statusCode: 400,
          error: "Bad Request",
          message: "Tracking number is required"
        });
      }

      // Generic validation error
      return reply.status(400).send({
        statusCode: 400,
        error: "Bad Request",
        message: firstError?.message || "Invalid request parameters"
      });
    }

    const { provider, trackingNumber, refresh } = parseResult.data;

    const before = Date.now();
    const { result: data, cacheHit } = await trackingService.track(provider, trackingNumber, refresh);

    return reply.send({
      data,
      meta: {
        requestId: (request as any).requestId,
        cacheHit,
        responseTime: Date.now() - before
      }
    });
  });
};