/**
 * Webhook Routes
 * 
 * Handles incoming webhooks from external services like Stripe.
 * These endpoints are public and don't require authentication but use signature verification.
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { StripeService } from '../services/StripeService';

export async function webhookRoutes(fastify: FastifyInstance) {
  const stripeService = new StripeService();

  /**
   * GET /webhooks/stripe/test
   * Test webhook endpoint accessibility
   */
  fastify.get('/stripe/test', async (request: FastifyRequest, reply: FastifyReply) => {
    return reply.code(200).send({
      message: 'Webhook endpoint is accessible',
      timestamp: new Date().toISOString(),
      status: 'healthy'
    });
  });

  /**
   * POST /webhooks/stripe
   * Handle Stripe webhook events
   */
  fastify.post('/stripe', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const signature = request.headers['stripe-signature'] as string;
      
      if (!signature) {
        return reply.code(400).send({
          error: 'Missing stripe-signature header'
        });
      }

      // Get raw body for signature verification
      const rawBody = (request as any).rawBody || request.body;
      const bodyString = typeof rawBody === 'string' ? rawBody : JSON.stringify(rawBody);

      // Verify webhook signature and construct event
      const event = stripeService.verifyWebhookSignature(bodyString, signature);

      // Process the webhook event
      await stripeService.processWebhookEvent(event);

      // Log successful webhook processing
      request.log.info(`Stripe webhook processed: ${event.type}`, {
        eventId: event.id,
        eventType: event.type
      });

      return reply.code(200).send({ received: true });

    } catch (error) {
      request.log.error('Stripe webhook error:', error);
      
      // Return 400 for signature verification failures
      if (error instanceof Error && error.message.includes('signature verification')) {
        return reply.code(400).send({
          error: 'Webhook signature verification failed'
        });
      }

      // Return 500 for other processing errors
      return reply.code(500).send({
        error: 'Webhook processing failed'
      });
    }
  });


}
