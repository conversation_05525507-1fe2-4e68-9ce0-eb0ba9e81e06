import { FastifyPluginAsync } from 'fastify';
import { getPerformanceStats } from '../middleware/performance';
import { authenticateApi<PERSON>ey } from '../middleware/auth';

export const metricsRoutes: FastifyPluginAsync = async (fastify) => {
  fastify.get('/api/v1/metrics', { preHandler: authenticateApiKey }, async (_request, reply) => {
    // Default to last day stats; support ?range=hour|day|week later if needed
    const stats = await getPerformanceStats('day');
    return reply.send({
      data: stats,
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  });
}; 