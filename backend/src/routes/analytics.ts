/**
 * Analytics Routes
 * 
 * User-facing endpoints for viewing API usage statistics, trends, and limits.
 * Provides comprehensive analytics dashboard data for users to monitor their usage.
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { UsageAnalyticsService } from '../services/UsageAnalyticsService';
import { UsageAlertService } from '../services/UsageAlertService';
import { requireAuth } from '../middleware/jwtAuth';

// Request types
interface AnalyticsQuery {
  startDate?: string;
  endDate?: string;
  apiKeyId?: string;
  endpoint?: string;
  interval?: 'hour' | 'day' | 'week';
  limit?: string;
}

interface ComparisonQuery {
  currentStart: string;
  currentEnd: string;
  previousStart: string;
  previousEnd: string;
}

export async function analyticsRoutes(fastify: FastifyInstance) {
  const analyticsService = new UsageAnalyticsService();
  const alertService = new UsageAlertService();

  // Apply JWT authentication to all analytics routes
  fastify.addHook('preHandler', requireAuth);

  /**
   * GET /api/v1/analytics/dashboard
   * Get comprehensive dashboard data for the authenticated user
   */
  fastify.get('/dashboard', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = (request as any).user;
      
      const dashboardData = await analyticsService.getDashboardData(user.id);
      
      return reply.code(200).send({
        success: true,
        data: dashboardData
      });
    } catch (error) {
      request.log.error('Dashboard data error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch dashboard data'
      });
    }
  });

  /**
   * GET /api/v1/analytics/usage-stats
   * Get usage statistics for the authenticated user
   */
  fastify.get<{ Querystring: AnalyticsQuery }>('/usage-stats', async (request, reply) => {
    try {
      const user = (request as any).user;
      const { startDate, endDate, apiKeyId, endpoint } = request.query;
      
      const stats = await analyticsService.getUsageStats({
        userId: user.id,
        startDate,
        endDate,
        apiKeyId,
        endpoint
      });
      
      return reply.code(200).send({
        success: true,
        data: stats
      });
    } catch (error) {
      request.log.error('Usage stats error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch usage statistics'
      });
    }
  });

  /**
   * GET /api/v1/analytics/time-series
   * Get time series data for usage trends
   */
  fastify.get<{ Querystring: AnalyticsQuery }>('/time-series', async (request, reply) => {
    try {
      const user = (request as any).user;
      const { startDate, endDate, apiKeyId, interval = 'day' } = request.query;
      
      const timeSeriesData = await analyticsService.getTimeSeriesData({
        userId: user.id,
        startDate,
        endDate,
        apiKeyId
      }, interval);
      
      return reply.code(200).send({
        success: true,
        data: timeSeriesData
      });
    } catch (error) {
      request.log.error('Time series data error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch time series data'
      });
    }
  });

  /**
   * GET /api/v1/analytics/top-endpoints
   * Get top endpoints by usage
   */
  fastify.get<{ Querystring: AnalyticsQuery }>('/top-endpoints', async (request, reply) => {
    try {
      const user = (request as any).user;
      const { startDate, endDate, limit = '10' } = request.query;
      
      const topEndpoints = await analyticsService.getTopEndpoints({
        userId: user.id,
        startDate,
        endDate
      }, parseInt(limit));
      
      return reply.code(200).send({
        success: true,
        data: topEndpoints
      });
    } catch (error) {
      request.log.error('Top endpoints error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch top endpoints'
      });
    }
  });

  /**
   * GET /api/v1/analytics/quotas
   * Get usage quotas for the authenticated user
   */
  fastify.get('/quotas', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = (request as any).user;
      
      const quotas = await analyticsService.getUsageQuotas(user.id);
      
      return reply.code(200).send({
        success: true,
        data: quotas
      });
    } catch (error) {
      request.log.error('Usage quotas error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch usage quotas'
      });
    }
  });

  /**
   * GET /api/v1/analytics/alerts
   * Get usage alerts for the authenticated user
   */
  fastify.get<{ Querystring: { includeResolved?: string } }>('/alerts', async (request, reply) => {
    try {
      const user = (request as any).user;
      const { includeResolved = 'false' } = request.query;
      
      const alerts = await analyticsService.getUsageAlerts(
        user.id, 
        includeResolved === 'true'
      );
      
      return reply.code(200).send({
        success: true,
        data: alerts
      });
    } catch (error) {
      request.log.error('Usage alerts error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch usage alerts'
      });
    }
  });

  /**
   * PUT /api/v1/analytics/alerts/:alertId/resolve
   * Resolve a usage alert
   */
  fastify.put<{ Params: { alertId: string } }>('/alerts/:alertId/resolve', async (request, reply) => {
    try {
      const user = (request as any).user;
      const { alertId } = request.params;
      
      // Verify the alert belongs to the user
      const alerts = await analyticsService.getUsageAlerts(user.id, true);
      const alert = alerts.find(a => a.id === alertId);
      
      if (!alert) {
        return reply.code(404).send({
          success: false,
          error: 'Alert not found'
        });
      }
      
      await analyticsService.resolveUsageAlert(alertId);
      
      return reply.code(200).send({
        success: true,
        message: 'Alert resolved successfully'
      });
    } catch (error) {
      request.log.error('Resolve alert error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to resolve alert'
      });
    }
  });

  /**
   * GET /api/v1/analytics/comparison
   * Get usage comparison between two periods
   */
  fastify.get<{ Querystring: ComparisonQuery }>('/comparison', async (request, reply) => {
    try {
      const user = (request as any).user;
      const { currentStart, currentEnd, previousStart, previousEnd } = request.query;
      
      if (!currentStart || !currentEnd || !previousStart || !previousEnd) {
        return reply.code(400).send({
          success: false,
          error: 'All date parameters are required: currentStart, currentEnd, previousStart, previousEnd'
        });
      }
      
      const comparison = await analyticsService.getUsageComparison(
        user.id,
        currentStart,
        currentEnd,
        previousStart,
        previousEnd
      );
      
      return reply.code(200).send({
        success: true,
        data: comparison
      });
    } catch (error) {
      request.log.error('Usage comparison error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch usage comparison'
      });
    }
  });

  /**
   * GET /api/v1/analytics/aggregations
   * Get pre-computed usage aggregations
   */
  fastify.get<{ 
    Querystring: { 
      apiKeyId?: string;
      periodType?: 'minute' | 'hour' | 'day' | 'month';
      limit?: string;
    } 
  }>('/aggregations', async (request, reply) => {
    try {
      const user = (request as any).user;
      const { apiKeyId, periodType = 'day', limit = '30' } = request.query;
      
      const aggregations = await analyticsService.getUsageAggregations(
        user.id,
        apiKeyId,
        periodType,
        parseInt(limit)
      );
      
      return reply.code(200).send({
        success: true,
        data: aggregations
      });
    } catch (error) {
      request.log.error('Usage aggregations error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch usage aggregations'
      });
    }
  });

  /**
   * POST /api/v1/analytics/check-thresholds
   * Manually trigger usage threshold checks
   */
  fastify.post<{ Body: { apiKeyId?: string } }>('/check-thresholds', async (request, reply) => {
    try {
      const user = (request as any).user;
      const { apiKeyId } = request.body || {};
      
      await analyticsService.checkUsageThresholds(user.id, apiKeyId);
      
      return reply.code(200).send({
        success: true,
        message: 'Usage thresholds checked successfully'
      });
    } catch (error) {
      request.log.error('Check thresholds error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to check usage thresholds'
      });
    }
  });

  /**
   * POST /api/v1/analytics/alerts/test-thresholds
   * Manually test usage thresholds for the authenticated user
   */
  fastify.post('/alerts/test-thresholds', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = (request as any).user;

      await alertService.checkUserUsageThresholds(user.id);

      return reply.code(200).send({
        success: true,
        message: 'Usage thresholds checked and alerts generated if needed'
      });
    } catch (error) {
      request.log.error('Test thresholds error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to test usage thresholds'
      });
    }
  });

  /**
   * GET /api/v1/analytics/alerts/monitoring-stats
   * Get monitoring system statistics
   */
  fastify.get('/alerts/monitoring-stats', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const stats = await alertService.getMonitoringStats();

      return reply.code(200).send({
        success: true,
        data: stats
      });
    } catch (error) {
      request.log.error('Monitoring stats error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch monitoring statistics'
      });
    }
  });
}
