/**
 * Admin Routes Index
 *
 * Centralized registration of all admin-only routes.
 * All routes under /admin require ADMIN role authentication.
 */

import { FastifyInstance } from 'fastify';
import { adminAnalyticsRoutes } from './analytics';
import { adminUserRoutes } from './users';
import { adminSubscriptionRoutes } from './subscriptions';
import { adminSystemRoutes } from './system';

export async function adminRoutes(fastify: FastifyInstance) {
  // Register admin analytics routes
  await fastify.register(adminAnalyticsRoutes, { prefix: '/analytics' });

  // Register admin user management routes
  await fastify.register(adminUserRoutes, { prefix: '/users' });

  // Register admin subscription management routes
  await fastify.register(adminSubscriptionRoutes, { prefix: '/subscriptions' });

  // Register admin system management routes
  await fastify.register(adminSystemRoutes, { prefix: '/system' });
}
