/**
 * Admin Subscription Management Routes
 * 
 * Admin-only endpoints for comprehensive subscription management including:
 * - Subscription listing with advanced filtering
 * - Subscription status management
 * - Manual subscription operations
 * - Payment issue resolution
 * - Subscription analytics
 */

import { FastifyPluginAsync } from 'fastify';
import { SubscriptionService } from '../../services/SubscriptionService';
import { requireAuth, requireRole } from '../../middleware/jwtAuth';

// Lazy initialization to avoid database pool issues
let subscriptionService: SubscriptionService;
function getSubscriptionService(): SubscriptionService {
  if (!subscriptionService) {
    subscriptionService = new SubscriptionService();
  }
  return subscriptionService;
}

// Helper function to ensure user is authenticated admin
function ensureAdmin(request: any): { id: string; role: string } {
  if (!request.user || request.user.role !== 'ADMIN') {
    throw new Error('Admin access required');
  }
  return request.user;
}

export const adminSubscriptionRoutes: FastifyPluginAsync = async (fastify) => {
  // Apply JWT authentication and admin role requirement to all routes
  fastify.addHook('preHandler', requireAuth);
  fastify.addHook('preHandler', requireRole('ADMIN'));

  // =============================================================================
  // SUBSCRIPTION LISTING AND SEARCH
  // =============================================================================

  /**
   * GET /admin/subscriptions
   * Get all subscriptions with advanced filtering
   */
  fastify.get('/', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const query = request.query as {
        page?: number;
        limit?: number;
        status?: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing';
        plan_id?: string;
        user_id?: string;
        expiring_in_days?: number;
        created_after?: string;
        created_before?: string;
        updated_after?: string;
        updated_before?: string;
        stripe_customer_id?: string;
        cancel_at_period_end?: boolean;
        sort_by?: 'created_at' | 'updated_at' | 'current_period_end' | 'user_email';
        sort_order?: 'asc' | 'desc';
      };

      const service = getSubscriptionService();
      const result = await service.getSubscriptionsForAdmin({
        page: query.page || 1,
        limit: Math.min(query.limit || 50, 100),
        ...(query.status && { status: query.status }),
        ...(query.plan_id && { planId: query.plan_id }),
        ...(query.user_id && { userId: query.user_id }),
        ...(query.expiring_in_days && { expiringInDays: query.expiring_in_days }),
        ...(query.created_after && { createdAfter: new Date(query.created_after) }),
        ...(query.created_before && { createdBefore: new Date(query.created_before) }),
        ...(query.updated_after && { updatedAfter: new Date(query.updated_after) }),
        ...(query.updated_before && { updatedBefore: new Date(query.updated_before) }),
        ...(query.stripe_customer_id && { stripeCustomerId: query.stripe_customer_id }),
        ...(query.cancel_at_period_end !== undefined && { cancelAtPeriodEnd: query.cancel_at_period_end }),
        sortBy: query.sort_by || 'created_at',
        sortOrder: query.sort_order || 'desc',
      });

      return {
        success: true,
        data: result.subscriptions,
        pagination: result.pagination,
        summary: {
          total_active: result.summary?.totalActive || 0,
          total_past_due: result.summary?.totalPastDue || 0,
          total_canceled: result.summary?.totalCanceled || 0,
          expiring_soon: result.summary?.expiringSoon || 0,
          revenue_this_month: result.summary?.revenueThisMonth || 0
        }
      };
    } catch (error) {
      fastify.log.error('Admin subscription listing failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve subscriptions',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * GET /admin/subscriptions/:id
   * Get detailed subscription information
   */
  fastify.get('/:id', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const { id } = request.params as { id: string };
      const service = getSubscriptionService();
      
      const subscription = await service.getSubscriptionDetailsForAdmin(id);
      if (!subscription) {
        return reply.status(404).send({
          success: false,
          error: 'Subscription not found'
        });
      }

      return {
        success: true,
        data: subscription
      };
    } catch (error) {
      fastify.log.error('Admin subscription details failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve subscription details',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =============================================================================
  // SUBSCRIPTION MANAGEMENT OPERATIONS
  // =============================================================================

  /**
   * PATCH /admin/subscriptions/:id/status
   * Manually change subscription status
   */
  fastify.patch('/:id/status', async (request, reply) => {
    try {
      const admin = ensureAdmin(request);
      const { id } = request.params as { id: string };
      const { status, reason, notes } = request.body as {
        status: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing';
        reason: string;
        notes?: string;
      };

      const service = getSubscriptionService();
      const updatedSubscription = await service.updateSubscriptionStatus(id, status, {
        reason,
        ...(notes && { notes }),
        adminId: admin.id
      });

      return {
        success: true,
        data: updatedSubscription,
        message: 'Subscription status updated successfully'
      };
    } catch (error) {
      fastify.log.error('Admin subscription status update failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to update subscription status',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * POST /admin/subscriptions/:id/extend
   * Extend subscription period
   */
  fastify.post('/:id/extend', async (request, reply) => {
    try {
      const admin = ensureAdmin(request);
      const { id } = request.params as { id: string };
      const { days, reason, notes } = request.body as {
        days: number;
        reason: string;
        notes?: string;
      };

      const service = getSubscriptionService();
      const updatedSubscription = await service.extendSubscription(id, days, {
        reason,
        ...(notes && { notes }),
        adminId: admin.id
      });

      return {
        success: true,
        data: updatedSubscription,
        message: `Subscription extended by ${days} days`
      };
    } catch (error) {
      fastify.log.error('Admin subscription extension failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to extend subscription',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * POST /admin/subscriptions/bulk-action
   * Perform bulk operations on subscriptions
   */
  fastify.post('/bulk-action', async (request, reply) => {
    try {
      const admin = ensureAdmin(request);
      const { action, subscription_ids, reason, notes } = request.body as {
        action: 'suspend' | 'reactivate' | 'cancel' | 'extend';
        subscription_ids: string[];
        reason: string;
        notes?: string;
        days?: number; // for extend action
      };

      if (!subscription_ids || subscription_ids.length === 0) {
        return reply.status(400).send({
          success: false,
          error: 'No subscription IDs provided'
        });
      }

      const service = getSubscriptionService();
      const results = await service.bulkSubscriptionAction(action, subscription_ids, {
        reason,
        ...(notes && { notes }),
        adminId: admin.id,
        ...((request.body as any).days && { days: (request.body as any).days })
      });

      return {
        success: true,
        data: results,
        message: `Bulk ${action} operation completed`
      };
    } catch (error) {
      fastify.log.error('Admin bulk subscription operation failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to perform bulk operation',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =============================================================================
  // AUDIT AND HISTORY
  // =============================================================================

  /**
   * GET /admin/subscriptions/:id/audit-log
   * Get subscription change history
   */
  fastify.get('/:id/audit-log', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const { id } = request.params as { id: string };
      const query = request.query as {
        page?: number;
        limit?: number;
        change_type?: string;
      };

      const service = getSubscriptionService();
      const auditLog = await service.getSubscriptionAuditLog(id, {
        page: query.page || 1,
        limit: Math.min(query.limit || 50, 100),
        ...(query.change_type && { changeType: query.change_type })
      });

      return {
        success: true,
        data: auditLog.entries,
        pagination: auditLog.pagination
      };
    } catch (error) {
      fastify.log.error('Admin subscription audit log failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve audit log',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * GET /admin/subscriptions/expiring
   * Get subscriptions expiring soon
   */
  fastify.get('/expiring', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const query = request.query as {
        days?: number;
        page?: number;
        limit?: number;
      };

      const service = getSubscriptionService();
      const result = await service.getExpiringSubscriptions({
        days: query.days || 7,
        page: query.page || 1,
        limit: Math.min(query.limit || 50, 100)
      });

      return {
        success: true,
        data: result.subscriptions,
        pagination: result.pagination,
        summary: {
          expiring_count: result.pagination.total,
          days_threshold: query.days || 7
        }
      };
    } catch (error) {
      fastify.log.error('Admin expiring subscriptions failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve expiring subscriptions',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =============================================================================
  // SUBSCRIPTION STATISTICS
  // =============================================================================

  /**
   * GET /admin/subscriptions/stats
   * Get comprehensive subscription statistics
   */
  fastify.get('/stats', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const service = getSubscriptionService();
      const stats = await service.getSubscriptionStatistics();

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      fastify.log.error('Admin subscription statistics failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve subscription statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
};
