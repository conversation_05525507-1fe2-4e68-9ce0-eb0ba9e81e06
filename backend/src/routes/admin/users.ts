/**
 * Admin User Management Routes
 * 
 * Admin-only endpoints for comprehensive user management including:
 * - User listing with advanced filtering
 * - User details and profile management
 * - User status management (activate/suspend)
 * - Role management
 * - Bulk operations
 */

import { FastifyPluginAsync } from 'fastify';
import { UserService } from '../../services/UserService';
import { requireAuth, requireRole } from '../../middleware/jwtAuth';

// Lazy initialization to avoid database pool issues
let userService: UserService;
function getUserService(): UserService {
  if (!userService) {
    userService = new UserService();
  }
  return userService;
}

// Helper function to ensure user is authenticated admin
function ensureAdmin(request: any): { id: string; role: string } {
  if (!request.user || request.user.role !== 'ADMIN') {
    throw new Error('Admin access required');
  }
  return request.user;
}

export const adminUserRoutes: FastifyPluginAsync = async (fastify) => {
  // Apply JWT authentication and admin role requirement to all routes
  fastify.addHook('preHandler', requireAuth);
  fastify.addHook('preHandler', requireRole('ADMIN'));

  // =============================================================================
  // USER LISTING AND SEARCH
  // =============================================================================

  /**
   * GET /admin/users
   * Get all users with advanced filtering and pagination
   */
  fastify.get('/', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const query = request.query as {
        page?: number;
        limit?: number;
        role?: 'ADMIN' | 'CUSTOMER';
        status?: 'active' | 'inactive';
        search?: string;
        sort_by?: 'created_at' | 'updated_at' | 'email' | 'last_login_at';
        sort_order?: 'asc' | 'desc';
        created_after?: string;
        created_before?: string;
        last_login_after?: string;
        last_login_before?: string;
      };

      const service = getUserService();
      const result = await service.getUsers({
        page: query.page || 1,
        limit: Math.min(query.limit || 50, 100),
        ...(query.role && { role: query.role }),
        ...(query.status === 'active' && { isActive: true }),
        ...(query.status === 'inactive' && { isActive: false }),
        ...(query.search && { search: query.search }),
        sortBy: query.sort_by || 'created_at',
        sortOrder: query.sort_order || 'desc',
        ...(query.created_after && { createdAfter: new Date(query.created_after) }),
        ...(query.created_before && { createdBefore: new Date(query.created_before) }),
        ...(query.last_login_after && { lastLoginAfter: new Date(query.last_login_after) }),
        ...(query.last_login_before && { lastLoginBefore: new Date(query.last_login_before) }),
      });

      return {
        success: true,
        data: result.users,
        pagination: result.pagination,
        summary: {
          total_users: result.pagination.total,
          active_users: result.summary?.activeUsers || 0,
          inactive_users: result.summary?.inactiveUsers || 0,
          admin_users: result.summary?.adminUsers || 0,
          customer_users: result.summary?.customerUsers || 0,
        }
      };
    } catch (error) {
      fastify.log.error('Admin user listing failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve users',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * GET /admin/users/:id
   * Get detailed user information by ID
   */
  fastify.get('/:id', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const { id } = request.params as { id: string };
      const service = getUserService();
      
      const user = await service.getUserById(id);
      if (!user) {
        return reply.status(404).send({
          success: false,
          error: 'User not found'
        });
      }

      // Get additional user details
      const [subscriptions, apiKeys, usage] = await Promise.all([
        service.getUserSubscriptions(id),
        service.getUserApiKeys(id),
        service.getUserUsageStats(id)
      ]);

      return {
        success: true,
        data: {
          ...user,
          subscriptions,
          api_keys: apiKeys,
          usage_stats: usage
        }
      };
    } catch (error) {
      fastify.log.error('Admin user details failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve user details',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =============================================================================
  // USER MANAGEMENT OPERATIONS
  // =============================================================================

  /**
   * PATCH /admin/users/:id
   * Update user profile and settings
   */
  fastify.patch('/:id', async (request, reply) => {
    try {
      ensureAdmin(request);
      const { id } = request.params as { id: string };
      
      const updateData = request.body as {
        email?: string;
        role?: 'ADMIN' | 'CUSTOMER';
        is_active?: boolean;
        admin_notes?: string;
      };

      const service = getUserService();
      const updatedUser = await service.updateUser(id, updateData);

      return {
        success: true,
        data: updatedUser,
        message: 'User updated successfully'
      };
    } catch (error) {
      fastify.log.error('Admin user update failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to update user',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * POST /admin/users/:id/suspend
   * Suspend user account
   */
  fastify.post('/:id/suspend', async (request, reply) => {
    try {
      const admin = ensureAdmin(request);
      const { id } = request.params as { id: string };
      const { reason, notes } = request.body as { reason: string; notes?: string };

      const service = getUserService();
      await service.suspendUser(id, reason, notes, admin.id);

      return {
        success: true,
        message: 'User suspended successfully'
      };
    } catch (error) {
      fastify.log.error('Admin user suspension failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to suspend user',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * POST /admin/users/:id/activate
   * Activate suspended user account
   */
  fastify.post('/:id/activate', async (request, reply) => {
    try {
      const admin = ensureAdmin(request);
      const { id } = request.params as { id: string };
      const { notes } = request.body as { notes?: string };

      const service = getUserService();
      await service.activateUser(id, notes, admin.id);

      return {
        success: true,
        message: 'User activated successfully'
      };
    } catch (error) {
      fastify.log.error('Admin user activation failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to activate user',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * DELETE /admin/users/:id
   * Delete user account (soft delete)
   */
  fastify.delete('/:id', async (request, reply) => {
    try {
      ensureAdmin(request);
      const { id } = request.params as { id: string };

      const service = getUserService();
      await service.deleteUser(id);

      return {
        success: true,
        message: 'User deleted successfully'
      };
    } catch (error) {
      fastify.log.error('Admin user deletion failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to delete user',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =============================================================================
  // BULK OPERATIONS
  // =============================================================================

  /**
   * POST /admin/users/bulk-action
   * Perform bulk operations on multiple users
   */
  fastify.post('/bulk-action', async (request, reply) => {
    try {
      const admin = ensureAdmin(request);
      const { action, user_ids, reason, notes } = request.body as {
        action: 'suspend' | 'activate' | 'delete' | 'change_role';
        user_ids: string[];
        reason: string;
        notes?: string;
        new_role?: 'ADMIN' | 'CUSTOMER';
      };

      if (!user_ids || user_ids.length === 0) {
        return reply.status(400).send({
          success: false,
          error: 'No user IDs provided'
        });
      }

      const service = getUserService();
      const results = await service.bulkUserAction(action, user_ids, {
        reason,
        ...(notes && { notes }),
        adminId: admin.id,
        ...((request.body as any).new_role && { newRole: (request.body as any).new_role })
      });

      return {
        success: true,
        data: results,
        message: `Bulk ${action} operation completed`
      };
    } catch (error) {
      fastify.log.error('Admin bulk user operation failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to perform bulk operation',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =============================================================================
  // USER STATISTICS
  // =============================================================================

  /**
   * GET /admin/users/stats
   * Get comprehensive user statistics
   */
  fastify.get('/stats', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const service = getUserService();
      const stats = await service.getUserStatistics();

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      fastify.log.error('Admin user statistics failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve user statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
};
