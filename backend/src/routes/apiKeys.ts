/**
 * API Key Management Routes
 * 
 * User-facing API key management endpoints for self-service key creation,
 * listing, updating, and revocation. Requires JWT authentication.
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { ApiKeyService, CreateApiKeyRequest, UpdateApiKeyRequest } from '../services/ApiKeyService';
import { JWTAuthMiddleware } from '../middleware/jwtAuth';

// Lazy initialization to avoid database pool issues
let apiKeyService: ApiKeyService;
function getApiKeyService(): ApiKeyService {
  if (!apiKeyService) {
    apiKeyService = new ApiKeyService();
  }
  return apiKeyService;
}

// JWT Auth middleware instance
const jwtAuth = new JWTAuthMiddleware();

// Helper function to ensure user is authenticated
function ensureUser(request: any): { id: string; role: string } {
  if (!request.user) {
    throw new Error('User not authenticated');
  }
  return request.user;
}

export async function apiKeyRoutes(fastify: FastifyInstance) {
  
  // =============================================================================
  // USER API KEY MANAGEMENT
  // =============================================================================

  /**
   * GET /my-api-keys
   * List user's API keys
   */
  fastify.get('/my-api-keys', {
    preHandler: jwtAuth.authenticate(),
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  description: { type: 'string' },
                  key_preview: { type: 'string' },
                  total_requests: { type: 'number' },
                  requests_this_month: { type: 'number' },
                  last_reset_date: { type: 'string' },
                  rate_limit_per_minute: { type: 'number' },
                  rate_limit_per_day: { type: 'number' },
                  rate_limit_burst: { type: 'number' },
                  allowed_ips: { type: 'array', items: { type: 'string' } },
                  allowed_domains: { type: 'array', items: { type: 'string' } },
                  last_used_at: { type: 'string' },
                  last_used_ip: { type: 'string' },
                  is_active: { type: 'boolean' },
                  expires_at: { type: 'string' },
                  created_at: { type: 'string' },
                  updated_at: { type: 'string' }
                }
              }
            },
            count: { type: 'number' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = ensureUser(request);
      const apiKeys = await getApiKeyService().getUserApiKeys(user.id);

      return reply.send({
        success: true,
        data: apiKeys,
        count: apiKeys.length
      });

    } catch (error) {
      request.log.error('Get API keys error:', error);
      return reply.status(500).send({
        error: 'INTERNAL_ERROR',
        message: 'Failed to retrieve API keys',
        requestId: request.id
      });
    }
  });

  /**
   * POST /my-api-keys
   * Create new API key
   */
  fastify.post<{ Body: CreateApiKeyRequest }>('/my-api-keys', {
    preHandler: jwtAuth.authenticate(),
    schema: {
      body: {
        type: 'object',
        required: ['name'],
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 255 },
          description: { type: 'string', maxLength: 1000 },
          allowed_ips: { 
            type: 'array', 
            items: { type: 'string' },
            maxItems: 10
          },
          allowed_domains: { 
            type: 'array', 
            items: { type: 'string' },
            maxItems: 10
          },
          expires_at: { type: 'string', format: 'date-time' }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                apiKey: { type: 'object' },
                secret: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: CreateApiKeyRequest }>, reply: FastifyReply) => {
    try {
      const user = ensureUser(request);
      const keyData = request.body;

      const result = await getApiKeyService().createApiKey(user.id, keyData);

      request.log.info(`API key created: ${result.apiKey.name} for user ${user.id}`);

      return reply.status(201).send({
        success: true,
        data: result
      });

    } catch (error) {
      request.log.error('Create API key error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('Maximum API keys limit')) {
          return reply.status(400).send({
            error: 'API_KEY_LIMIT_EXCEEDED',
            message: error.message,
            requestId: request.id
          });
        }
        if (error.message.includes('No active subscription found')) {
          return reply.status(400).send({
            error: 'NO_ACTIVE_SUBSCRIPTION',
            message: 'An active subscription is required to create API keys',
            requestId: request.id
          });
        }
      }

      return reply.status(500).send({
        error: 'INTERNAL_ERROR',
        message: 'Failed to create API key',
        requestId: request.id
      });
    }
  });

  /**
   * GET /my-api-keys/:id
   * Get specific API key details
   */
  fastify.get<{ Params: { id: string } }>('/my-api-keys/:id', {
    preHandler: jwtAuth.authenticate(),
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = ensureUser(request);
      const { id } = request.params;

      const apiKey = await getApiKeyService().getUserApiKeyById(user.id, id);
      if (!apiKey) {
        return reply.status(404).send({
          error: 'API_KEY_NOT_FOUND',
          message: 'API key not found',
          requestId: request.id
        });
      }

      return reply.send({
        success: true,
        data: apiKey
      });

    } catch (error) {
      request.log.error('Get API key error:', error);
      return reply.status(500).send({
        error: 'INTERNAL_ERROR',
        message: 'Failed to retrieve API key',
        requestId: request.id
      });
    }
  });

  /**
   * PUT /my-api-keys/:id
   * Update API key
   */
  fastify.put<{ Params: { id: string }; Body: UpdateApiKeyRequest }>('/my-api-keys/:id', {
    preHandler: jwtAuth.authenticate(),
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 255 },
          description: { type: 'string', maxLength: 1000 },
          allowed_ips: { 
            type: 'array', 
            items: { type: 'string' },
            maxItems: 10
          },
          allowed_domains: { 
            type: 'array', 
            items: { type: 'string' },
            maxItems: 10
          },
          is_active: { type: 'boolean' },
          expires_at: { type: 'string', format: 'date-time' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string }; Body: UpdateApiKeyRequest }>, reply: FastifyReply) => {
    try {
      const user = ensureUser(request);
      const { id } = request.params;
      const updates = request.body;

      const updatedKey = await getApiKeyService().updateApiKey(user.id, id, updates);
      if (!updatedKey) {
        return reply.status(404).send({
          error: 'API_KEY_NOT_FOUND',
          message: 'API key not found',
          requestId: request.id
        });
      }

      request.log.info(`API key updated: ${id} for user ${user.id}`);

      return reply.send({
        success: true,
        data: updatedKey
      });

    } catch (error) {
      request.log.error('Update API key error:', error);
      return reply.status(500).send({
        error: 'INTERNAL_ERROR',
        message: 'Failed to update API key',
        requestId: request.id
      });
    }
  });

  /**
   * DELETE /my-api-keys/:id
   * Revoke (deactivate) API key
   */
  fastify.delete<{ Params: { id: string } }>('/my-api-keys/:id', {
    preHandler: jwtAuth.authenticate(),
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = ensureUser(request);
      const { id } = request.params;

      const success = await getApiKeyService().revokeApiKey(user.id, id);
      if (!success) {
        return reply.status(404).send({
          error: 'API_KEY_NOT_FOUND',
          message: 'API key not found',
          requestId: request.id
        });
      }

      request.log.info(`API key revoked: ${id} for user ${user.id}`);

      return reply.send({
        success: true,
        message: 'API key revoked successfully'
      });

    } catch (error) {
      request.log.error('Revoke API key error:', error);
      return reply.status(500).send({
        error: 'INTERNAL_ERROR',
        message: 'Failed to revoke API key',
        requestId: request.id
      });
    }
  });

  /**
   * POST /my-api-keys/:id/regenerate
   * Regenerate API key secret
   */
  fastify.post<{ Params: { id: string } }>('/my-api-keys/:id/regenerate', {
    preHandler: jwtAuth.authenticate(),
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = ensureUser(request);
      const { id } = request.params;

      const result = await getApiKeyService().regenerateApiKey(user.id, id);
      if (!result) {
        return reply.status(404).send({
          error: 'API_KEY_NOT_FOUND',
          message: 'API key not found',
          requestId: request.id
        });
      }

      request.log.info(`API key regenerated: ${id} for user ${user.id}`);

      return reply.send({
        success: true,
        data: result
      });

    } catch (error) {
      request.log.error('Regenerate API key error:', error);
      return reply.status(500).send({
        error: 'INTERNAL_ERROR',
        message: 'Failed to regenerate API key',
        requestId: request.id
      });
    }
  });

  /**
   * GET /my-api-keys/:id/usage
   * Get API key usage statistics
   */
  fastify.get<{ Params: { id: string } }>('/my-api-keys/:id/usage', {
    preHandler: jwtAuth.authenticate(),
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = ensureUser(request);
      const { id } = request.params;

      const usage = await getApiKeyService().getApiKeyUsage(user.id, id);
      if (!usage) {
        return reply.status(404).send({
          error: 'API_KEY_NOT_FOUND',
          message: 'API key not found',
          requestId: request.id
        });
      }

      return reply.send({
        success: true,
        data: usage
      });

    } catch (error) {
      request.log.error('Get API key usage error:', error);
      return reply.status(500).send({
        error: 'INTERNAL_ERROR',
        message: 'Failed to retrieve API key usage',
        requestId: request.id
      });
    }
  });
}
