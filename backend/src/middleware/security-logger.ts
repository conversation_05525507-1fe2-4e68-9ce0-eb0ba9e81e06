/**
 * Enhanced security logging middleware for monitoring and alerting
 */

import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { validateEnvironment } from '../config';

export interface SecurityEvent {
  timestamp: string;
  level: 'SECURITY' | 'WARNING' | 'INFO';
  event: string;
  ip: string;
  userAgent?: string;
  method: string;
  url: string;
  statusCode?: number;
  requestId: string;
  apiKey?: 'present' | 'missing' | 'invalid';
  details?: any;
}

export interface SecurityMetrics {
  authenticationFailures: number;
  rateLimitViolations: number;
  suspiciousRequests: number;
  lastReset: Date;
}

// In-memory security metrics (in production, use Redis or database)
let securityMetrics: SecurityMetrics = {
  authenticationFailures: 0,
  rateLimitViolations: 0,
  suspiciousRequests: 0,
  lastReset: new Date()
};

// Suspicious patterns to detect
const SUSPICIOUS_PATTERNS = [
  // SQL injection attempts
  /(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i,
  
  // XSS attempts
  /<script[^>]*>.*?<\/script>/i,
  /javascript:/i,
  /on\w+\s*=/i,
  
  // Path traversal
  /\.\.\//,
  /\.\.\\/,
  /%2e%2e%2f/i,
  
  // Command injection
  /[;&|`$()]/,
  
  // Common attack tools
  /sqlmap/i,
  /nmap/i,
  /nikto/i,
  /burp/i,
  /metasploit/i,
  
  // Suspicious user agents
  /bot|crawler|spider|scraper/i
];

/**
 * Log security event
 */
export function logSecurityEvent(
  event: string,
  request: FastifyRequest,
  reply?: FastifyReply,
  details?: any,
  level: 'SECURITY' | 'WARNING' | 'INFO' = 'SECURITY'
) {
  const securityEvent: SecurityEvent = {
    timestamp: new Date().toISOString(),
    level,
    event,
    ip: request.ip,
    userAgent: request.headers['user-agent'],
    method: request.method,
    url: request.url,
    statusCode: reply?.statusCode,
    requestId: (request as any).requestId || 'unknown',
    apiKey: getApiKeyStatus(request),
    ...(details && { details })
  };

  console.log(JSON.stringify(securityEvent));
  
  // Update metrics
  updateSecurityMetrics(event);
}

/**
 * Get API key status from request
 */
function getApiKeyStatus(request: FastifyRequest): 'present' | 'missing' | 'invalid' {
  const authHeader = request.headers.authorization;
  const apiKeyHeader = request.headers['x-api-key'];
  
  if (authHeader || apiKeyHeader) {
    return 'present'; // We'll determine validity in auth middleware
  }
  
  return 'missing';
}

/**
 * Update security metrics and trigger notifications for high-risk events
 */
function updateSecurityMetrics(event: string) {
  const now = new Date();

  // Reset metrics daily
  if (now.getTime() - securityMetrics.lastReset.getTime() > 24 * 60 * 60 * 1000) {
    securityMetrics = {
      authenticationFailures: 0,
      rateLimitViolations: 0,
      suspiciousRequests: 0,
      lastReset: now
    };
  }

  switch (event) {
    case 'AUTHENTICATION_FAILED':
    case 'INVALID_API_KEY':
    case 'MISSING_API_KEY':
      securityMetrics.authenticationFailures++;
      // Notify on high authentication failure rate
      if (securityMetrics.authenticationFailures >= 10) {
        notifySecurityIncident('High Authentication Failure Rate', {
          failureCount: securityMetrics.authenticationFailures,
          timeWindow: '24 hours',
          event
        });
      }
      break;
    case 'RATE_LIMIT_EXCEEDED':
      securityMetrics.rateLimitViolations++;
      // Notify on high rate limit violations
      if (securityMetrics.rateLimitViolations >= 5) {
        notifySecurityIncident('High Rate Limit Violation Rate', {
          violationCount: securityMetrics.rateLimitViolations,
          timeWindow: '24 hours',
          event
        });
      }
      break;
    case 'SUSPICIOUS_REQUEST':
      securityMetrics.suspiciousRequests++;
      // Notify on high suspicious request rate
      if (securityMetrics.suspiciousRequests >= 20) {
        notifySecurityIncident('High Suspicious Request Rate', {
          suspiciousCount: securityMetrics.suspiciousRequests,
          timeWindow: '24 hours',
          event
        });
      }
      break;
  }
}

/**
 * Notify about security incidents
 */
async function notifySecurityIncident(incident: string, details: Record<string, any>) {
  try {
    const { getNotificationService } = await import('../services/notification-service');
    const notificationService = getNotificationService();
    await notificationService.notifySecurityIncident(incident, details, 'warning');
  } catch (error) {
    console.error('Failed to send security incident notification:', error);
  }
}

/**
 * Check if request contains suspicious patterns
 */
export function detectSuspiciousRequest(request: FastifyRequest): boolean {
  const url = request.url;
  const userAgent = request.headers['user-agent'] || '';
  const queryString = new URL(url, 'http://localhost').search;
  
  // Check URL and query parameters
  for (const pattern of SUSPICIOUS_PATTERNS) {
    if (pattern.test(url) || pattern.test(queryString) || pattern.test(userAgent)) {
      return true;
    }
  }
  
  // Check for excessive query parameters (potential parameter pollution)
  const searchParams = new URLSearchParams(queryString);
  if (Array.from(searchParams.keys()).length > 20) {
    return true;
  }
  
  // Check for unusually long URLs
  if (url.length > 2000) {
    return true;
  }
  
  return false;
}

/**
 * Log authentication failure
 */
export function logAuthenticationFailure(
  request: FastifyRequest,
  reason: 'missing_key' | 'invalid_key' | 'expired_key' | 'rate_limited'
) {
  const eventMap = {
    missing_key: 'MISSING_API_KEY',
    invalid_key: 'INVALID_API_KEY',
    expired_key: 'EXPIRED_API_KEY',
    rate_limited: 'RATE_LIMIT_EXCEEDED'
  };
  
  logSecurityEvent(eventMap[reason], request, undefined, { reason });
}

/**
 * Log successful authentication
 */
export function logAuthenticationSuccess(request: FastifyRequest, apiKeyId: string) {
  logSecurityEvent('AUTHENTICATION_SUCCESS', request, undefined, { apiKeyId }, 'INFO');
}

/**
 * Get current security metrics
 */
export function getSecurityMetrics(): SecurityMetrics {
  return { ...securityMetrics };
}

/**
 * Security logging middleware
 */
export const securityLoggingMiddleware: FastifyPluginAsync = async (fastify) => {
  // Log all requests for security monitoring
  fastify.addHook('onRequest', async (request) => {
    // Check for suspicious patterns
    if (detectSuspiciousRequest(request)) {
      logSecurityEvent('SUSPICIOUS_REQUEST', request, undefined, {
        suspiciousUrl: request.url,
        userAgent: request.headers['user-agent']
      }, 'WARNING');
    }
  });

  // Log response status codes for security analysis
  fastify.addHook('onSend', async (request, reply) => {
    const statusCode = reply.statusCode;
    
    // Log security-relevant status codes
    if (statusCode === 401) {
      logSecurityEvent('UNAUTHORIZED_ACCESS', request, reply);
    } else if (statusCode === 403) {
      logSecurityEvent('FORBIDDEN_ACCESS', request, reply);
    } else if (statusCode === 429) {
      logSecurityEvent('RATE_LIMIT_EXCEEDED', request, reply);
    } else if (statusCode >= 500) {
      logSecurityEvent('SERVER_ERROR', request, reply, undefined, 'WARNING');
    }
  });
};

/**
 * Generate security report
 */
export function generateSecurityReport(): any {
  const env = validateEnvironment();
  const metrics = getSecurityMetrics();
  
  return {
    timestamp: new Date().toISOString(),
    environment: env.NODE_ENV,
    period: {
      start: metrics.lastReset.toISOString(),
      end: new Date().toISOString()
    },
    metrics: {
      authenticationFailures: metrics.authenticationFailures,
      rateLimitViolations: metrics.rateLimitViolations,
      suspiciousRequests: metrics.suspiciousRequests
    },
    alerts: generateSecurityAlerts(metrics)
  };
}

/**
 * Generate security alerts based on metrics
 */
function generateSecurityAlerts(metrics: SecurityMetrics): string[] {
  const alerts: string[] = [];
  
  if (metrics.authenticationFailures > 100) {
    alerts.push('HIGH: Excessive authentication failures detected');
  } else if (metrics.authenticationFailures > 50) {
    alerts.push('MEDIUM: Elevated authentication failures');
  }
  
  if (metrics.rateLimitViolations > 50) {
    alerts.push('HIGH: Excessive rate limit violations');
  } else if (metrics.rateLimitViolations > 20) {
    alerts.push('MEDIUM: Elevated rate limit violations');
  }
  
  if (metrics.suspiciousRequests > 20) {
    alerts.push('HIGH: Multiple suspicious requests detected');
  } else if (metrics.suspiciousRequests > 10) {
    alerts.push('MEDIUM: Suspicious request activity');
  }
  
  return alerts;
}

/**
 * Reset security metrics (for testing or manual reset)
 */
export function resetSecurityMetrics() {
  securityMetrics = {
    authenticationFailures: 0,
    rateLimitViolations: 0,
    suspiciousRequests: 0,
    lastReset: new Date()
  };
}
