/**
 * JWT Authentication Middleware
 * 
 * Handles JWT token validation for SaaS endpoints while preserving
 * existing API key authentication for legacy endpoints.
 */

import { FastifyRequest, FastifyReply } from 'fastify';
import { JWTService } from '../services/JWTService';
import { UserService } from '../services/UserService';
import { 
  JWTPayload, 
  UserProfile, 
  UserRole,
  AuthMiddlewareOptions 
} from '../types/auth';

// Extend FastifyRequest to include user data
declare module 'fastify' {
  interface FastifyRequest {
    user?: UserProfile;
    token?: JWTPayload;
    authMethod?: 'jwt' | 'api_key';
  }
}

export class JWTAuthMiddleware {
  private jwtService?: JWTService;
  private userService?: UserService;

  constructor() {
    // Services will be lazy-loaded when needed
  }

  private getJWTService(): JWTService {
    if (!this.jwtService) {
      this.jwtService = new JWTService();
    }
    return this.jwtService;
  }

  private getUserService(): UserService {
    if (!this.userService) {
      this.userService = new UserService();
    }
    return this.userService;
  }

  /**
   * JWT authentication middleware
   */
  authenticate(options: AuthMiddlewareOptions = {}) {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      const { required = true, roles, allowApiKey = false } = options;

      try {
        // Try JWT authentication first
        const jwtResult = await this.tryJWTAuth(request);
        
        if (jwtResult.success) {
          request.user = jwtResult.user!;
          request.token = jwtResult.token!;
          request.authMethod = 'jwt';
          
          // Check role requirements
          if (roles && !roles.includes(request.user.role)) {
            return reply.status(403).send({
              error: 'INSUFFICIENT_PERMISSIONS',
              message: 'Insufficient permissions to access this resource',
              requestId: request.id
            });
          }
          
          return; // Authentication successful
        }

        // Try API key authentication if allowed
        if (allowApiKey) {
          const apiKeyResult = await this.tryApiKeyAuth(request);
          
          if (apiKeyResult.success) {
            request.authMethod = 'api_key';
            // API key auth doesn't set user/token as it's handled by existing middleware
            return;
          }
        }

        // If authentication is required and both methods failed
        if (required) {
          return reply.status(401).send({
            error: 'AUTHENTICATION_REQUIRED',
            message: 'Valid authentication token required',
            requestId: request.id
          });
        }

      } catch (error) {
        request.log.error('Authentication middleware error:', error);
        
        if (required) {
          return reply.status(500).send({
            error: 'AUTHENTICATION_ERROR',
            message: 'Authentication service temporarily unavailable',
            requestId: request.id
          });
        }
      }
    };
  }

  /**
   * Role-based authorization middleware
   */
  requireRole(roles: UserRole | UserRole[]) {
    const roleArray = Array.isArray(roles) ? roles : [roles];
    
    return async (request: FastifyRequest, reply: FastifyReply) => {
      if (!request.user) {
        return reply.status(401).send({
          error: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required',
          requestId: request.id
        });
      }

      if (!roleArray.includes(request.user.role)) {
        return reply.status(403).send({
          error: 'INSUFFICIENT_PERMISSIONS',
          message: `Access denied. Required role(s): ${roleArray.join(', ')}`,
          requestId: request.id
        });
      }
    };
  }

  /**
   * Admin-only middleware
   */
  requireAdmin() {
    return this.requireRole('ADMIN');
  }

  /**
   * Customer or Admin middleware
   */
  requireUser() {
    return this.requireRole(['ADMIN', 'CUSTOMER']);
  }

  // =============================================================================
  // PRIVATE AUTHENTICATION METHODS
  // =============================================================================

  /**
   * Try JWT authentication
   */
  private async tryJWTAuth(request: FastifyRequest): Promise<{
    success: boolean;
    user?: UserProfile;
    token?: JWTPayload;
    error?: string;
  }> {
    const authHeader = request.headers.authorization;
    const token = JWTService.extractTokenFromHeader(authHeader);

    if (!token) {
      return { success: false, error: 'No token provided' };
    }

    // Verify token
    const payload = this.getJWTService().verifyAccessToken(token);
    if (!payload) {
      return { success: false, error: 'Invalid or expired token' };
    }

    // Get user data
    const user = await this.getUserService().getUserProfile(payload.userId);
    if (!user) {
      return { success: false, error: 'User not found' };
    }

    // Check if user is active
    if (!user.is_active) {
      return { success: false, error: 'User account is inactive' };
    }

    return {
      success: true,
      user,
      token: payload
    };
  }

  /**
   * Try API key authentication (fallback to existing system)
   */
  private async tryApiKeyAuth(request: FastifyRequest): Promise<{
    success: boolean;
    error?: string;
  }> {
    // Check for API key in headers
    const apiKey = request.headers['x-api-key'] as string || 
                   JWTService.extractTokenFromHeader(request.headers.authorization);

    if (!apiKey || !apiKey.startsWith('ptapi_')) {
      return { success: false, error: 'No valid API key provided' };
    }

    // This would integrate with existing API key validation
    // For now, we'll assume it's handled by the existing authenticateApiKey middleware
    return { success: true };
  }
}

// =============================================================================
// MIDDLEWARE FACTORY FUNCTIONS
// =============================================================================

const jwtAuthMiddleware = new JWTAuthMiddleware();

/**
 * JWT authentication middleware (required)
 */
export const requireAuth = jwtAuthMiddleware.authenticate({ required: true });

/**
 * Optional JWT authentication middleware
 */
export const optionalAuth = jwtAuthMiddleware.authenticate({ required: false });

/**
 * JWT or API key authentication middleware
 */
export const requireAuthOrApiKey = jwtAuthMiddleware.authenticate({ 
  required: true, 
  allowApiKey: true 
});

/**
 * Admin role required
 */
export const requireAdmin = jwtAuthMiddleware.requireAdmin();

/**
 * Customer or Admin role required
 */
export const requireUser = jwtAuthMiddleware.requireUser();

/**
 * Specific role(s) required
 */
export const requireRole = (roles: UserRole | UserRole[]) => 
  jwtAuthMiddleware.requireRole(roles);

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Extract user from request (for use in route handlers)
 */
export function getAuthenticatedUser(request: FastifyRequest): UserProfile | null {
  return request.user || null;
}

/**
 * Extract JWT payload from request
 */
export function getJWTPayload(request: FastifyRequest): JWTPayload | null {
  return request.token || null;
}

/**
 * Check if request is authenticated via JWT
 */
export function isJWTAuthenticated(request: FastifyRequest): boolean {
  return request.authMethod === 'jwt' && !!request.user;
}

/**
 * Check if request is authenticated via API key
 */
export function isApiKeyAuthenticated(request: FastifyRequest): boolean {
  return request.authMethod === 'api_key';
}

/**
 * Get authentication method used
 */
export function getAuthMethod(request: FastifyRequest): 'jwt' | 'api_key' | null {
  return request.authMethod || null;
}

// =============================================================================
// FASTIFY PLUGIN REGISTRATION
// =============================================================================

/**
 * Fastify plugin for JWT authentication
 */
export async function jwtAuthPlugin(fastify: any) {
  // Register the middleware instance
  fastify.decorate('jwtAuth', jwtAuthMiddleware);
  
  // Register utility functions
  fastify.decorate('requireAuth', requireAuth);
  fastify.decorate('optionalAuth', optionalAuth);
  fastify.decorate('requireAuthOrApiKey', requireAuthOrApiKey);
  fastify.decorate('requireAdmin', requireAdmin);
  fastify.decorate('requireUser', requireUser);
  fastify.decorate('requireRole', requireRole);
  
  // Register utility functions
  fastify.decorate('getAuthenticatedUser', getAuthenticatedUser);
  fastify.decorate('getJWTPayload', getJWTPayload);
  fastify.decorate('isJWTAuthenticated', isJWTAuthenticated);
  fastify.decorate('isApiKeyAuthenticated', isApiKeyAuthenticated);
  fastify.decorate('getAuthMethod', getAuthMethod);
}

// Default export
export default jwtAuthMiddleware;
