import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { getDatabasePool } from '../database/connection';
import { hashApiKey, validateApiKeyFormat, generateRequestId } from '../utils/crypto';
import { ApiKeyAuthRequest, ERROR_CODES } from '../types/api';
import { logAuthenticationFailure, logAuthenticationSuccess } from './security-logger';

interface RateLimitWindow {
  requests: number;
  windowStart: Date;
  burstUsed: number;
}

// PostgreSQL-based rate limiting cache
const RATE_LIMIT_CACHE_TTL = 60; // 1 minute

async function checkRateLimit(
  apiKeyId: string,
  userId: string,
  limits: { perMinute: number; perDay: number; burst: number }
): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
  const pool = getDatabasePool();
  const now = new Date();
  const windowStart = new Date(now.getTime() - (now.getTime() % 60000)); // Start of current minute
  const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  // Check minute-based rate limit using cache
  const minuteCacheKey = `rate_limit:${apiKeyId}:minute:${windowStart.getTime()}`;
  const dayCacheKey = `rate_limit:${apiKeyId}:day:${dayStart.getTime()}`;

  try {
    // Get current minute window data
    const minuteResult = await pool.query(`
      SELECT cache_value FROM api_cache
      WHERE cache_key = $1 AND user_id = $2 AND expires_at > NOW()
    `, [minuteCacheKey, userId]);

    const minuteData: RateLimitWindow = minuteResult.rows.length > 0
      ? minuteResult.rows[0].cache_value
      : { requests: 0, windowStart, burstUsed: 0 };

    // Get daily data
    const dayResult = await pool.query(`
      SELECT cache_value FROM api_cache
      WHERE cache_key = $1 AND user_id = $2 AND expires_at > NOW()
    `, [dayCacheKey, userId]);

    const dayData = dayResult.rows.length > 0
      ? dayResult.rows[0].cache_value
      : { requests: 0 };

    // Check if burst limit allows this request
    const canUseBurst = minuteData.burstUsed < limits.burst;
    const withinMinuteLimit = minuteData.requests < limits.perMinute;
    const withinDayLimit = dayData.requests < limits.perDay;

    if (!withinDayLimit) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: new Date(dayStart.getTime() + 24 * 60 * 60 * 1000)
      };
    }

    if (!withinMinuteLimit && !canUseBurst) {
      return {
        allowed: false,
        remaining: limits.perMinute - minuteData.requests,
        resetTime: new Date(windowStart.getTime() + 60000)
      };
    }

    // Update counters
    const newMinuteData = {
      ...minuteData,
      requests: minuteData.requests + 1,
      burstUsed: !withinMinuteLimit ? minuteData.burstUsed + 1 : minuteData.burstUsed
    };

    const newDayData = {
      requests: dayData.requests + 1
    };

    // Update cache with new values
    await pool.query(`
      INSERT INTO api_cache (cache_key, user_id, cache_value, expires_at)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (cache_key, user_id)
      DO UPDATE SET cache_value = EXCLUDED.cache_value, expires_at = EXCLUDED.expires_at
    `, [
      minuteCacheKey,
      userId,
      JSON.stringify(newMinuteData),
      new Date(windowStart.getTime() + 60000 + RATE_LIMIT_CACHE_TTL * 1000)
    ]);

    await pool.query(`
      INSERT INTO api_cache (cache_key, user_id, cache_value, expires_at)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (cache_key, user_id)
      DO UPDATE SET cache_value = EXCLUDED.cache_value, expires_at = EXCLUDED.expires_at
    `, [
      dayCacheKey,
      userId,
      JSON.stringify(newDayData),
      new Date(dayStart.getTime() + 24 * 60 * 60 * 1000 + RATE_LIMIT_CACHE_TTL * 1000)
    ]);

    return {
      allowed: true,
      remaining: Math.max(0, limits.perMinute - newMinuteData.requests),
      resetTime: new Date(windowStart.getTime() + 60000)
    };

  } catch (error) {
    console.error('Rate limiting error:', error);
    // Fail open - allow request if rate limiting fails
    return {
      allowed: true,
      remaining: limits.perMinute,
      resetTime: new Date(windowStart.getTime() + 60000)
    };
  }
}

// Authentication function that can be used as a preHandler
export async function authenticateApiKey(request: FastifyRequest, reply: FastifyReply) {
  const authRequest = request as ApiKeyAuthRequest;

  // Ensure request ID is set (in case this runs before requestLogger)
  if (!(request as any).requestId) {
    (request as any).requestId = generateRequestId();
  }

  // Support two header formats:
  // 1.  Custom header:  X-API-Key: ptapi_...
  // 2.  Standard auth header:  Authorization: Bearer ptapi_...
  let apiKey: string | undefined = request.headers['x-api-key'] as string | undefined;

  if (!apiKey && typeof request.headers['authorization'] === 'string') {
    const authHeader = request.headers['authorization'];
    const match = authHeader.match(/^Bearer\s+(\S+)$/i);
    if (match) {
      apiKey = match[1];
    }
  }

  if (!apiKey) {
    logAuthenticationFailure(request, 'missing_key');
    return reply.status(401).send({
      error: {
        code: ERROR_CODES.MISSING_API_KEY,
        message: 'API key is required',
        requestId: (request as any).requestId || 'unknown',
        timestamp: new Date().toISOString()
      }
    });
  }

  // Validate API key format
  if (!validateApiKeyFormat(apiKey)) {
    logAuthenticationFailure(request, 'invalid_key');
    return reply.status(401).send({
      error: {
        code: ERROR_CODES.INVALID_API_KEY,
        message: 'Invalid API key format',
        requestId: (request as any).requestId || 'unknown',
        timestamp: new Date().toISOString()
      }
    });
  }

  try {
    // Hash the provided key for database lookup
    const keyHash = hashApiKey(apiKey);

    const pool = getDatabasePool();
    const result = await pool.query(`
      SELECT id, user_id, rate_limit_per_minute, rate_limit_per_day, rate_limit_burst, is_active
      FROM api_keys
      WHERE key_hash = $1 AND is_active = true
    `, [keyHash]);

    if (result.rows.length === 0) {
      logAuthenticationFailure(request, 'invalid_key');
      return reply.status(401).send({
        error: {
          code: ERROR_CODES.INVALID_API_KEY,
          message: 'Invalid or inactive API key',
          requestId: (request as any).requestId || 'unknown',
          timestamp: new Date().toISOString()
        }
      });
    }

    const keyData = result.rows[0];

    // Update last used timestamp
    await pool.query(`
      UPDATE api_keys
      SET last_used_at = NOW()
      WHERE id = $1
    `, [keyData.id]);

    // Attach API key info to request
    authRequest.apiKey = {
      id: keyData.id,
      userId: keyData.user_id,
      rateLimitPerMinute: keyData.rate_limit_per_minute,
      rateLimitPerDay: keyData.rate_limit_per_day,
      rateLimitBurst: keyData.rate_limit_burst
    };

    // Check rate limiting
    const rateLimitResult = await checkRateLimit(
      keyData.id,
      keyData.user_id,
      {
        perMinute: keyData.rate_limit_per_minute,
        perDay: keyData.rate_limit_per_day,
        burst: keyData.rate_limit_burst
      }
    );

    if (!rateLimitResult.allowed) {
      logAuthenticationFailure(request, 'rate_limited');
      return reply.status(429).send({
        error: {
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
          message: 'Rate limit exceeded',
          requestId: (request as any).requestId || 'unknown',
          timestamp: new Date().toISOString(),
          details: {
            remaining: rateLimitResult.remaining,
            resetTime: rateLimitResult.resetTime.toISOString()
          }
        }
      });
    }

    // Log successful authentication
    logAuthenticationSuccess(request, keyData.id);

    // Add rate limit headers
    reply.header('X-RateLimit-Limit', keyData.rate_limit_per_minute);
    reply.header('X-RateLimit-Remaining', rateLimitResult.remaining);
    reply.header('X-RateLimit-Reset', Math.ceil(rateLimitResult.resetTime.getTime() / 1000));

  } catch (error) {
    request.log.error('Authentication error:', error);
    return reply.status(500).send({
      error: {
        code: ERROR_CODES.INTERNAL_ERROR,
        message: 'Authentication service unavailable',
        requestId: (request as any).requestId || 'unknown',
        timestamp: new Date().toISOString()
      }
    });
  }
}

export const authMiddleware: FastifyPluginAsync = async (fastify) => {
  fastify.addHook('onRequest', authenticateApiKey);
};
