/**
 * Error message sanitization middleware to prevent information disclosure
 */

import { FastifyRequest } from 'fastify';
import { validateEnvironment } from '../config';

export interface SanitizedError {
  statusCode: number;
  error: string;
  message: string;
  requestId?: string;
  timestamp: string;
  details?: any;
}

// Generic error messages for production
const GENERIC_ERROR_MESSAGES: Record<number, string> = {
  400: 'Invalid request parameters',
  401: 'Authentication required',
  403: 'Access forbidden',
  404: 'Resource not found',
  429: 'Too many requests',
  500: 'Internal server error',
  502: 'Service temporarily unavailable',
  503: 'Service temporarily unavailable',
  504: 'Request timeout'
};

// Sensitive patterns that should be removed from error messages
const SENSITIVE_PATTERNS = [
  // Database connection strings
  /postgresql:\/\/[^@]+@[^/]+\/\w+/gi,
  /postgres:\/\/[^@]+@[^/]+\/\w+/gi,

  // API keys and tokens
  /ptapi_[a-f0-9]{64}/gi,
  /bearer\s+[a-zA-Z0-9_-]+/gi,
  /token[:\s=]+[a-zA-Z0-9_-]+/gi,

  // File paths
  /\/[a-zA-Z0-9_\-/.]+\.ts/gi,
  /\/[a-zA-Z0-9_\-/.]+\.js/gi,
  /\/Users\/[^/\s]+/gi,
  /\/home\/[^/\s]+/gi,
  /C:\\[^\\s]+/gi,

  // Stack traces
  /at\s+[a-zA-Z0-9_.]+\s+\([^)]+\)/gi,
  /at\s+[a-zA-Z0-9_.]+:[0-9]+:[0-9]+/gi,

  // Internal service URLs
  /https?:\/\/[a-zA-Z0-9\-.]+\.[a-zA-Z]{2,}\/[^\s]*/gi,
  
  // SQL queries
  /SELECT\s+[^;]+;?/gi,
  /INSERT\s+[^;]+;?/gi,
  /UPDATE\s+[^;]+;?/gi,
  /DELETE\s+[^;]+;?/gi,
  
  // Environment variables
  /[A-Z_]+=[^\s]+/gi
];

/**
 * Sanitize error message by removing sensitive information
 */
export function sanitizeErrorMessage(message: string, isDevelopment: boolean): string {
  if (isDevelopment) {
    // In development, show full error messages but still remove sensitive data
    let sanitized = message;
    SENSITIVE_PATTERNS.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });
    return sanitized;
  }
  
  // In production, use generic messages for most errors
  return message;
}

/**
 * Sanitize error details object
 */
export function sanitizeErrorDetails(details: any, isDevelopment: boolean): any {
  if (!details || typeof details !== 'object') {
    return details;
  }
  
  if (!isDevelopment) {
    // In production, remove most details except safe ones
    const safeDetails: any = {};
    
    // Only include safe fields
    if (details.validation) {
      safeDetails.validation = details.validation;
    }
    if (details.retryAfter) {
      safeDetails.retryAfter = details.retryAfter;
    }
    if (details.remaining) {
      safeDetails.remaining = details.remaining;
    }
    if (details.resetTime) {
      safeDetails.resetTime = details.resetTime;
    }
    
    return Object.keys(safeDetails).length > 0 ? safeDetails : undefined;
  }
  
  // In development, sanitize but keep structure
  const sanitized = JSON.parse(JSON.stringify(details));
  
  function sanitizeObject(obj: any): any {
    if (typeof obj === 'string') {
      return sanitizeErrorMessage(obj, true);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    
    if (obj && typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = sanitizeObject(value);
      }
      return result;
    }
    
    return obj;
  }
  
  return sanitizeObject(sanitized);
}

/**
 * Create a sanitized error response
 */
export function createSanitizedError(
  error: Error | any,
  statusCode: number = 500,
  request?: FastifyRequest
): SanitizedError {
  const env = validateEnvironment();
  const isDevelopment = env.NODE_ENV === 'development';
  
  // Determine error message
  let message: string;
  let details: any;
  
  if (error && typeof error === 'object') {
    if (error.message) {
      message = sanitizeErrorMessage(error.message, isDevelopment);
    } else if (error.error) {
      message = sanitizeErrorMessage(error.error, isDevelopment);
    } else {
      message = GENERIC_ERROR_MESSAGES[statusCode] || 'An error occurred';
    }
    
    // Handle validation errors specially
    if (error.validation) {
      details = sanitizeErrorDetails({ validation: error.validation }, isDevelopment);
    } else if (error.details) {
      details = sanitizeErrorDetails(error.details, isDevelopment);
    }
  } else if (typeof error === 'string') {
    message = sanitizeErrorMessage(error, isDevelopment);
  } else {
    message = GENERIC_ERROR_MESSAGES[statusCode] || 'An error occurred';
  }
  
  // In production, use generic messages for server errors
  if (!isDevelopment && statusCode >= 500) {
    message = GENERIC_ERROR_MESSAGES[statusCode] || 'Internal server error';
    details = undefined; // Remove all details for server errors in production
  }
  
  const sanitizedError: SanitizedError = {
    statusCode,
    error: getErrorName(statusCode),
    message,
    requestId: (request as any)?.requestId || 'unknown',
    timestamp: new Date().toISOString()
  };
  
  if (details) {
    sanitizedError.details = details;
  }
  
  return sanitizedError;
}

/**
 * Get standard error name for status code
 */
function getErrorName(statusCode: number): string {
  const errorNames: Record<number, string> = {
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    429: 'Too Many Requests',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
    504: 'Gateway Timeout'
  };
  
  return errorNames[statusCode] || 'Error';
}

/**
 * Log security event for error sanitization
 */
export function logErrorSanitization(originalError: any, sanitizedError: SanitizedError, request?: FastifyRequest) {
  const env = validateEnvironment();
  
  if (env.NODE_ENV === 'production' && sanitizedError.statusCode >= 500) {
    // Log security event for potential information disclosure
    const securityEvent = {
      timestamp: new Date().toISOString(),
      level: 'SECURITY',
      event: 'ERROR_SANITIZED',
      statusCode: sanitizedError.statusCode,
      originalErrorType: typeof originalError,
      hasStackTrace: originalError?.stack ? true : false,
      ip: request?.ip,
      userAgent: request?.headers?.['user-agent'],
      method: request?.method,
      url: request?.url,
      requestId: sanitizedError.requestId
    };
    
    console.log(JSON.stringify(securityEvent));
  }
}
