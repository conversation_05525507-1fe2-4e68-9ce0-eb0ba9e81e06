import { FastifyRequest, FastifyReply } from 'fastify';
import { getDatabasePool } from '../database/connection';
import { CONFIG } from '../config';
import { generateRequestId } from '../utils/crypto';

interface PerformanceMetrics {
  requestId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  dbQueryTime?: number;
  cacheHit?: boolean;
  userAgent?: string;
  apiKeyId?: string;
  userId?: string;
  timestamp: Date;
}

interface RequestContext {
  requestId: string;
  startTime: number;
  dbQueryStartTime?: number;
  dbQueryTime?: number;
  cacheHit?: boolean;
}

// Store for tracking request metrics
const requestMetrics = new Map<string, RequestContext>();

// Performance thresholds - now configurable via environment variables
const PERFORMANCE_THRESHOLDS = {
  SLOW_REQUEST: CONFIG.performance.slowRequestThreshold,
  VERY_SLOW_REQUEST: CONFIG.performance.verySlowRequestThreshold,
  SLOW_DB_QUERY: CONFIG.performance.slowDbQueryThreshold,
  VERY_SLOW_DB_QUERY: CONFIG.performance.verySlowDbQueryThreshold
};

export async function performanceMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  // Use existing request ID from requestLogger middleware
  const requestId = (request as any).requestId || generateRequestId();
  const startTime = (request as any).startTime || Date.now();

  // Don't overwrite existing request context
  if (!(request as any).requestId) {
    (request as any).requestId = requestId;
  }
  if (!(request as any).startTime) {
    (request as any).startTime = startTime;
  }

  // Store metrics context
  requestMetrics.set(requestId, {
    requestId,
    startTime,
    cacheHit: false
  });

  // Add response time header
  reply.header('X-Request-ID', requestId);

  // Store context for later use in response
  (request as any).performanceContext = requestMetrics.get(requestId);
}

// Response hook to be used in routes
export async function performanceResponseHook(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  const requestId = (request as any).requestId;
  const context = requestMetrics.get(requestId);

  if (!context) return;

  const responseTime = Date.now() - context.startTime;
  const statusCode = reply.statusCode;

  // Add performance headers
  reply.header('X-Response-Time', `${responseTime}ms`);
  reply.header('X-Cache-Hit', context.cacheHit ? 'true' : 'false');

  // Log performance metrics
  const metrics: PerformanceMetrics = {
    requestId,
    method: request.method,
    url: request.url,
    statusCode,
    responseTime,
    dbQueryTime: context.dbQueryTime || 0,
    cacheHit: context.cacheHit || false,
    userAgent: request.headers['user-agent'] || '',
    apiKeyId: (request as any).apiKey?.id,
    userId: (request as any).apiKey?.user_id,
    timestamp: new Date()
  };

  // Log slow requests
  if (responseTime > PERFORMANCE_THRESHOLDS.SLOW_REQUEST) {
    const level = responseTime > PERFORMANCE_THRESHOLDS.VERY_SLOW_REQUEST ? 'warn' : 'info';
    request.log[level]({
      ...metrics,
      message: 'Slow request detected'
    });
  }

  // Store metrics in database for analytics (async, don't block response)
  setImmediate(() => storeMetrics(metrics));

  // Clean up
  requestMetrics.delete(requestId);
}

// Database query performance tracking
export function trackDbQuery<T>(
  requestId: string,
  queryPromise: Promise<T>
): Promise<T> {
  const context = requestMetrics.get(requestId);
  if (!context) return queryPromise;

  const queryStartTime = Date.now();
  context.dbQueryStartTime = queryStartTime;

  return queryPromise.then(
    (result) => {
      const queryTime = Date.now() - queryStartTime;
      context.dbQueryTime = (context.dbQueryTime || 0) + queryTime;

      // Log slow queries
      if (queryTime > PERFORMANCE_THRESHOLDS.SLOW_DB_QUERY) {
        const level = queryTime > PERFORMANCE_THRESHOLDS.VERY_SLOW_DB_QUERY ? 'warn' : 'info';
        console[level](`Slow database query: ${queryTime}ms (Request: ${requestId})`);
      }

      return result;
    },
    (error) => {
      const queryTime = Date.now() - queryStartTime;
      context.dbQueryTime = (context.dbQueryTime || 0) + queryTime;
      console.error(`Database query error after ${queryTime}ms (Request: ${requestId}):`, error);
      throw error;
    }
  );
}

// Cache hit tracking
export function markCacheHit(requestId: string): void {
  const context = requestMetrics.get(requestId);
  if (context) {
    context.cacheHit = true;
  }
}

// Store metrics in database for analytics
async function storeMetrics(metrics: PerformanceMetrics): Promise<void> {
  try {
    const pool = getDatabasePool();
    
    await pool.query(`
      INSERT INTO usage_analytics (
        user_id, api_key_id, endpoint, method, response_time_ms,
        response_status, cache_hit, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      metrics.userId || null,
      metrics.apiKeyId || null,
      metrics.url,
      metrics.method,
      metrics.responseTime,
      metrics.statusCode,
      metrics.cacheHit || false,
      metrics.timestamp
    ]);
  } catch (error) {
    console.error('Failed to store performance metrics:', error);
  }
}



// Get performance statistics
export async function getPerformanceStats(
  timeRange: 'hour' | 'day' | 'week' = 'day'
): Promise<{
  averageResponseTime: number;
  slowRequestCount: number;
  cacheHitRate: number;
  requestCount: number;
  errorRate: number;
}> {
  const pool = getDatabasePool();
  
  let timeCondition = '';
  switch (timeRange) {
    case 'hour':
      timeCondition = "created_at > NOW() - INTERVAL '1 hour'";
      break;
    case 'day':
      timeCondition = "created_at > NOW() - INTERVAL '1 day'";
      break;
    case 'week':
      timeCondition = "created_at > NOW() - INTERVAL '1 week'";
      break;
  }

  try {
    const result = await pool.query(`
      SELECT 
        AVG(response_time_ms) as avg_response_time,
        COUNT(*) as total_requests,
        COUNT(*) FILTER (WHERE response_time_ms > 1000) as slow_requests,
        COUNT(*) FILTER (WHERE cache_hit = true) as cache_hits,
        COUNT(*) FILTER (WHERE response_status >= 400) as error_requests
      FROM usage_analytics
      WHERE ${timeCondition}
    `);

    const row = result.rows[0];
    const totalRequests = parseInt(row.total_requests) || 0;

    return {
      averageResponseTime: parseFloat(row.avg_response_time) || 0,
      slowRequestCount: parseInt(row.slow_requests) || 0,
      cacheHitRate: totalRequests > 0 ? parseInt(row.cache_hits) / totalRequests : 0,
      requestCount: totalRequests,
      errorRate: totalRequests > 0 ? parseInt(row.error_requests) / totalRequests : 0
    };
  } catch (error) {
    console.error('Failed to get performance stats:', error);
    return {
      averageResponseTime: 0,
      slowRequestCount: 0,
      cacheHitRate: 0,
      requestCount: 0,
      errorRate: 0
    };
  }
}

// Response compression middleware
export async function compressionMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  const acceptEncoding = request.headers['accept-encoding'] || '';
  
  if (acceptEncoding.includes('gzip')) {
    reply.header('Content-Encoding', 'gzip');
  } else if (acceptEncoding.includes('deflate')) {
    reply.header('Content-Encoding', 'deflate');
  }
  
  // Add Vary header for proper caching
  reply.header('Vary', 'Accept-Encoding');
}

// ETag middleware for caching
export function generateETag(data: any): string {
  const crypto = require('crypto');
  const hash = crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
  return `"${hash}"`;
}

export async function etagMiddleware(
  request: FastifyRequest,
  _reply: FastifyReply
): Promise<void> {
  const ifNoneMatch = request.headers['if-none-match'];
  
  if (ifNoneMatch) {
    // Store for comparison in response hook
    (request as any).ifNoneMatch = ifNoneMatch;
  }
}

// Helper to check if response should be cached
export function shouldCache(request: FastifyRequest, reply: FastifyReply): boolean {
  // Don't cache errors
  if (reply.statusCode >= 400) {
    return false;
  }
  
  // Don't cache POST, PUT, DELETE requests
  if (!['GET', 'HEAD'].includes(request.method)) {
    return false;
  }
  
  // Don't cache if no-cache header is present
  const cacheControl = request.headers['cache-control'];
  if (cacheControl && cacheControl.includes('no-cache')) {
    return false;
  }
  
  return true;
}
