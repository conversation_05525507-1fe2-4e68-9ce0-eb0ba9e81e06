/**
 * Security headers middleware for defense-in-depth protection
 */

import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { validateEnvironment } from '../config';

export interface SecurityHeadersOptions {
  contentTypeOptions?: boolean;
  frameOptions?: boolean;
  xssProtection?: boolean;
  referrerPolicy?: boolean;
  strictTransportSecurity?: boolean;
  contentSecurityPolicy?: boolean;
  permissionsPolicy?: boolean;
}

const DEFAULT_OPTIONS: SecurityHeadersOptions = {
  contentTypeOptions: true,
  frameOptions: true,
  xssProtection: true,
  referrerPolicy: true,
  strictTransportSecurity: true,
  contentSecurityPolicy: true,
  permissionsPolicy: true
};

/**
 * Apply security headers to response
 */
export async function applySecurityHeaders(
  request: FastifyRequest,
  reply: FastifyReply,
  options: SecurityHeadersOptions = DEFAULT_OPTIONS
) {
  const env = validateEnvironment();
  const isProduction = env.NODE_ENV === 'production';
  const isHttps = request.protocol === 'https' || request.headers['x-forwarded-proto'] === 'https';

  // X-Content-Type-Options: Prevent MIME type sniffing
  if (options.contentTypeOptions) {
    reply.header('X-Content-Type-Options', 'nosniff');
  }

  // X-Frame-Options: Prevent clickjacking
  if (options.frameOptions) {
    reply.header('X-Frame-Options', 'DENY');
  }

  // X-XSS-Protection: Enable XSS filtering (legacy browsers)
  if (options.xssProtection) {
    reply.header('X-XSS-Protection', '1; mode=block');
  }

  // Referrer-Policy: Control referrer information
  if (options.referrerPolicy) {
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
  }

  // Strict-Transport-Security: Force HTTPS (only for HTTPS connections)
  if (options.strictTransportSecurity && isHttps && isProduction) {
    reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }

  // Content-Security-Policy: Prevent XSS and data injection
  if (options.contentSecurityPolicy) {
    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self'",
      "style-src 'self' 'unsafe-inline'", // Allow inline styles for API responses
      "img-src 'self' data:",
      "font-src 'self'",
      "connect-src 'self'",
      "media-src 'none'",
      "object-src 'none'",
      "child-src 'none'",
      "frame-src 'none'",
      "worker-src 'none'",
      "frame-ancestors 'none'",
      "form-action 'self'",
      "base-uri 'self'",
      "manifest-src 'none'"
    ];

    // In development, allow more permissive CSP for debugging
    if (!isProduction) {
      cspDirectives.push("script-src 'self' 'unsafe-eval' 'unsafe-inline'");
      cspDirectives.push("style-src 'self' 'unsafe-inline'");
    }

    reply.header('Content-Security-Policy', cspDirectives.join('; '));
  }

  // Permissions-Policy: Control browser features
  if (options.permissionsPolicy) {
    const permissionsPolicies = [
      'accelerometer=()',
      'ambient-light-sensor=()',
      'autoplay=()',
      'battery=()',
      'camera=()',
      'cross-origin-isolated=()',
      'display-capture=()',
      'document-domain=()',
      'encrypted-media=()',
      'execution-while-not-rendered=()',
      'execution-while-out-of-viewport=()',
      'fullscreen=()',
      'geolocation=()',
      'gyroscope=()',
      'keyboard-map=()',
      'magnetometer=()',
      'microphone=()',
      'midi=()',
      'navigation-override=()',
      'payment=()',
      'picture-in-picture=()',
      'publickey-credentials-get=()',
      'screen-wake-lock=()',
      'sync-xhr=()',
      'usb=()',
      'web-share=()',
      'xr-spatial-tracking=()'
    ];

    reply.header('Permissions-Policy', permissionsPolicies.join(', '));
  }

  // Additional security headers for API
  reply.header('X-Powered-By', 'Postal Terminal API'); // Custom powered-by header
  reply.header('X-API-Version', env.APP_VERSION);
  
  // Cache control for API responses
  if (request.url.includes('/api/')) {
    reply.header('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    reply.header('Pragma', 'no-cache');
    reply.header('Expires', '0');
    reply.header('Surrogate-Control', 'no-store');
  }
}

/**
 * Security headers middleware plugin
 */
export const securityHeadersMiddleware: FastifyPluginAsync<SecurityHeadersOptions> = async (
  fastify,
  options = DEFAULT_OPTIONS
) => {
  // Apply security headers to all responses
  fastify.addHook('onSend', async (request, reply) => {
    await applySecurityHeaders(request, reply, options);
  });
};

/**
 * Enhanced security headers for sensitive endpoints
 */
export const strictSecurityHeaders: SecurityHeadersOptions = {
  ...DEFAULT_OPTIONS,
  contentSecurityPolicy: true,
  strictTransportSecurity: true
};

/**
 * Relaxed security headers for public endpoints
 */
export const relaxedSecurityHeaders: SecurityHeadersOptions = {
  contentTypeOptions: true,
  frameOptions: true,
  xssProtection: true,
  referrerPolicy: true,
  strictTransportSecurity: false,
  contentSecurityPolicy: false,
  permissionsPolicy: false
};

/**
 * Log security headers application
 */
export function logSecurityHeaders(request: FastifyRequest, reply: FastifyReply) {
  const env = validateEnvironment();
  
  if (env.NODE_ENV === 'development') {
    const appliedHeaders = [
      'X-Content-Type-Options',
      'X-Frame-Options',
      'X-XSS-Protection',
      'Referrer-Policy',
      'Strict-Transport-Security',
      'Content-Security-Policy',
      'Permissions-Policy'
    ];

    const securityEvent = {
      timestamp: new Date().toISOString(),
      level: 'SECURITY',
      event: 'SECURITY_HEADERS_APPLIED',
      url: request.url,
      method: request.method,
      headers: appliedHeaders.filter(header => reply.getHeader(header)),
      requestId: (request as any).requestId
    };

    console.log(JSON.stringify(securityEvent));
  }
}

/**
 * Validate security headers configuration
 */
export function validateSecurityHeadersConfig(options: SecurityHeadersOptions): boolean {
  // Ensure at least basic security headers are enabled
  const requiredHeaders = ['contentTypeOptions', 'frameOptions', 'xssProtection'];
  
  for (const header of requiredHeaders) {
    if (!options[header as keyof SecurityHeadersOptions]) {
      console.warn(`Security warning: ${header} is disabled. This may reduce security.`);
      return false;
    }
  }
  
  return true;
}

// Export default configuration
export default securityHeadersMiddleware;
