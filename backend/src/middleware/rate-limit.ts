/**
 * Rate limiting middleware for API endpoints
 */

import { FastifyInstance, FastifyRequest } from 'fastify';
import { CONFIG, RATE_LIMITING_CONFIG } from '../config';

export interface RateLimitOptions {
  max: number;
  timeWindow: string | number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator: (request: FastifyRequest) => string;
  errorResponseBuilder: (request: FastifyRequest, context: any) => any;
}

// Default rate limit configuration
export const defaultRateLimitConfig: RateLimitOptions = {
  max: CONFIG.rateLimiting.defaultRateLimit, // requests per window
  timeWindow: `${CONFIG.rateLimiting.windowSize}s`, // time window in seconds
  skipSuccessfulRequests: false,
  skipFailedRequests: false,
  keyGenerator: (request: FastifyRequest) => {
    // Use API key if available, otherwise IP address
    const apiKey = request.headers.authorization?.replace('Bearer ', '');
    return apiKey || request.ip;
  },
  errorResponseBuilder: (_request: FastifyRequest, context: any) => ({
    statusCode: 429,
    error: 'Too Many Requests',
    message: `Rate limit exceeded. Maximum ${context.max} requests per ${context.timeWindow}. Try again in ${Math.round(context.ttl / 1000)} seconds.`,
    retryAfter: Math.round(context.ttl / 1000)
  })
};

// Burst rate limit for high-traffic scenarios
export const burstRateLimitConfig: RateLimitOptions = {
  max: CONFIG.rateLimiting.burstLimit,
  timeWindow: `${CONFIG.rateLimiting.windowSize}s`,
  skipSuccessfulRequests: false,
  skipFailedRequests: false,
  keyGenerator: defaultRateLimitConfig.keyGenerator,
  errorResponseBuilder: (_request: FastifyRequest, context: any) => ({
    statusCode: 429,
    error: 'Too Many Requests',
    message: `Burst rate limit exceeded. Maximum ${context.max} requests per ${context.timeWindow}. Try again in ${Math.round(context.ttl / 1000)} seconds.`,
    retryAfter: Math.round(context.ttl / 1000)
  })
};

// Strict rate limit for sensitive endpoints
export const strictRateLimitConfig: RateLimitOptions = {
  max: Math.floor(CONFIG.rateLimiting.defaultRateLimit * RATE_LIMITING_CONFIG.strictMultiplier), // Configurable percentage of default
  timeWindow: `${CONFIG.rateLimiting.windowSize}s`,
  skipSuccessfulRequests: false,
  skipFailedRequests: false,
  keyGenerator: defaultRateLimitConfig.keyGenerator,
  errorResponseBuilder: (_request: FastifyRequest, context: any) => ({
    statusCode: 429,
    error: 'Too Many Requests',
    message: `Strict rate limit exceeded for sensitive endpoint. Maximum ${context.max} requests per ${context.timeWindow}. Try again in ${Math.round(context.ttl / 1000)} seconds.`,
    retryAfter: Math.round(context.ttl / 1000)
  })
};

// Security event logging for rate limit violations
export const logRateLimitViolation = (request: FastifyRequest, context: any) => {
  const securityEvent = {
    timestamp: new Date().toISOString(),
    level: 'SECURITY',
    event: 'RATE_LIMIT_EXCEEDED',
    ip: request.ip,
    userAgent: request.headers['user-agent'],
    method: request.method,
    url: request.url,
    apiKey: request.headers.authorization ? 'present' : 'missing',
    rateLimitMax: context.max,
    rateLimitWindow: context.timeWindow,
    retryAfter: Math.round(context.ttl / 1000)
  };
  
  console.log(JSON.stringify(securityEvent));
};

// Enhanced error response builder with logging
export const enhancedErrorResponseBuilder = (request: FastifyRequest, context: any) => {
  // Log security event
  logRateLimitViolation(request, context);
  
  // Return error response
  return defaultRateLimitConfig.errorResponseBuilder!(request, context);
};

// Rate limit configurations for different endpoint types
export const rateLimitConfigs = {
  default: {
    ...defaultRateLimitConfig,
    errorResponseBuilder: enhancedErrorResponseBuilder
  },
  burst: {
    ...burstRateLimitConfig,
    errorResponseBuilder: enhancedErrorResponseBuilder
  },
  strict: {
    ...strictRateLimitConfig,
    errorResponseBuilder: enhancedErrorResponseBuilder
  },
  // Health endpoint - more permissive
  health: {
    max: Math.floor(CONFIG.rateLimiting.defaultRateLimit * RATE_LIMITING_CONFIG.healthMultiplier),
    timeWindow: `${CONFIG.rateLimiting.windowSize}s`,
    keyGenerator: (request: FastifyRequest) => request.ip,
    errorResponseBuilder: enhancedErrorResponseBuilder
  },
  // Tracking endpoint - moderate limits
  tracking: {
    max: Math.floor(CONFIG.rateLimiting.defaultRateLimit / 2),
    timeWindow: `${CONFIG.rateLimiting.windowSize}s`,
    keyGenerator: defaultRateLimitConfig.keyGenerator,
    errorResponseBuilder: enhancedErrorResponseBuilder
  },
  // Metrics endpoint - strict limits
  metrics: strictRateLimitConfig
};

/**
 * Register rate limiting plugin with Fastify
 */
export async function registerRateLimit(fastify: FastifyInstance) {
  await fastify.register(import('@fastify/rate-limit'), {
    global: true, // Apply global rate limiting
    max: CONFIG.rateLimiting.defaultRateLimit,
    timeWindow: `${CONFIG.rateLimiting.windowSize}s`,
    keyGenerator: (request: FastifyRequest) => {
      // Use API key if available, otherwise IP address
      const apiKey = request.headers.authorization?.replace('Bearer ', '');
      return apiKey || request.ip;
    },
    errorResponseBuilder: (request: FastifyRequest, context: any) => {
      // Log security event
      logRateLimitViolation(request, context);

      return {
        statusCode: 429,
        error: 'Too Many Requests',
        message: `Rate limit exceeded. Maximum ${context.max} requests per ${context.timeWindow}. Try again in ${Math.round(context.ttl / 1000)} seconds.`,
        retryAfter: Math.round(context.ttl / 1000)
      };
    },
    addHeaders: {
      'x-ratelimit-limit': true,
      'x-ratelimit-remaining': true,
      'x-ratelimit-reset': true
    }
  });
}
