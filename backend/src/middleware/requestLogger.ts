import { FastifyPluginAsync } from 'fastify';
import { generateRequestId } from '../utils/crypto';

export const requestLogger: FastifyPluginAsync = async (fastify) => {
  fastify.addHook('onRequest', async (request) => {
    // Add request ID and start time
    (request as any).requestId = generateRequestId();
    (request as any).startTime = Date.now();
    
    // Log request
    request.log.info({
      requestId: (request as any).requestId,
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip
    }, 'Incoming request');
  });

  fastify.addHook('onSend', async (request, reply, payload) => {
    const responseTime = Date.now() - (request as any).startTime;
    
    // Add response time header
    reply.header('X-Response-Time', `${responseTime}ms`);
    reply.header('X-Request-ID', (request as any).requestId);
    
    // Log response
    request.log.info({
      requestId: (request as any).requestId,
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      responseTime: `${responseTime}ms`
    }, 'Request completed');
    
    return payload;
  });
};
