/**
 * Usage Tracking Middleware
 * 
 * Real-time tracking of API requests, response times, and usage patterns.
 * Captures detailed analytics for every API request and stores in database.
 */

import { FastifyRequest, FastifyReply, FastifyInstance } from 'fastify';
import { Pool } from 'pg';
import { getDatabasePool } from '../database/connection';

// Types for usage tracking
interface UsageTrackingData {
  apiKeyId?: string;
  userId?: string;
  requestId: string;
  endpoint: string;
  method: string;
  statusCode: number;
  responseTimeMs: number;
  requestSizeBytes: number;
  responseSizeBytes: number;
  userAgent?: string | undefined;
  ipAddress?: string | undefined;
  countryCode?: string | undefined;
  city?: string | undefined;
}

interface RequestMetrics {
  startTime: number;
  requestSize: number;
}

// Extend FastifyRequest to include metrics
declare module 'fastify' {
  interface FastifyRequest {
    metrics?: RequestMetrics;
  }
}

export class UsageTrackingMiddleware {
  private pool: Pool | null = null;
  private batchQueue: UsageTrackingData[] = [];
  private batchSize = 50;
  private batchTimeout = 5000; // 5 seconds
  private batchTimer?: ReturnType<typeof setTimeout>;

  private getPool(): Pool {
    if (!this.pool) {
      this.pool = getDatabasePool();
    }
    return this.pool;
  }

  /**
   * Initialize usage tracking middleware
   */
  async register(fastify: FastifyInstance): Promise<void> {
    // Pre-handler to capture request start time and size
    fastify.addHook('preHandler', async (request: FastifyRequest) => {
      request.metrics = {
        startTime: Date.now(),
        requestSize: this.calculateRequestSize(request)
      };
    });

    // Post-response hook to capture response metrics and track usage
    fastify.addHook('onResponse', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        await this.trackRequest(request, reply);
      } catch (error) {
        // Log error but don't fail the request
        request.log.error('Usage tracking error:', error);
      }
    });

    // Start batch processing
    this.startBatchProcessing();

    // Graceful shutdown
    fastify.addHook('onClose', async () => {
      await this.flushBatch();
      if (this.batchTimer) {
        clearTimeout(this.batchTimer);
      }
    });
  }

  /**
   * Track individual API request
   */
  private async trackRequest(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const metrics = request.metrics;
    if (!metrics) return;

    const responseTime = Date.now() - metrics.startTime;
    const responseSize = this.calculateResponseSize(reply);

    // Extract API key and user information from request
    const apiKeyId = (request as any).apiKey?.id;
    const userId = (request as any).apiKey?.userId || (request as any).user?.id;

    // Skip tracking if no authentication info (health checks, etc.)
    if (!apiKeyId && !userId) return;

    // Extract location information from IP (simplified)
    const ipAddress = this.extractClientIP(request);
    const { countryCode, city } = await this.getLocationFromIP(ipAddress);

    const trackingData: UsageTrackingData = {
      apiKeyId,
      userId,
      requestId: request.id,
      endpoint: this.normalizeEndpoint(request.url),
      method: request.method,
      statusCode: reply.statusCode,
      responseTimeMs: responseTime,
      requestSizeBytes: metrics.requestSize,
      responseSizeBytes: responseSize,
      userAgent: request.headers['user-agent'],
      ipAddress,
      countryCode,
      city
    };

    // Add to batch queue
    this.addToBatch(trackingData);
  }

  /**
   * Calculate request size in bytes
   */
  private calculateRequestSize(request: FastifyRequest): number {
    let size = 0;
    
    // Headers size (approximate)
    Object.entries(request.headers).forEach(([key, value]) => {
      size += key.length + (Array.isArray(value) ? value.join(',').length : String(value || '').length);
    });
    
    // Body size
    if (request.body) {
      size += JSON.stringify(request.body).length;
    }
    
    // URL and method
    size += request.url.length + request.method.length;
    
    return size;
  }

  /**
   * Calculate response size in bytes
   */
  private calculateResponseSize(reply: FastifyReply): number {
    let size = 0;
    
    // Headers size (approximate)
    const headers = reply.getHeaders();
    Object.entries(headers).forEach(([key, value]) => {
      size += key.length + String(value || '').length;
    });
    
    // Content-Length header if available
    const contentLength = headers['content-length'];
    if (contentLength) {
      size += parseInt(String(contentLength), 10) || 0;
    } else {
      // Estimate based on status code
      size += reply.statusCode >= 400 ? 100 : 500; // Error responses are typically smaller
    }
    
    return size;
  }

  /**
   * Extract client IP address
   */
  private extractClientIP(request: FastifyRequest): string {
    // Check various headers for real IP
    const forwarded = request.headers['x-forwarded-for'];
    if (forwarded) {
      const ips = Array.isArray(forwarded) ? forwarded[0] : forwarded;
      if (ips && typeof ips === 'string') {
        const ipList = ips.split(',');
        const firstIp = ipList[0];
        if (firstIp) {
          return firstIp.trim();
        }
      }
    }

    return (request.headers['x-real-ip'] as string) ||
           (request.headers['x-client-ip'] as string) ||
           request.ip ||
           'unknown';
  }

  /**
   * Get location information from IP address
   * (Simplified implementation - in production, use a proper GeoIP service)
   */
  private async getLocationFromIP(_ipAddress: string): Promise<{ countryCode?: string; city?: string }> {
    // For now, return empty - in production, integrate with GeoIP service
    // like MaxMind, IPinfo, or similar
    return {};
  }

  /**
   * Normalize endpoint for analytics (remove IDs, query params)
   */
  private normalizeEndpoint(url: string): string {
    // Remove query parameters
    const baseUrl = url.split('?')[0];

    if (!baseUrl) return url;

    // Replace UUIDs with placeholder
    const uuidRegex = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi;
    const normalized = baseUrl.replace(uuidRegex, ':id');

    // Replace other ID patterns
    return normalized.replace(/\/\d+/g, '/:id');
  }

  /**
   * Add tracking data to batch queue
   */
  private addToBatch(data: UsageTrackingData): void {
    this.batchQueue.push(data);
    
    if (this.batchQueue.length >= this.batchSize) {
      this.flushBatch();
    }
  }

  /**
   * Start batch processing timer
   */
  private startBatchProcessing(): void {
    this.batchTimer = setInterval(() => {
      if (this.batchQueue.length > 0) {
        this.flushBatch();
      }
    }, this.batchTimeout);
  }

  /**
   * Flush batch queue to database
   */
  private async flushBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return;

    const batch = [...this.batchQueue];
    this.batchQueue = [];

    try {
      await this.insertBatchToDatabase(batch);
    } catch (error) {
      console.error('Failed to flush usage tracking batch:', error);
      // In production, you might want to implement retry logic or dead letter queue
    }
  }

  /**
   * Insert batch of tracking data to database
   */
  private async insertBatchToDatabase(batch: UsageTrackingData[]): Promise<void> {
    const pool = this.getPool();
    
    // Build bulk insert query
    const values: any[] = [];
    const placeholders: string[] = [];
    
    batch.forEach((data, index) => {
      const baseIndex = index * 12;
      placeholders.push(`($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6}, $${baseIndex + 7}, $${baseIndex + 8}, $${baseIndex + 9}, $${baseIndex + 10}, $${baseIndex + 11}, $${baseIndex + 12})`);
      
      values.push(
        data.apiKeyId || null,
        data.userId || null,
        data.requestId,
        data.endpoint,
        data.method,
        data.statusCode,
        data.responseTimeMs,
        data.requestSizeBytes,
        data.responseSizeBytes,
        data.userAgent || null,
        data.ipAddress || null,
        data.countryCode || null
      );
    });

    const query = `
      INSERT INTO api_request_analytics (
        api_key_id, user_id, request_id, endpoint, method, status_code,
        response_time_ms, request_size_bytes, response_size_bytes,
        user_agent, ip_address, country_code
      ) VALUES ${placeholders.join(', ')}
    `;

    await pool.query(query, values);
  }

  /**
   * Get real-time usage statistics
   */
  async getRealTimeStats(userId?: string, apiKeyId?: string): Promise<{
    requestsLastMinute: number;
    requestsLastHour: number;
    avgResponseTime: number;
    errorRate: number;
  }> {
    const pool = this.getPool();
    
    let whereClause = '';
    const params: any[] = [];
    
    if (userId) {
      whereClause += ' AND user_id = $1';
      params.push(userId);
    }
    
    if (apiKeyId) {
      whereClause += ` AND api_key_id = $${params.length + 1}`;
      params.push(apiKeyId);
    }

    const query = `
      SELECT 
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '1 minute' THEN 1 END) as requests_last_minute,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '1 hour' THEN 1 END) as requests_last_hour,
        AVG(response_time_ms) as avg_response_time,
        (COUNT(CASE WHEN status_code >= 400 THEN 1 END)::float / NULLIF(COUNT(*), 0)) * 100 as error_rate
      FROM api_request_analytics
      WHERE created_at >= NOW() - INTERVAL '1 hour' ${whereClause}
    `;

    const result = await pool.query(query, params);
    const row = result.rows[0];

    return {
      requestsLastMinute: parseInt(row.requests_last_minute) || 0,
      requestsLastHour: parseInt(row.requests_last_hour) || 0,
      avgResponseTime: parseFloat(row.avg_response_time) || 0,
      errorRate: parseFloat(row.error_rate) || 0
    };
  }
}
