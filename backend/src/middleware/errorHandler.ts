import { FastifyPluginAsync, FastifyError, FastifyRequest } from 'fastify';
import { ZodError } from 'zod';
import { ERROR_CODES, ErrorResponse } from '../types/api';
import { createSanitizedError, logErrorSanitization } from './error-sanitizer';

// Type for request with requestId
interface RequestWithId extends FastifyRequest {
  requestId?: string;
}

// Type for validation error details
interface ValidationErrorDetail {
  field: string;
  message: string;
  code: string;
}

export const errorHandler: FastifyPluginAsync = async (fastify) => {
  fastify.setErrorHandler(async (error: FastifyError, request, reply) => {
    const requestId = (request as RequestWithId).requestId || 'unknown';

    // Log error (sanitized in production)
    request.log.error({
      requestId,
      error: {
        message: error.message,
        code: error.code,
        statusCode: error.statusCode
      }
    }, 'Request error');

    // Handle different error types
    let statusCode = 500;
    let errorCode: string = ERROR_CODES.INTERNAL_ERROR;
    let message = 'Internal server error';
    let details: ValidationErrorDetail[] | undefined = undefined;

    if (error instanceof ZodError) {
      // Validation errors
      statusCode = 400;
      errorCode = ERROR_CODES.VALIDATION_ERROR;
      message = 'Invalid request parameters';
      details = error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code
      }));
    } else if (error.statusCode) {
      // Fastify errors
      statusCode = error.statusCode;
      message = error.message;
      
      switch (statusCode) {
        case 400:
          errorCode = ERROR_CODES.VALIDATION_ERROR;
          break;
        case 401:
          errorCode = ERROR_CODES.INVALID_API_KEY;
          break;
        case 404:
          errorCode = ERROR_CODES.NOT_FOUND;
          break;
        case 429:
          errorCode = ERROR_CODES.RATE_LIMIT_EXCEEDED;
          break;
        case 503:
          errorCode = ERROR_CODES.SERVICE_UNAVAILABLE;
          break;
        case 422:
          errorCode = ERROR_CODES.TRACKING_NOT_FOUND;
          break;
        default:
          errorCode = ERROR_CODES.INTERNAL_ERROR;
      }
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      // Database connection errors
      errorCode = ERROR_CODES.DATABASE_ERROR;
      message = 'Database connection failed';
    }

    // Create sanitized error response
    const sanitizedError = createSanitizedError(
      { message, details, code: errorCode },
      statusCode,
      request
    );

    // Log security event if needed
    logErrorSanitization(error, sanitizedError, request);

    const errorResponse: ErrorResponse = {
      error: {
        code: errorCode,
        message: sanitizedError.message,
        ...(sanitizedError.details && { details: sanitizedError.details }),
        requestId: sanitizedError.requestId,
        timestamp: sanitizedError.timestamp
      }
    };

    reply.status(statusCode).send(errorResponse);
  });
};
