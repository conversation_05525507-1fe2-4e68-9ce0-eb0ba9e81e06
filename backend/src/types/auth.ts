/**
 * Authentication and User Management Types
 * 
 * This file contains all TypeScript interfaces and types for the SaaS authentication system.
 * Follows the coding standards and implementation guidelines.
 */

// =============================================================================
// USER TYPES
// =============================================================================

export type UserRole = 'ADMIN' | 'CUSTOMER';

export interface User {
  id: string;
  email: string;
  password_hash?: string;
  google_id?: string;
  google_email?: string;
  first_name?: string;
  last_name?: string;
  role: UserRole;
  is_active: boolean;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

export interface CreateUserRequest {
  email: string;
  password?: string | null;
  google_id?: string | null;
  google_email?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  role?: UserRole;
}

export interface UpdateUserRequest {
  email?: string | null;
  password?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  role?: UserRole | null;
  is_active?: boolean | null;
  email_verified?: boolean | null;
  google_id?: string | null;
  google_email?: string | null;
}

export interface UserProfile {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  role: UserRole;
  is_active: boolean;
  email_verified: boolean;
  created_at: string;
  last_login_at?: string | null;
}

// =============================================================================
// AUTHENTICATION TYPES
// =============================================================================

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  confirmPassword: string;
  first_name?: string;
  last_name?: string;
}

export interface GoogleOAuthRequest {
  code: string;
  state?: string;
}

export interface AuthResponse {
  user: UserProfile;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// =============================================================================
// JWT TOKEN TYPES
// =============================================================================

export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
  jti?: string; // JWT ID for token tracking
}

export interface RefreshToken {
  id: string;
  user_id: string;
  token_hash: string;
  expires_at: string;
  created_at: string;
  last_used_at?: string;
  user_agent?: string;
  ip_address?: string;
  is_revoked: boolean;
  revoked_at?: string;
  revoked_reason?: string;
}

// =============================================================================
// PASSWORD RESET TYPES
// =============================================================================

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface PasswordResetToken {
  id: string;
  user_id: string;
  token_hash: string;
  expires_at: string;
  created_at: string;
  used_at?: string;
}

// =============================================================================
// EMAIL VERIFICATION TYPES
// =============================================================================

export interface EmailVerificationRequest {
  token: string;
}

export interface ResendVerificationRequest {
  email: string;
}

export interface EmailVerificationToken {
  id: string;
  user_id: string;
  token_hash: string;
  expires_at: string;
  created_at: string;
  used_at?: string;
}

// =============================================================================
// MIDDLEWARE TYPES
// =============================================================================

export interface AuthenticatedRequest {
  user: UserProfile;
  token: JWTPayload;
}

export interface AuthMiddlewareOptions {
  required?: boolean;
  roles?: UserRole[];
  allowApiKey?: boolean;
}

// =============================================================================
// SERVICE TYPES
// =============================================================================

export interface UserServiceOptions {
  includeInactive?: boolean;
  includeUnverified?: boolean;
}

export interface UserListQuery {
  page?: number;
  limit?: number;
  role?: UserRole | null;
  is_active?: boolean | null;
  email_verified?: boolean | null;
  search?: string | null;
  sort_by?: 'created_at' | 'updated_at' | 'email' | 'last_login_at';
  sort_order?: 'asc' | 'desc';
}

export interface UserListResponse {
  users: UserProfile[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// =============================================================================
// ERROR TYPES
// =============================================================================

export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export type AuthErrorCode = 
  | 'INVALID_CREDENTIALS'
  | 'USER_NOT_FOUND'
  | 'USER_INACTIVE'
  | 'EMAIL_NOT_VERIFIED'
  | 'TOKEN_EXPIRED'
  | 'TOKEN_INVALID'
  | 'TOKEN_REVOKED'
  | 'INSUFFICIENT_PERMISSIONS'
  | 'EMAIL_ALREADY_EXISTS'
  | 'WEAK_PASSWORD'
  | 'OAUTH_ERROR'
  | 'RATE_LIMITED';

// =============================================================================
// VALIDATION TYPES
// =============================================================================

export interface PasswordValidation {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
}

export interface EmailValidation {
  isValid: boolean;
  error?: string;
}

// =============================================================================
// GOOGLE OAUTH TYPES
// =============================================================================

export interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  locale?: string;
}

export interface GoogleOAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
}

// =============================================================================
// AUDIT TYPES
// =============================================================================

export interface AuthAuditLog {
  id: string;
  user_id?: string;
  action: string;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export type AuthAuditAction = 
  | 'LOGIN_SUCCESS'
  | 'LOGIN_FAILED'
  | 'LOGOUT'
  | 'REGISTER'
  | 'PASSWORD_RESET_REQUESTED'
  | 'PASSWORD_RESET_COMPLETED'
  | 'EMAIL_VERIFIED'
  | 'TOKEN_REFRESHED'
  | 'TOKEN_REVOKED'
  | 'OAUTH_LOGIN'
  | 'USER_CREATED'
  | 'USER_UPDATED'
  | 'USER_DELETED';
