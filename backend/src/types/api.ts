import { FastifyRequest } from 'fastify';

// API Key types
export interface ApiKey {
  id: string;
  userId: string;
  name: string;
  isActive: boolean;
  rateLimitPerMinute: number;
  rateLimitPerDay: number;
  rateLimitBurst: number;
  totalRequests: number;
  requestsThisMonth: number;
  lastResetDate: Date;
  lastUsedAt?: Date;
  createdAt: Date;
  expiresAt?: Date;
}

// Enhanced request type with API key info
export interface ApiKeyAuthRequest extends FastifyRequest {
  apiKey?: {
    id: string;
    userId: string;
    rateLimitPerMinute: number;
    rateLimitPerDay: number;
    rateLimitBurst: number;
  };
  requestId: string;
  startTime: number;
}

// Error response format (camelCase for API consistency)
export interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    requestId: string;
    timestamp: string;
  };
}

// Health check response
export interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  version: string;
  checks: {
    database: 'healthy' | 'unhealthy';
    cache: 'healthy' | 'unhealthy';
    apiKeys: 'healthy' | 'unhealthy';
  };
}

// Common error codes
export const ERROR_CODES = {
  MISSING_API_KEY: 'MISSING_API_KEY',
  INVALID_API_KEY: 'INVALID_API_KEY',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  REQUEST_TIMEOUT: 'REQUEST_TIMEOUT',
  PAYLOAD_TOO_LARGE: 'PAYLOAD_TOO_LARGE',
  METHOD_NOT_ALLOWED: 'METHOD_NOT_ALLOWED',
  UNSUPPORTED_MEDIA_TYPE: 'UNSUPPORTED_MEDIA_TYPE',
  BAD_REQUEST: 'BAD_REQUEST',
  TRACKING_NOT_FOUND: 'TRACKING_NOT_FOUND',
  TRACKING_PROVIDER_UNAVAILABLE: 'TRACKING_PROVIDER_UNAVAILABLE'
} as const;

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

// Configuration types
export interface DatabaseConfig {
  maxConnections: number;
  minConnections: number;
  queryTimeout: number;
  statementTimeout: number;
  idleTimeout: number;
  connectionTimeout: number;
}

export interface PerformanceConfig {
  cacheSize: number;
  maxConcurrentQueries: number;
  queryComplexityLimit: number;
  slowRequestThreshold: number;
  verySlowRequestThreshold: number;
  slowDbQueryThreshold: number;
  verySlowDbQueryThreshold: number;
}

export interface RateLimitingConfig {
  defaultRateLimit: number;
  burstLimit: number;
  windowSize: number;
}

export interface SearchConfig {
  maxResults: number;
  trigramThreshold: number;
  searchTimeout: number;
}

export interface CacheConfig {
  fileCacheDurationDays: number;
  forceDownloadOnSync: boolean;
  defaultTtl: number;
  terminalDetailTtl: number;
  terminalListTtl: number;
  cleanupSchedule: string;
}

export interface ValidationConfig {
  maxPageLimit: number;
  maxResultsPerPage: number;
  maxTerminalIdLength: number;
  maxCityNameLength: number;
  maxSearchQueryLength: number;
  minSearchRadius: number;
  maxSearchRadius: number;
}

export interface SyncConfig {
  enableSmartSync: boolean;
  skipSyncWhenNoChanges: boolean;
}

export interface ConfigurationManager {
  database: DatabaseConfig;
  performance: PerformanceConfig;
  rateLimiting: RateLimitingConfig;
  search: SearchConfig;
  validation: ValidationConfig;
  cache: CacheConfig;
  sync: SyncConfig;
}

// Cache types
export interface CacheEntry {
  cacheKey: string;
  userId: string;
  cacheValue: any;
  expiresAt: Date;
  createdAt: Date;
}

// Coordinates type
export interface Coordinates {
  latitude: number;
  longitude: number;
  accuracy?: 'exact' | 'approximate' | 'city_level';
}
