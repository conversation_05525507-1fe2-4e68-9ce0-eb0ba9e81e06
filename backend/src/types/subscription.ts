/**
 * Subscription Management Types
 * 
 * TypeScript interfaces for subscription plans, user subscriptions,
 * orders, and audit logging in the SaaS system.
 */

// =============================================================================
// SUBSCRIPTION PLANS
// =============================================================================

export interface SubscriptionPlan {
  id: string;
  name: string;
  display_name: string;
  description: string | null;
  
  // Pricing
  price_monthly: number;
  price_yearly: number | null;
  currency: string;
  
  // Limits
  api_requests_per_month: number;
  api_requests_per_minute: number;
  rate_limit_per_day: number;
  rate_limit_burst: number;
  max_api_keys: number;
  
  // Features
  features: Record<string, any>;
  
  // Stripe integration
  stripe_price_id_monthly: string | null;
  stripe_price_id_yearly: string | null;
  stripe_product_id: string | null;
  
  // Status
  is_active: boolean;
  is_public: boolean;
  sort_order: number;
  
  // Timestamps
  created_at: Date;
  updated_at: Date;
}

export interface CreateSubscriptionPlanRequest {
  name: string;
  display_name: string;
  description?: string;
  price_monthly: number;
  price_yearly?: number;
  currency?: string;
  api_requests_per_month: number;
  api_requests_per_minute: number;
  max_api_keys?: number;
  features?: Record<string, any>;
  is_public?: boolean;
  sort_order?: number;
}

export interface UpdateSubscriptionPlanRequest {
  display_name?: string;
  description?: string;
  price_monthly?: number;
  price_yearly?: number;
  api_requests_per_month?: number;
  api_requests_per_minute?: number;
  max_api_keys?: number;
  features?: Record<string, any>;
  is_active?: boolean;
  is_public?: boolean;
  sort_order?: number;
}

// =============================================================================
// USER SUBSCRIPTIONS
// =============================================================================

export type SubscriptionStatus = 
  | 'active' 
  | 'canceled' 
  | 'past_due' 
  | 'unpaid' 
  | 'paused' 
  | 'trialing';

export type BillingCycle = 'monthly' | 'yearly';

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  
  // Status
  status: SubscriptionStatus;
  billing_cycle: BillingCycle;
  
  // Stripe integration
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
  
  // Billing periods
  current_period_start: Date;
  current_period_end: Date;
  trial_start: Date | null;
  trial_end: Date | null;
  canceled_at: Date | null;
  
  // Usage tracking
  api_requests_used: number;
  api_requests_reset_at: Date;
  
  // Timestamps
  created_at: Date;
  updated_at: Date;
  
  // Relations (populated when needed)
  plan?: SubscriptionPlan;
  user?: {
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
  };
}

export interface CreateSubscriptionRequest {
  plan_id: string;
  billing_cycle: BillingCycle;
  trial_days?: number;
  stripe_payment_method_id?: string;
  status?: SubscriptionStatus;
}

export interface UpdateSubscriptionRequest {
  plan_id?: string;
  billing_cycle?: BillingCycle;
  status?: SubscriptionStatus;
  canceled_at?: Date;
}

// =============================================================================
// ORDERS
// =============================================================================

export type OrderStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'canceled' 
  | 'refunded';

export interface Order {
  id: string;
  user_id: string;
  subscription_id: string | null;
  
  // Order details
  order_number: string;
  status: OrderStatus;
  
  // Financial information
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  
  // Stripe integration
  stripe_payment_intent_id: string | null;
  stripe_invoice_id: string | null;
  
  // Metadata
  billing_address: Record<string, any> | null;
  metadata: Record<string, any>;
  
  // Timestamps
  created_at: Date;
  updated_at: Date;
  completed_at: Date | null;
  
  // Relations (populated when needed)
  user?: {
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
  };
  subscription?: UserSubscription;
}

export interface CreateOrderRequest {
  subscription_id?: string;
  subtotal: number;
  tax_amount?: number;
  discount_amount?: number;
  billing_address?: Record<string, any>;
  metadata?: Record<string, any>;
  completed_at?: Date;
}

// =============================================================================
// AUDIT LOGGING
// =============================================================================

export interface SubscriptionAuditLog {
  id: string;
  user_id: string | null;
  subscription_id: string | null;
  order_id: string | null;
  
  // Event details
  event_type: string;
  event_description: string;
  
  // Change tracking
  old_values: Record<string, any> | null;
  new_values: Record<string, any> | null;
  
  // Context
  performed_by: string | null;
  ip_address: string | null;
  user_agent: string | null;
  
  // Timestamp
  created_at: Date;
}

export interface CreateAuditLogRequest {
  user_id?: string;
  subscription_id?: string;
  order_id?: string;
  event_type: string;
  event_description: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  performed_by?: string;
  ip_address?: string;
  user_agent?: string;
}

// =============================================================================
// API RESPONSES
// =============================================================================

export interface SubscriptionPlanListResponse {
  data: SubscriptionPlan[];
  meta: {
    total: number;
    timestamp: string;
  };
}

export interface UserSubscriptionResponse {
  data: UserSubscription;
  meta: {
    timestamp: string;
  };
}

export interface OrderListResponse {
  data: Order[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  meta: {
    timestamp: string;
  };
}

// =============================================================================
// STRIPE INTEGRATION TYPES
// =============================================================================

export interface StripeCustomerData {
  id: string;
  email: string;
  name?: string;
  metadata?: Record<string, string>;
}

export interface StripeSubscriptionData {
  id: string;
  customer: string;
  status: string;
  current_period_start: number;
  current_period_end: number;
  trial_start?: number;
  trial_end?: number;
  canceled_at?: number;
  items: {
    data: Array<{
      price: {
        id: string;
        recurring: {
          interval: 'month' | 'year';
        };
      };
    }>;
  };
}

export interface StripeWebhookEvent {
  id: string;
  type: string;
  data: {
    object: any;
  };
  created: number;
}

// =============================================================================
// USAGE TRACKING
// =============================================================================

export interface UsageStats {
  current_usage: number;
  limit: number;
  percentage_used: number;
  reset_date: Date;
  overage: number;
}

export interface SubscriptionUsage {
  api_requests: UsageStats;
  api_keys_used: number;
  api_keys_limit: number;
}

// =============================================================================
// QUERY TYPES
// =============================================================================

export interface SubscriptionListQuery {
  status?: SubscriptionStatus;
  plan_id?: string;
  page?: number;
  limit?: number;
  sort_by?: 'created_at' | 'updated_at' | 'current_period_end';
  sort_order?: 'asc' | 'desc';
}

export interface OrderListQuery {
  status?: OrderStatus;
  user_id?: string;
  subscription_id?: string;
  page?: number;
  limit?: number;
  sort_by?: 'created_at' | 'updated_at' | 'completed_at';
  sort_order?: 'asc' | 'desc';
  date_from?: string;
  date_to?: string;
}
