// Core terminal types with consistent camelCase naming

export enum TerminalType {
  PARCEL_LOCKER = 'PARCEL_LOCKER',
  POST_OFFICE = 'POST_OFFICE',
  PICKUP_POINT = 'PICKUP_POINT',
  DELIVERY_POINT = 'DELIVERY_POINT',
  AUTOMATED_STATION = 'AUTOMATED_STATION'
}

export interface PostalTerminal {
  // Core identification
  id: string;
  
  // Basic information
  name: string;
  
  // Location data (camelCase for API consistency)
  city: string;
  address: string;
  postalCode?: string;
  latitude: number;
  longitude: number;
  
  // Metadata
  updated: Date;
  
  // Future-proofing fields
  countryCode?: string;
  provider?: string;
  terminalType?: TerminalType;
  metadata?: TerminalMetadata;
}

export interface TerminalMetadata {
  serviceHours?: ServiceHours[];
  accessInstructions?: string;
  supportedServices?: string[];
  capacity?: number;
  restrictions?: string[];
  contactInfo?: ContactInfo;
  coordinateAccuracy?: 'exact' | 'approximate' | 'city_level';
  collectionTime?: string;
  workingHours?: string;
  [key: string]: any;
}

export interface ServiceHours {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  openTime?: string; // HH:mm format
  closeTime?: string; // HH:mm format
  isClosed: boolean;
  isHoliday?: boolean;
}

export interface ContactInfo {
  phone?: string;
  email?: string;
  website?: string;
}

// API Response types with consistent camelCase
export interface TerminalListResponse {
  data: PostalTerminal[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  meta: {
    requestId: string;
    responseTime: number;
    cacheHit: boolean;
  };
}

export interface TerminalDetailResponse {
  data: PostalTerminal;
  meta: {
    requestId: string;
    responseTime: number;
    cacheHit: boolean;
  };
}

export interface NearbyTerminalsResponse {
  data: Array<PostalTerminal & { distance: number }>;
  meta: {
    requestId: string;
    responseTime: number;
    searchCenter: { lat: number; lng: number };
    searchRadius: number;
    cacheHit: boolean;
  };
}

export interface TerminalSearchResponse {
  data: Array<PostalTerminal & {
    relevanceScore: number;
    matchedFields: string[];
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  meta: {
    requestId: string;
    responseTime: number;
    searchQuery: string;
    cacheHit: boolean;
  };
}

// Database row type (snake_case for database)
export interface TerminalRow {
  id: string;
  name: string;
  city: string;
  address: string;
  postal_code?: string;
  coordinates: string; // PostGIS point
  updated: Date;
  country_code: string;
  provider: string;
  terminal_type: string;
  metadata: any;
  is_active: boolean;
  search_vector: string;
  popularity_score: number;
  last_accessed?: Date;
}
