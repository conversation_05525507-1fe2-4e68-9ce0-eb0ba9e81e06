// Declarations for optional external modules that may not be installed in the main
// Postal Terminal API container. They are loaded dynamically only when certain
// features are enabled.

declare module 'node-fetch' {
  const fetch: any;
  export = fetch;
}

declare module 'puppeteer-extra' {
  const puppeteerExtra: any;
  export = puppeteerExtra;
}

declare module 'puppeteer-extra-plugin-stealth' {
  const stealthPlugin: () => any;
  export default stealthPlugin;
} 