/**
 * Standardized tracking types for unified event mapping across all providers
 */

export enum StandardTrackingStatus {
  CREATED = 'CREATED',
  ACCEPTED = 'ACCEPTED',
  IN_TRANSIT = 'IN_TRANSIT',
  OUT_FOR_DELIVERY = 'OUT_FOR_DELIVERY',
  DELIVERED = 'DELIVERED',
  RETURNED = 'RETURNED',
  EXCEPTION = 'EXCEPTION'
}

export interface StandardizedTrackingEvent {
  // Standardized fields
  status: StandardTrackingStatus;
  statusText: {
    en: string;
    lt?: string | undefined;
    original: string;
  };
  location?: string | undefined;
  timestamp: string; // ISO 8601 format

  // Provider-specific preservation
  providerEventId?: string | undefined;
  providerStatusCode?: string | undefined;
  rawData: Record<string, any>;
}

export interface StandardizedTrackingResult {
  provider: 'LP_EXPRESS' | 'OMNIVA' | 'DPD' | 'VENIPAK';
  trackingNumber: string;
  status: StandardTrackingStatus;
  statusText: {
    en: string;
    lt?: string | undefined;
    original: string;
  };
  events: StandardizedTrackingEvent[];
  lastUpdated: string; // ISO 8601 format
}
