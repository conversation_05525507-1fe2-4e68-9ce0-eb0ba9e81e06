import { config as dotenvConfig } from 'dotenv';
import { ConfigurationManager } from '../types/api';

// Load environment variables from .env files
dotenvConfig({ path: ['.env.local', '.env'] });

// Environment validation and configuration
export interface EnvironmentConfig {
  // Database
  DATABASE_URL: string;
  DATABASE_SSL: string;
  
  // API Security
  API_KEY_SECRET: string;
  
  // Data Collection
  UPDATE_FREQUENCY: 'weekly' | 'monthly';
  AUTO_UPDATE_ENABLED: string;
  
  // Application
  PORT: string;
  NODE_ENV: 'development' | 'production' | 'test';
  APP_VERSION: string;
  
  // Logging
  LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
}

// Validate and load environment configuration
export function validateEnvironment(): EnvironmentConfig {
  const nodeEnv = (process.env['NODE_ENV'] as 'development' | 'production' | 'test') || 'development';
  const isProduction = nodeEnv === 'production';

  // Always required variables
  const requiredVars = ['DATABASE_URL', 'API_KEY_SECRET'];

  // Production-only required variables (URLs that should be explicit in production)
  const productionRequiredVars = isProduction ? [
    'LP_EXPRESS_CSV_URL',
    'OMNIVA_JSON_URL',
    'DPD_EXCEL_URL',
    'VENIPAK_JSON_URL',
    'NOMINATIM_BASE_URL'
  ] : [];

  // Check all required variables
  const allRequired = [...requiredVars, ...productionRequiredVars];
  const missing = allRequired.filter(key => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}${isProduction ? ' (production mode)' : ''}`);
  }

  // Validate API_KEY_SECRET strength
  const apiKeySecret = process.env['API_KEY_SECRET']!;
  if (apiKeySecret.length < 32) {
    throw new Error('API_KEY_SECRET must be at least 32 characters long for security');
  }

  // Validate PORT (must be numeric, 1-65535)
  const portEnv = process.env['PORT'] || '3000';
  const portNum = Number(portEnv);
  if (Number.isNaN(portNum) || portNum < 1 || portNum > 65535) {
    throw new Error(`Invalid PORT value: ${portEnv}. Must be an integer between 1 and 65535`);
  }

  // Log configuration strategy for security audit
  console.log(`[CONFIG] Environment: ${nodeEnv}, Required vars: ${allRequired.length}, Production mode: ${isProduction}`);

  return {
    DATABASE_URL: process.env['DATABASE_URL']!,
    DATABASE_SSL: process.env['DATABASE_SSL'] || 'false',
    API_KEY_SECRET: apiKeySecret,
    UPDATE_FREQUENCY: (process.env['UPDATE_FREQUENCY'] as 'weekly' | 'monthly') || 'weekly',
    AUTO_UPDATE_ENABLED: process.env['AUTO_UPDATE_ENABLED'] || 'true',
    PORT: String(portNum),
    NODE_ENV: nodeEnv,
    APP_VERSION: process.env['APP_VERSION'] || '2.1.0',
    LOG_LEVEL: (process.env['LOG_LEVEL'] as 'debug' | 'info' | 'warn' | 'error') || 'info'
  };
}

// Application configuration with defaults
export const CONFIG: ConfigurationManager = {
  database: {
    maxConnections: parseInt(process.env['DB_MAX_CONNECTIONS'] || '20'),
    minConnections: parseInt(process.env['DB_MIN_CONNECTIONS'] || '2'),
    queryTimeout: parseInt(process.env['DB_QUERY_TIMEOUT'] || '30000'),
    statementTimeout: parseInt(process.env['DB_STATEMENT_TIMEOUT'] || '60000'),
    idleTimeout: parseInt(process.env['DB_IDLE_TIMEOUT'] || '300000'), // 5 minutes
    connectionTimeout: parseInt(process.env['DB_CONNECTION_TIMEOUT'] || '30000')
  },
  performance: {
    cacheSize: parseInt(process.env['CACHE_SIZE'] || '100000'),
    maxConcurrentQueries: parseInt(process.env['MAX_CONCURRENT_QUERIES'] || '100'),
    queryComplexityLimit: parseInt(process.env['QUERY_COMPLEXITY_LIMIT'] || '1000'),
    slowRequestThreshold: parseInt(process.env['SLOW_REQUEST_THRESHOLD_MS'] || '1000'),
    verySlowRequestThreshold: parseInt(process.env['VERY_SLOW_REQUEST_THRESHOLD_MS'] || '5000'),
    slowDbQueryThreshold: parseInt(process.env['SLOW_DB_QUERY_THRESHOLD_MS'] || '100'),
    verySlowDbQueryThreshold: parseInt(process.env['VERY_SLOW_DB_QUERY_THRESHOLD_MS'] || '500')
  },
  rateLimiting: {
    defaultRateLimit: parseInt(process.env['DEFAULT_RATE_LIMIT'] || '1000'),
    burstLimit: parseInt(process.env['BURST_LIMIT'] || '2000'),
    windowSize: parseInt(process.env['RATE_WINDOW_SIZE'] || '60')
  },
  search: {
    maxResults: parseInt(process.env['SEARCH_MAX_RESULTS'] || '100'),
    trigramThreshold: parseFloat(process.env['TRIGRAM_THRESHOLD'] || '0.3'),
    searchTimeout: parseInt(process.env['SEARCH_TIMEOUT'] || '5000')
  },
  validation: {
    maxPageLimit: parseInt(process.env['MAX_PAGE_LIMIT'] || '1000'),
    maxResultsPerPage: parseInt(process.env['MAX_RESULTS_PER_PAGE'] || '100'),
    maxTerminalIdLength: parseInt(process.env['MAX_TERMINAL_ID_LENGTH'] || '255'),
    maxCityNameLength: parseInt(process.env['MAX_CITY_NAME_LENGTH'] || '255'),
    maxSearchQueryLength: parseInt(process.env['MAX_SEARCH_QUERY_LENGTH'] || '255'),
    minSearchRadius: parseFloat(process.env['MIN_SEARCH_RADIUS'] || '0.1'),
    maxSearchRadius: parseInt(process.env['MAX_SEARCH_RADIUS'] || '100')
  },
  cache: {
    defaultTtl: parseInt(process.env['DEFAULT_CACHE_TTL_SECONDS'] || '300'),
    terminalDetailTtl: parseInt(process.env['TERMINAL_DETAIL_CACHE_TTL'] || '600'),
    terminalListTtl: parseInt(process.env['TERMINAL_LIST_CACHE_TTL'] || '300'),
    cleanupSchedule: process.env['CACHE_CLEANUP_SCHEDULE'] || '0 3 * * *',
    fileCacheDurationDays: parseInt(process.env['FILE_CACHE_DURATION_DAYS'] || '30'),
    forceDownloadOnSync: process.env['FORCE_DOWNLOAD_ON_SYNC'] === 'true'
  },
  sync: {
    enableSmartSync: process.env['ENABLE_SMART_SYNC'] !== 'false', // Default to true
    skipSyncWhenNoChanges: process.env['SKIP_SYNC_WHEN_NO_CHANGES'] !== 'false' // Default to true
  }
};

// Data update configuration
export interface UpdateConfig {
  updateFrequency: 'weekly' | 'monthly';
  cronExpression: string;
  enabled: boolean;
}

export const UPDATE_CONFIG: UpdateConfig = {
  updateFrequency: process.env['UPDATE_FREQUENCY'] as 'weekly' | 'monthly' || 'weekly',
  cronExpression: process.env['UPDATE_FREQUENCY'] === 'monthly'
    ? '0 2 1 * *'      // Monthly on 1st at 2 AM
    : '0 2 * * 1',     // Weekly on Monday at 2 AM
  enabled: process.env['AUTO_UPDATE_ENABLED'] !== 'false'
};

// Geocoding configuration
export const GEOCODING_CONFIG = {
  baseUrl: process.env['NOMINATIM_BASE_URL'] || 'https://nominatim.openstreetmap.org/search',
  rateLimitDelay: parseInt(process.env['GEOCODING_RATE_LIMIT_DELAY'] || '1000'),
  smartGeocodingLimit: parseInt(process.env['SMART_GEOCODING_LIMIT'] || '10'),
  smartGeocodingRateDelay: parseInt(process.env['SMART_GEOCODING_RATE_DELAY'] || '800')
};

// Tracking configuration
export const TRACKING_CONFIG = {
  // Provider endpoints
  lpExpressTrackingUrl: process.env['LP_EXPRESS_TRACKING_URL'] || 'https://api-esavitarna.post.lt/tracking',
  omnivaTrackingUrl: process.env['OMNIVA_TRACKING_URL'] || 'https://mano.omniva.lt/api/track/shipment',
  venipakTrackingUrl: process.env['VENIPAK_TRACKING_URL'] || 'https://venipak.com/lt/wp-json/v1/shipment-tracker',
  dpdTrackingUrl: process.env['DPD_TRACKING_URL'] || 'https://www.dpdgroup.com/lt/mydpd/my-parcels/incoming',

  // Request timeouts
  defaultTimeout: parseInt(process.env['TRACKING_DEFAULT_TIMEOUT'] || '10000'),
  dpdTimeout: parseInt(process.env['DPD_TRACKING_TIMEOUT'] || '60000'),

  // Feature flags
  enableDpdTracking: process.env['ENABLE_DPD_TRACKING'] === 'true'
};

// Additional configuration sections for externalized values
export const RATE_LIMITING_CONFIG = {
  rootEndpointLimit: parseInt(process.env['ROOT_ENDPOINT_RATE_LIMIT'] || '100'),
  strictMultiplier: parseFloat(process.env['STRICT_RATE_LIMIT_MULTIPLIER'] || '0.1'),
  healthMultiplier: parseFloat(process.env['HEALTH_RATE_LIMIT_MULTIPLIER'] || '2.0')
};

export const FILE_DOWNLOAD_CONFIG = {
  timeout: parseInt(process.env['FILE_DOWNLOAD_TIMEOUT_MS'] || '60000'),
  maxRetries: parseInt(process.env['FILE_DOWNLOAD_MAX_RETRIES'] || '3'),
  retryBaseDelay: parseInt(process.env['FILE_DOWNLOAD_RETRY_BASE_DELAY_MS'] || '1000'),
  userAgent: process.env['FILE_DOWNLOAD_USER_AGENT'] || 'PostalTerminalAPI/2.1.0'
};

export const TRACKING_SERVICE_CONFIG = {
  deliveredCacheTtl: parseInt(process.env['TRACKING_DELIVERED_CACHE_TTL'] || '86400'),
  activeCacheTtl: parseInt(process.env['TRACKING_ACTIVE_CACHE_TTL'] || '300'),
  maxFailureCount: parseInt(process.env['TRACKING_MAX_FAILURE_COUNT'] || '5'),
  circuitBreakerTimeout: parseInt(process.env['TRACKING_CIRCUIT_BREAKER_TIMEOUT_MS'] || '300000')
};

export const WEBHOOK_CONFIG = {
  timeout: parseInt(process.env['WEBHOOK_TIMEOUT_MS'] || '10000')
};

export const SYNC_CONFIG = {
  initialDelay: parseInt(process.env['INITIAL_SYNC_DELAY_MS'] || '5000')
};

// Data source configurations
export interface DataSourceProvider {
  name: string;
  type: 'API' | 'CSV' | 'JSON' | 'XML' | 'EXCEL';
  endpoint: string;
  headers?: Record<string, string>;
  transformFunction: string;
}

export const DATA_SOURCES: Record<string, DataSourceProvider[]> = {
  'LT': [
    {
      name: 'LP_EXPRESS',
      type: 'CSV',
      endpoint: process.env['LP_EXPRESS_CSV_URL'] || 'https://api-esavitarna.post.lt/terminal/list/csv',
      headers: {
        'origin': process.env['LP_EXPRESS_ORIGIN_URL'] || 'https://lpexpress.lt',
        'referer': process.env['LP_EXPRESS_REFERER_URL'] || 'https://lpexpress.lt/',
        'user-agent': process.env['USER_AGENT'] || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      },
      transformFunction: 'transformLPExpressData'
    },
    {
      name: 'OMNIVA',
      type: 'JSON',
      endpoint: process.env['OMNIVA_JSON_URL'] || 'https://www.omniva.lt/locations.json',
      transformFunction: 'transformOmnivaData'
    },
    {
      name: 'DPD',
      type: 'EXCEL',
      endpoint: process.env['DPD_EXCEL_URL'] || 'https://www.dpd.com/lt/content/dam/dpd_lt/documents/pickup_locations.xlsx',
      headers: {
        'referer': process.env['DPD_REFERER_URL'] || 'https://www.dpd.com/lt/private/send_parcel/pickup_locations',
        'user-agent': process.env['USER_AGENT'] || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      },
      transformFunction: 'transformDPDData'
    }
  ]
};

// Export config as default export for easier importing
export const config = {
  ...CONFIG,
  environment: validateEnvironment(),
  update: UPDATE_CONFIG,
  geocoding: GEOCODING_CONFIG,
  tracking: TRACKING_CONFIG,
  dataSources: DATA_SOURCES,

  // Additional configuration
  dataSyncSchedule: process.env['DATA_SYNC_SCHEDULE'] || UPDATE_CONFIG.cronExpression, // Use update config cron instead of daily
  runInitialSync: process.env['RUN_INITIAL_SYNC'] === 'true',
  syncTimeout: parseInt(process.env['SYNC_TIMEOUT'] || '1800000'),
  fileCacheDurationDays: parseInt(process.env['FILE_CACHE_DURATION_DAYS'] || '30'),
  enableSmartSync: process.env['ENABLE_SMART_SYNC'] !== 'false', // Default to true
  skipSyncWhenNoChanges: process.env['SKIP_SYNC_WHEN_NO_CHANGES'] !== 'false', // Default to true

  // Search configuration
  searchMinLength: parseInt(process.env['SEARCH_MIN_LENGTH'] || '2'),
  nearbyMaxRadius: parseInt(process.env['NEARBY_MAX_RADIUS'] || '100'),
  nearbyDefaultRadius: parseInt(process.env['NEARBY_DEFAULT_RADIUS'] || '10'),

  // Data source URLs (environment-aware configuration)
  // In production, these MUST be explicitly set via environment variables
  // In development, fallback values are provided for convenience
  lpExpressCsvUrl: (() => {
    const env = process.env['NODE_ENV'] || 'development';
    const url = process.env['LP_EXPRESS_CSV_URL'];
    return env === 'production' ? url! : (url || 'https://api-esavitarna.post.lt/terminal/list/csv');
  })(),
  omnivaJsonUrl: (() => {
    const env = process.env['NODE_ENV'] || 'development';
    const url = process.env['OMNIVA_JSON_URL'];
    return env === 'production' ? url! : (url || 'https://www.omniva.lt/locations.json');
  })(),
  dpdExcelUrl: (() => {
    const env = process.env['NODE_ENV'] || 'development';
    const url = process.env['DPD_EXCEL_URL'];
    return env === 'production' ? url! : (url || 'https://www.dpd.com/lt/content/dam/dpd_lt/documents/pickup_locations.xlsx');
  })(),
  venipakJsonUrl: (() => {
    const env = process.env['NODE_ENV'] || 'development';
    const url = process.env['VENIPAK_JSON_URL'];
    return env === 'production' ? url! : (url || 'https://go.venipak.lt/ws/get_pickup_points');
  })()
};
