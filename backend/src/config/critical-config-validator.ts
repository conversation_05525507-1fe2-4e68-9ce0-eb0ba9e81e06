/**
 * Critical Configuration Validator
 * 
 * Validates that all critical configuration variables are properly set
 * and throws descriptive errors during application startup if any are missing.
 */

import { getNotificationService } from '../services/notification-service';

export interface CriticalConfigError {
  variable: string;
  reason: string;
  suggestion: string;
}

export class CriticalConfigurationError extends Error {
  public readonly errors: CriticalConfigError[];
  
  constructor(errors: CriticalConfigError[]) {
    const message = `Critical configuration errors detected:\n${
      errors.map(err => `  • ${err.variable}: ${err.reason}\n    Suggestion: ${err.suggestion}`).join('\n')
    }`;
    
    super(message);
    this.name = 'CriticalConfigurationError';
    this.errors = errors;
  }
}

/**
 * Critical configuration variables that MUST be set in production
 */
const CRITICAL_PRODUCTION_VARS = [
  {
    name: 'DATABASE_URL',
    validator: (value: string | undefined) => {
      if (!value) return 'Database URL is required';
      if (!value.startsWith('postgresql://') && !value.startsWith('postgres://')) {
        return 'Database URL must be a valid PostgreSQL connection string';
      }
      return null;
    },
    suggestion: 'Set DATABASE_URL to a valid PostgreSQL connection string (e.g., postgresql://user:pass@host:port/dbname)'
  },
  {
    name: 'API_KEY_SECRET',
    validator: (value: string | undefined) => {
      if (!value) return 'API key secret is required for secure authentication';
      if (value.length < 32) return 'API key secret must be at least 32 characters long';
      return null;
    },
    suggestion: 'Set API_KEY_SECRET to a strong, randomly generated secret (minimum 32 characters)'
  },
  {
    name: 'JWT_SECRET',
    validator: (value: string | undefined) => {
      if (!value) return 'JWT secret is required for secure authentication';
      if (value.length < 32) return 'JWT secret must be at least 32 characters long';
      if (value === 'your-jwt-secret-key-change-in-production-min-32-chars') {
        return 'JWT secret must be changed from the default example value';
      }
      return null;
    },
    suggestion: 'Set JWT_SECRET to a strong, randomly generated secret (minimum 32 characters)'
  }
];

/**
 * Configuration variables that should be explicitly set in production
 * (have fallbacks but should be configured for production use)
 */
const PRODUCTION_RECOMMENDED_VARS = [
  {
    name: 'LP_EXPRESS_CSV_URL',
    suggestion: 'Set LP_EXPRESS_CSV_URL to the official LP Express API endpoint'
  },
  {
    name: 'OMNIVA_JSON_URL',
    suggestion: 'Set OMNIVA_JSON_URL to the official Omniva API endpoint'
  },
  {
    name: 'DPD_EXCEL_URL',
    suggestion: 'Set DPD_EXCEL_URL to the official DPD API endpoint'
  },
  {
    name: 'VENIPAK_JSON_URL',
    suggestion: 'Set VENIPAK_JSON_URL to the official Venipak API endpoint'
  },
  {
    name: 'NOMINATIM_BASE_URL',
    suggestion: 'Set NOMINATIM_BASE_URL to your preferred geocoding service endpoint'
  }
];

/**
 * Validates all critical configuration variables
 */
export function validateCriticalConfiguration(): void {
  const errors: CriticalConfigError[] = [];
  const isProduction = process.env['NODE_ENV'] === 'production';
  
  // Validate critical variables
  for (const config of CRITICAL_PRODUCTION_VARS) {
    const value = process.env[config.name];
    const error = config.validator(value);
    
    if (error) {
      errors.push({
        variable: config.name,
        reason: error,
        suggestion: config.suggestion
      });
    }
  }
  
  // In production, warn about recommended variables
  if (isProduction) {
    for (const config of PRODUCTION_RECOMMENDED_VARS) {
      const value = process.env[config.name];
      
      if (!value) {
        console.warn(`⚠️  PRODUCTION WARNING: ${config.name} not set. ${config.suggestion}`);
      }
    }
  }
  
  // If there are critical errors, throw immediately
  if (errors.length > 0) {
    // Try to notify about the configuration error before throwing
    try {
      const notificationService = getNotificationService();
      const errorMessage = `Critical configuration errors detected: ${
        errors.map(err => `${err.variable}: ${err.reason}`).join(', ')
      }`;
      
      // Fire and forget notification - don't wait for it
      notificationService.notifyDatabaseIssue('critical_config_error', errorMessage).catch(() => {
        // Ignore notification failures during startup
      });
    } catch {
      // Ignore notification service errors during startup
    }
    
    throw new CriticalConfigurationError(errors);
  }
}

/**
 * Validates configuration and logs the result
 */
export function validateAndLogConfiguration(): void {
  try {
    validateCriticalConfiguration();
    console.log('✅ Critical configuration validation passed');
  } catch (error) {
    if (error instanceof CriticalConfigurationError) {
      console.error('❌ Critical configuration validation failed:');
      console.error(error.message);
      console.error('\n🛑 Application cannot start with missing critical configuration.');
      console.error('Please set the required environment variables and restart the application.\n');
      
      // Exit the process - don't allow the application to start
      process.exit(1);
    }
    
    // Re-throw other errors
    throw error;
  }
}

/**
 * Validates that no default/example values are being used in production
 */
export function validateNoDefaultValues(): void {
  const isProduction = process.env['NODE_ENV'] === 'production';
  
  if (!isProduction) {
    return; // Only validate in production
  }
  
  const dangerousDefaults = [
    {
      name: 'API_KEY_SECRET',
      dangerousValues: ['97abd5aertetye14cdd84ef906cd34db5302019a180a566dfd269f5076ee51721ecf1d33c']
    },
    {
      name: 'JWT_SECRET',
      dangerousValues: ['your-jwt-secret-key-change-in-production-min-32-chars']
    },
    {
      name: 'DATABASE_URL',
      dangerousValues: ['postgresql://user:pass@localhost:5432/dbname']
    }
  ];
  
  const errors: CriticalConfigError[] = [];
  
  for (const config of dangerousDefaults) {
    const value = process.env[config.name];
    
    if (value && config.dangerousValues.includes(value)) {
      errors.push({
        variable: config.name,
        reason: 'Using default/example value in production',
        suggestion: `Change ${config.name} from the default example value to a production-appropriate value`
      });
    }
  }
  
  if (errors.length > 0) {
    throw new CriticalConfigurationError(errors);
  }
}
