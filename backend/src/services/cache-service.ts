import { getDatabasePool } from '../database/connection';
import { CONFIG } from '../config';

// Structured logger
const logger = {
  info: (message: string, data?: any) => console.log(`[${new Date().toISOString()}] INFO: ${message}`, data || ''),
  error: (message: string, error?: any) => console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error || ''),
  debug: (message: string, data?: any) => console.log(`[${new Date().toISOString()}] DEBUG: ${message}`, data || '')
};

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tenantId?: string;
  compress?: boolean;
}

// Note: CacheEntry interface is defined in types/api.ts

interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  hitRate: number;
}

export class PostgreSQLCacheService {
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    hitRate: 0
  };

  private defaultTtl = CONFIG.cache.defaultTtl; // Configurable default TTL


  async get<T = any>(key: string, options: CacheOptions = {}): Promise<T | null> {
    const pool = getDatabasePool();
    const userId = options.tenantId || null; // Use tenantId as userId for backward compatibility

    try {
      const result = await pool.query(`
        SELECT cache_value, expires_at
        FROM api_cache
        WHERE cache_key = $1 AND user_id = $2 AND expires_at > NOW()
      `, [key, userId]);

      if (result.rows.length === 0) {
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }

      this.stats.hits++;
      this.updateHitRate();

      const cacheData = result.rows[0];
      return cacheData.cache_value as T;
    } catch (error) {
      logger.error('Cache get error:', error);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  async set<T = any>(key: string, value: T, options: CacheOptions = {}): Promise<boolean> {
    const pool = getDatabasePool();
    const userId = options.tenantId || null; // Use tenantId as userId for backward compatibility
    const ttl = options.ttl || this.defaultTtl;
    const expiresAt = new Date(Date.now() + ttl * 1000);

    try {
      await pool.query(`
        INSERT INTO api_cache (cache_key, user_id, cache_value, expires_at)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (cache_key, user_id)
        DO UPDATE SET
          cache_value = EXCLUDED.cache_value,
          expires_at = EXCLUDED.expires_at,
          created_at = NOW()
      `, [key, userId, JSON.stringify(value), expiresAt]);

      this.stats.sets++;
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  }

  async delete(key: string, options: CacheOptions = {}): Promise<boolean> {
    const pool = getDatabasePool();
    const userId = options.tenantId || null; // Use tenantId as userId for backward compatibility

    try {
      const result = await pool.query(`
        DELETE FROM api_cache
        WHERE cache_key = $1 AND user_id = $2
      `, [key, userId]);

      this.stats.deletes++;
      return (result.rowCount || 0) > 0;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  }

  async exists(key: string, options: CacheOptions = {}): Promise<boolean> {
    const pool = getDatabasePool();
    const userId = options.tenantId || null; // Use tenantId as userId for backward compatibility

    try {
      const result = await pool.query(`
        SELECT 1 FROM api_cache
        WHERE cache_key = $1 AND user_id = $2 AND expires_at > NOW()
      `, [key, userId]);

      return result.rows.length > 0;
    } catch (error) {
      logger.error('Cache exists error:', error);
      return false;
    }
  }

  async getOrSet<T = any>(
    key: string,
    factory: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    // Generate new value
    const value = await factory();
    
    // Store in cache
    await this.set(key, value, options);
    
    return value;
  }

  async invalidatePattern(pattern: string, options: CacheOptions = {}): Promise<number> {
    const pool = getDatabasePool();
    const userId = options.tenantId || null; // Use tenantId as userId for backward compatibility

    try {
      const result = await pool.query(`
        DELETE FROM api_cache
        WHERE cache_key LIKE $1 AND user_id = $2
      `, [pattern, userId]);

      const deletedCount = result.rowCount || 0;
      this.stats.deletes += deletedCount;
      return deletedCount;
    } catch (error) {
      logger.error('Cache invalidate pattern error:', error);
      return 0;
    }
  }

  async cleanExpired(): Promise<number> {
    const pool = getDatabasePool();

    try {
      const result = await pool.query(`
        DELETE FROM api_cache
        WHERE expires_at <= NOW()
      `);

      const cleanedCount = result.rowCount || 0;
      logger.info(`🧹 Cleaned ${cleanedCount} expired cache entries`);
      return cleanedCount;
    } catch (error) {
      logger.error('Cache cleanup error:', error);
      return 0;
    }
  }

  async clear(userId?: string | null): Promise<number> {
    const pool = getDatabasePool();
    const targetUserId = userId || null;

    try {
      const result = await pool.query(`
        DELETE FROM api_cache
        WHERE user_id = $1
      `, [targetUserId]);

      const deletedCount = result.rowCount || 0;
      this.stats.deletes += deletedCount;
      return deletedCount;
    } catch (error) {
      logger.error('Cache clear error:', error);
      return 0;
    }
  }

  async getStats(): Promise<CacheStats & { totalEntries: number; userEntries: Record<string, number> }> {
    const pool = getDatabasePool();

    try {
      // Get total entries
      const totalResult = await pool.query(`
        SELECT COUNT(*) as total FROM api_cache WHERE expires_at > NOW()
      `);
      const totalEntries = parseInt(totalResult.rows[0].total);

      // Get entries per user
      const userResult = await pool.query(`
        SELECT user_id, COUNT(*) as count
        FROM api_cache
        WHERE expires_at > NOW()
        GROUP BY user_id
      `);

      const userEntries: Record<string, number> = {};
      userResult.rows.forEach(row => {
        const userId = row.user_id || 'anonymous';
        userEntries[userId] = parseInt(row.count);
      });

      return {
        ...this.stats,
        totalEntries,
        userEntries
      };
    } catch (error) {
      logger.error('Cache stats error:', error);
      return {
        ...this.stats,
        totalEntries: 0,
        userEntries: {}
      };
    }
  }

  async warmup(keys: Array<{ key: string; factory: () => Promise<any>; options?: CacheOptions }>): Promise<number> {
    let warmedUp = 0;

    for (const { key, factory, options } of keys) {
      try {
        const exists = await this.exists(key, options);
        if (!exists) {
          const value = await factory();
          const success = await this.set(key, value, options);
          if (success) {
            warmedUp++;
          }
        }
      } catch (error) {
        logger.error(`Cache warmup error for key ${key}:`, error);
      }
    }

    logger.info(`🔥 Cache warmup completed: ${warmedUp} entries warmed up`);
    return warmedUp;
  }

  // Helper method to generate cache keys
  generateKey(prefix: string, ...parts: (string | number)[]): string {
    return `${prefix}:${parts.join(':')}`;
  }

  // Helper method to generate terminal-related cache keys
  generateTerminalKey(type: 'list' | 'search' | 'nearby' | 'detail', ...params: (string | number)[]): string {
    return this.generateKey('terminal', type, ...params);
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }

  // Reset stats (useful for testing)
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      hitRate: 0
    };
  }
}

// Export singleton instance
export const cacheService = new PostgreSQLCacheService();
