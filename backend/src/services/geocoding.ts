import { CoordinatesType } from '../validation/schemas';
import { GEOCODING_CONFIG } from '../config';

// Additional geocoding configuration
const GEOCODING_RESULT_LIMIT = parseInt(process.env['GEOCODING_RESULT_LIMIT'] || '5');

interface NominatimResponse {
  lat: string;
  lon: string;
  display_name: string;
  importance: number;
  place_id: number;
  licence: string;
  osm_type: string;
  osm_id: number;
  boundingbox: string[];
}

interface GeocodingContext {
  city?: string;
  postalCode?: string;
  country?: string;
}

// Structured logger
const logger = {
  info: (message: string, data?: any) => console.log(`[${new Date().toISOString()}] INFO: ${message}`, data || ''),
  warn: (message: string, data?: any) => console.warn(`[${new Date().toISOString()}] WARN: ${message}`, data || ''),
  error: (message: string, error?: any) => console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error || ''),
  debug: (message: string, data?: any) => console.log(`[${new Date().toISOString()}] DEBUG: ${message}`, data || '')
};

export class GeocodingService {
  private readonly baseUrl = GEOCODING_CONFIG.baseUrl;
  private readonly rateLimitDelay = GEOCODING_CONFIG.rateLimitDelay;
  private lastRequestTime = 0;
  
  private readonly headers = {
    'User-Agent': 'PostalTerminalAPI/1.0 (<EMAIL>)', // Replace with actual contact
    'Accept': 'application/json',
    'Accept-Language': 'lt,en'
  };

  async geocodeAddress(
    address: string, 
    context: GeocodingContext = {}
  ): Promise<CoordinatesType | null> {
    try {
      // Rate limiting
      await this.enforceRateLimit();

      const query = this.buildQuery(address, context);
      const url = `${this.baseUrl}?${query}`;

      logger.debug(`🌍 Geocoding: ${address}`);

      const response = await fetch(url, {
        headers: this.headers
        // Note: timeout is not supported in standard fetch, would need AbortController
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const results = await response.json() as NominatimResponse[];

      if (!results || !Array.isArray(results) || results.length === 0) {
        logger.warn(`⚠️  No geocoding results for: ${address}`);
        return null;
      }

      // Take the first (most relevant) result
      const result = results[0];
      if (!result || !result.lat || !result.lon) {
        logger.warn(`⚠️  Invalid result structure from geocoding`);
        return null;
      }

      const coordinates = {
        lat: parseFloat(result.lat),
        lng: parseFloat(result.lon)
      };

      // Validate coordinates
      if (isNaN(coordinates.lat) || isNaN(coordinates.lng)) {
        logger.warn(`⚠️  Invalid coordinates from geocoding: ${result.lat}, ${result.lon}`);
        return null;
      }

      // Validate coordinates are in Lithuania (approximate bounds)
      if (!this.isInLithuania(coordinates)) {
        logger.warn(`⚠️  Geocoded coordinates outside Lithuania: ${coordinates.lat}, ${coordinates.lng}`);
        return null;
      }

      logger.debug(`✅ Geocoded: ${address} -> ${coordinates.lat}, ${coordinates.lng}`);
      return coordinates;

    } catch (error) {
      logger.error(`❌ Geocoding failed for "${address}":`, error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  private buildQuery(address: string, context: GeocodingContext): string {
    const params = new URLSearchParams();
    
    // Build the search query
    let searchQuery = address;
    
    // Add context if available
    if (context.city && !address.toLowerCase().includes(context.city.toLowerCase())) {
      searchQuery += `, ${context.city}`;
    }
    
    if (context.postalCode && !address.includes(context.postalCode)) {
      searchQuery += `, ${context.postalCode}`;
    }
    
    // Always add Lithuania to ensure we get Lithuanian results
    if (!searchQuery.toLowerCase().includes('lithuania') && !searchQuery.toLowerCase().includes('lietuva')) {
      searchQuery += ', Lithuania';
    }

    params.set('q', searchQuery);
    params.set('format', 'json');
    params.set('limit', GEOCODING_RESULT_LIMIT.toString()); // Configurable result limit
    params.set('countrycodes', 'lt'); // Limit to Lithuania
    params.set('addressdetails', '1');
    params.set('extratags', '1');
    params.set('namedetails', '1');

    return params.toString();
  }

  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.rateLimitDelay) {
      const waitTime = this.rateLimitDelay - timeSinceLastRequest;
      logger.debug(`⏳ Rate limiting: waiting ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  private isInLithuania(coordinates: CoordinatesType): boolean {
    // Lithuania approximate bounds
    const bounds = {
      north: 56.5,
      south: 53.8,
      east: 26.9,
      west: 20.9
    };

    return (
      coordinates.lat >= bounds.south &&
      coordinates.lat <= bounds.north &&
      coordinates.lng >= bounds.west &&
      coordinates.lng <= bounds.east
    );
  }

  async batchGeocode(
    addresses: Array<{ address: string; context?: GeocodingContext }>,
    onProgress?: (completed: number, total: number) => void
  ): Promise<Array<{ address: string; coordinates: CoordinatesType | null }>> {
    const results: Array<{ address: string; coordinates: CoordinatesType | null }> = [];
    
    logger.info(`🌍 Starting batch geocoding of ${addresses.length} addresses`);
    
    for (let i = 0; i < addresses.length; i++) {
      const item = addresses[i];
      if (!item) continue;
      const { address, context } = item;
      const coordinates = await this.geocodeAddress(address, context);
      
      results.push({ address, coordinates });
      
      if (onProgress) {
        onProgress(i + 1, addresses.length);
      }
      
      // Log progress every 10 items
      if ((i + 1) % 10 === 0) {
        logger.info(`📍 Geocoded ${i + 1}/${addresses.length} addresses`);
      }
    }
    
    const successful = results.filter(r => r.coordinates !== null).length;
    logger.info(`✅ Batch geocoding complete: ${successful}/${addresses.length} successful`);
    
    return results;
  }

  async reverseGeocode(coordinates: CoordinatesType): Promise<string | null> {
    try {
      await this.enforceRateLimit();

      const params = new URLSearchParams({
        lat: coordinates.lat.toString(),
        lon: coordinates.lng.toString(),
        format: 'json',
        addressdetails: '1',
        zoom: '18'
      });

      const url = `${GEOCODING_CONFIG.baseUrl.replace('/search', '/reverse')}?${params}`;

      const response = await fetch(url, {
        headers: this.headers
        // Note: timeout is not supported in standard fetch, would need AbortController
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json() as { display_name?: string };

      if (result && result.display_name) {
        return result.display_name;
      }

      return null;

    } catch (error) {
      logger.error(`❌ Reverse geocoding failed for ${coordinates.lat}, ${coordinates.lng}:`, error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  // Utility method to validate and normalize coordinates
  normalizeCoordinates(lat: number, lng: number): CoordinatesType | null {
    if (isNaN(lat) || isNaN(lng)) {
      return null;
    }

    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return null;
    }

    // Round to 6 decimal places for ~0.1m precision
    return {
      lat: Math.round(lat * 1000000) / 1000000,
      lng: Math.round(lng * 1000000) / 1000000
    };
  }

  // Get geocoding statistics
  getStats(): { requestCount: number; lastRequestTime: number } {
    return {
      requestCount: 0, // Could be tracked if needed
      lastRequestTime: this.lastRequestTime
    };
  }
}
