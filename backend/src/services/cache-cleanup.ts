import cron from 'node-cron';
import { getDatabasePool } from '../database/connection';
import { FastifyBaseLogger } from 'fastify';
import { CONFIG } from '../config';

/**
 * Schedule daily cleanup of expired api_cache rows.
 * Runs at 03:00 Europe/Vilnius time.
 */
export function startApiCacheCleanup(logger?: FastifyBaseLogger): void {
  const schedule = CONFIG.cache.cleanupSchedule; // Configurable cleanup schedule

  cron.schedule(schedule, async () => {
    try {
      logger?.info('🧹 Starting scheduled api_cache cleanup');
      const pool = getDatabasePool();
      await pool.query('SELECT clean_expired_cache()');
      logger?.info('✅ api_cache cleanup finished');
    } catch (error) {
      logger?.error({ error }, '❌ api_cache cleanup failed');
    }
  }, {
    scheduled: true,
    timezone: 'Europe/Vilnius'
  });
} 