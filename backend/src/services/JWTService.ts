/**
 * JWT Service
 * 
 * Handles JWT token generation, validation, and refresh token management.
 * Implements secure token handling with refresh token rotation.
 */

import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { Pool } from 'pg';
import { getDatabasePool } from '../database/connection';
import { 
  JWTPayload, 
  RefreshToken, 
  UserProfile,
  AuthResponse,
  RefreshTokenResponse 
} from '../types/auth';

export class JWTService {
  private pool: Pool;
  private jwtSecret: string;
  private accessTokenExpiry: string;
  private refreshTokenExpiry: string;

  constructor() {
    this.pool = getDatabasePool();

    // Critical: JWT_SECRET must be set in environment variables
    if (!process.env['JWT_SECRET']) {
      throw new Error(
        'CRITICAL_CONFIG_ERROR: JWT_SECRET environment variable is required for secure authentication. ' +
        'Please set JWT_SECRET in your environment variables with a strong, randomly generated secret key (minimum 32 characters).'
      );
    }

    this.jwtSecret = process.env['JWT_SECRET'];
    this.accessTokenExpiry = process.env['JWT_ACCESS_EXPIRY'] || '15m';
    this.refreshTokenExpiry = process.env['JWT_REFRESH_EXPIRY'] || '7d';
  }

  // =============================================================================
  // TOKEN GENERATION
  // =============================================================================

  /**
   * Generate access token
   */
  generateAccessToken(user: UserProfile): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
      role: user.role,
      jti: crypto.randomUUID()
    };

    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.accessTokenExpiry,
      issuer: 'postal-terminal-api',
      audience: 'postal-terminal-api-users'
    } as any);
  }

  /**
   * Generate refresh token and store in database
   */
  async generateRefreshToken(
    userId: string, 
    userAgent?: string, 
    ipAddress?: string
  ): Promise<string> {
    const token = crypto.randomBytes(64).toString('hex');
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    
    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + this.parseExpiryToMs(this.refreshTokenExpiry));

    const query = `
      INSERT INTO refresh_tokens (
        user_id, token_hash, expires_at, user_agent, ip_address
      )
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id
    `;

    await this.pool.query(query, [
      userId,
      tokenHash,
      expiresAt,
      userAgent,
      ipAddress
    ]);

    return token;
  }

  /**
   * Generate complete auth response with both tokens
   */
  async generateAuthResponse(
    user: UserProfile,
    userAgent?: string,
    ipAddress?: string
  ): Promise<AuthResponse> {
    const accessToken = this.generateAccessToken(user);
    const refreshToken = await this.generateRefreshToken(user.id, userAgent, ipAddress);
    
    return {
      user,
      accessToken,
      refreshToken,
      expiresIn: this.parseExpiryToSeconds(this.accessTokenExpiry)
    };
  }

  // =============================================================================
  // TOKEN VALIDATION
  // =============================================================================

  /**
   * Verify and decode access token
   */
  verifyAccessToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, this.jwtSecret, {
        issuer: 'postal-terminal-api',
        audience: 'postal-terminal-api-users'
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      return null;
    }
  }

  /**
   * Verify refresh token and get associated user
   */
  async verifyRefreshToken(token: string): Promise<RefreshToken | null> {
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    const query = `
      SELECT * FROM refresh_tokens 
      WHERE token_hash = $1 
        AND expires_at > NOW() 
        AND is_revoked = FALSE
    `;

    const result = await this.pool.query(query, [tokenHash]);
    return result.rows[0] || null;
  }

  // =============================================================================
  // TOKEN REFRESH
  // =============================================================================

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(
    refreshToken: string,
    userAgent?: string,
    ipAddress?: string
  ): Promise<RefreshTokenResponse | null> {
    // Verify refresh token
    const tokenData = await this.verifyRefreshToken(refreshToken);
    if (!tokenData) {
      return null;
    }

    // Get user data
    const userQuery = 'SELECT * FROM users WHERE id = $1 AND is_active = TRUE';
    const userResult = await this.pool.query(userQuery, [tokenData.user_id]);
    const user = userResult.rows[0];

    if (!user) {
      // Revoke token if user doesn't exist or is inactive
      await this.revokeRefreshToken(refreshToken, 'User inactive or deleted');
      return null;
    }

    // Update last used timestamp
    await this.updateRefreshTokenUsage(tokenData.id, ipAddress);

    // Generate new tokens (rotate refresh token for security)
    const newAccessToken = this.generateAccessToken({
      id: user.id,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      is_active: user.is_active,
      email_verified: user.email_verified,
      created_at: user.created_at,
      last_login_at: user.last_login_at
    });

    const newRefreshToken = await this.generateRefreshToken(
      user.id, 
      userAgent, 
      ipAddress
    );

    // Revoke old refresh token
    await this.revokeRefreshToken(refreshToken, 'Token rotated');

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      expiresIn: this.parseExpiryToSeconds(this.accessTokenExpiry)
    };
  }

  // =============================================================================
  // TOKEN MANAGEMENT
  // =============================================================================

  /**
   * Revoke refresh token
   */
  async revokeRefreshToken(token: string, reason?: string): Promise<boolean> {
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    const query = `
      UPDATE refresh_tokens 
      SET 
        is_revoked = TRUE,
        revoked_at = NOW(),
        revoked_reason = $2
      WHERE token_hash = $1 AND is_revoked = FALSE
    `;

    const result = await this.pool.query(query, [tokenHash, reason]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Revoke all user tokens (for logout all devices)
   */
  async revokeAllUserTokens(userId: string, reason?: string): Promise<number> {
    const query = `
      UPDATE refresh_tokens 
      SET 
        is_revoked = TRUE,
        revoked_at = NOW(),
        revoked_reason = $2
      WHERE user_id = $1 AND is_revoked = FALSE
    `;

    const result = await this.pool.query(query, [userId, reason]);
    return result.rowCount || 0;
  }

  /**
   * Update refresh token usage
   */
  private async updateRefreshTokenUsage(tokenId: string, ipAddress?: string): Promise<void> {
    const query = `
      UPDATE refresh_tokens 
      SET 
        last_used_at = NOW(),
        ip_address = COALESCE($2, ip_address)
      WHERE id = $1
    `;

    await this.pool.query(query, [tokenId, ipAddress]);
  }

  // =============================================================================
  // TOKEN CLEANUP
  // =============================================================================

  /**
   * Clean expired and revoked tokens
   */
  async cleanupTokens(): Promise<number> {
    const query = `
      DELETE FROM refresh_tokens 
      WHERE expires_at < NOW() OR is_revoked = TRUE
    `;

    const result = await this.pool.query(query);
    return result.rowCount || 0;
  }

  /**
   * Get user's active tokens
   */
  async getUserActiveTokens(userId: string): Promise<RefreshToken[]> {
    const query = `
      SELECT * FROM refresh_tokens 
      WHERE user_id = $1 
        AND expires_at > NOW() 
        AND is_revoked = FALSE
      ORDER BY created_at DESC
    `;

    const result = await this.pool.query(query, [userId]);
    return result.rows;
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Parse expiry string to milliseconds
   */
  private parseExpiryToMs(expiry: string): number {
    const unit = expiry.slice(-1);
    const value = parseInt(expiry.slice(0, -1));

    switch (unit) {
      case 's': return value * 1000;
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: return 15 * 60 * 1000; // Default 15 minutes
    }
  }

  /**
   * Parse expiry string to seconds
   */
  private parseExpiryToSeconds(expiry: string): number {
    return Math.floor(this.parseExpiryToMs(expiry) / 1000);
  }

  /**
   * Extract token from Authorization header
   */
  static extractTokenFromHeader(authHeader?: string): string | null {
    if (!authHeader) return null;

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1] || null;
  }

  /**
   * Get token expiry info
   */
  getTokenInfo(token: string): { valid: boolean; expired: boolean; payload?: JWTPayload } {
    try {
      const decoded = jwt.decode(token) as JWTPayload;
      if (!decoded) {
        return { valid: false, expired: false };
      }

      const now = Math.floor(Date.now() / 1000);
      const expired = decoded.exp < now;

      return {
        valid: !expired,
        expired,
        payload: decoded
      };
    } catch (error) {
      return { valid: false, expired: false };
    }
  }
}
