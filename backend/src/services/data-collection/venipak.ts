import { readFileSync } from 'fs';
import { join } from 'path';
import { TerminalImportType } from '../../validation/schemas';
import { fileDownloader } from '../file-downloader';
import { config } from '../../config';

interface VenipakRawData {
  id: number;
  name: string;
  code: string;
  address: string;
  city: string;
  zip: string;
  country: string;
  terminal: string;
  display_name: string;
  description?: string;
  working_hours: string; // JSON string
  contact_t: string;
  lat: string;
  lng: string;
  pick_up_enabled: number; // 0 or 1
  cod_enabled: number; // 0 or 1
  ldg_enabled: number; // 0 or 1
  size_limit: number;
  type: number; // 1 = pickup point, 3 = locker
  max_height: number;
  max_width: number;
  max_length: number;
}

interface VenipakWorkingHours {
  from_h: string;
  from_m: string;
  to_h: string;
  to_m: string;
  dayOfWeek: number;
  openTime: string;
  closeTime: string;
}

export class VenipakDataCollector {
  private readonly jsonUrl = config.venipakJsonUrl || 'https://go.venipak.lt/ws/get_pickup_points';
  
  private readonly headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'lt-LT,lt;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'cross-origin',
    'Referer': process.env['VENIPAK_REFERER_URL'] || 'https://venipak.lt/'
  };

  async fetchData(): Promise<TerminalImportType[]> {
    console.log('🔄 Fetching Venipak terminal data...');

    try {
      // Download file first
      const filePath = join(process.cwd(), 'data', 'downloads', 'venipak-terminals.json');

      const downloadResult = await fileDownloader.downloadFile({
        url: this.jsonUrl,
        filePath,
        headers: this.headers,
        timeout: 60000
      });

      if (!downloadResult.success) {
        throw new Error(`Download failed: ${downloadResult.error}`);
      }

      console.log(`📄 Downloaded ${downloadResult.fileSize} bytes to ${filePath}`);

      // Read and parse the downloaded file
      const jsonData = JSON.parse(readFileSync(filePath, 'utf-8'));
      console.log(`📄 Parsed JSON data with ${Array.isArray(jsonData) ? jsonData.length : 'unknown'} items`);

      if (!Array.isArray(jsonData)) {
        throw new Error('Invalid JSON data format - expected array');
      }
      
      return this.parseJSONData(jsonData);
    } catch (error) {
      console.error('❌ Failed to fetch Venipak data:', error);
      throw new Error(`Venipak data collection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async parseJSONData(jsonData: VenipakRawData[]): Promise<TerminalImportType[]> {
    if (!Array.isArray(jsonData)) {
      throw new Error('Invalid JSON data format - expected array');
    }

    const terminals: TerminalImportType[] = [];
    
    for (const record of jsonData) {
      try {
        // Filter for Lithuanian terminals only
        if (record.country !== 'LT') {
          continue;
        }

        const terminal = this.transformRecord(record);
        if (terminal) {
          terminals.push(terminal);
        }
      } catch (error) {
        console.warn(`⚠️  Skipping invalid Venipak record:`, error instanceof Error ? error.message : 'Unknown error', record);
      }
    }

    console.log(`✅ Parsed ${terminals.length} Venipak terminals`);
    return terminals;
  }

  private transformRecord(record: VenipakRawData): TerminalImportType | null {
    // Validate required fields
    if (!record.id || !record.name || !record.city || !record.address) {
      throw new Error('Missing required fields');
    }

    // Parse coordinates (they come as strings from JSON)
    const lat = parseFloat(record.lat);
    const lng = parseFloat(record.lng);

    if (isNaN(lat) || isNaN(lng)) {
      throw new Error('Invalid coordinates');
    }

    // Validate coordinate ranges for Lithuania
    if (lat < 53.8 || lat > 56.5 || lng < 20.9 || lng > 26.9) {
      console.warn(`⚠️  Coordinates outside Lithuania range for terminal ${record.id}: lat=${lat}, lng=${lng}`);
      // Continue anyway as there might be border terminals
    }

    // Clean and standardize postal code
    const postalCode = this.standardizePostalCode(record.zip);

    // Determine terminal type based on TYPE field
    const terminalType = this.determineTerminalType(record.type);

    // Parse working hours
    const parsedWorkingHours = this.parseWorkingHours(record.working_hours);

    // Build comprehensive metadata
    const metadata = {
      originalId: record.id,
      code: record.code,
      terminal: record.terminal,
      displayName: record.display_name,
      description: record.description || '',
      contactPhone: record.contact_t || '',
      
      // Service capabilities
      pickupEnabled: record.pick_up_enabled === 1,
      codEnabled: record.cod_enabled === 1,
      ldgEnabled: record.ldg_enabled === 1,
      
      // Physical dimensions
      sizeLimit: record.size_limit,
      maxHeight: record.max_height,
      maxWidth: record.max_width,
      maxLength: record.max_length,
      
      // Working hours
      workingHours: record.working_hours,
      parsedWorkingHours: parsedWorkingHours,
      
      // Source metadata
      source: 'venipak_api',
      lastUpdated: new Date().toISOString()
    };

    return {
      id: `venipak_${record.id}`,
      name: this.cleanText(record.name),
      city: this.cleanText(record.city),
      address: this.cleanText(record.address),
      postalCode,
      coordinates: { lat, lng },
      provider: 'VENIPAK',
      terminalType,
      metadata
    };
  }

  private determineTerminalType(type: number): 'PARCEL_LOCKER' | 'PICKUP_POINT' | 'POST_OFFICE' {
    // Based on Venipak's type classification
    switch (type) {
      case 1: // Pickup point
        return 'PICKUP_POINT';
      case 3: // Parcel locker/automated terminal
        return 'PARCEL_LOCKER';
      default:
        return 'PICKUP_POINT'; // Default to pickup point
    }
  }

  private parseWorkingHours(workingHoursStr: string): VenipakWorkingHours[] | null {
    try {
      if (!workingHoursStr || workingHoursStr.trim() === '') {
        return null;
      }
      
      const parsed = JSON.parse(workingHoursStr);
      if (Array.isArray(parsed)) {
        return parsed as VenipakWorkingHours[];
      }
      
      return null;
    } catch (error) {
      console.warn(`⚠️  Failed to parse working hours: ${workingHoursStr}`);
      return null;
    }
  }

  private standardizePostalCode(postalCode: string): string | undefined {
    if (!postalCode) return undefined;

    // Remove any non-numeric characters
    let cleaned = postalCode.replace(/[^0-9]/g, '');

    // Lithuanian postal codes are 5 digits
    if (cleaned.length > 0 && cleaned.length <= 5) {
      cleaned = cleaned.padStart(5, '0');
    }

    // Validate length
    if (cleaned.length !== 5) {
      console.warn(`⚠️  Invalid postal code length: ${postalCode} -> ${cleaned}`);
      return undefined;
    }

    return cleaned;
  }

  private cleanText(text: string): string {
    if (!text) return '';
    
    return text
      .trim()
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/[\r\n\t]/g, ' ') // Remove line breaks and tabs
      .substring(0, 500); // Limit length for database constraints
  }

  async validateData(terminals: TerminalImportType[]): Promise<{
    valid: TerminalImportType[];
    invalid: Array<{ terminal: any; errors: string[] }>;
  }> {
    const valid: TerminalImportType[] = [];
    const invalid: Array<{ terminal: any; errors: string[] }> = [];

    for (const terminal of terminals) {
      const errors: string[] = [];

      // Validate required fields
      if (!terminal.id) errors.push('Missing ID');
      if (!terminal.name) errors.push('Missing name');
      if (!terminal.city) errors.push('Missing city');
      if (!terminal.address) errors.push('Missing address');
      if (!terminal.coordinates?.lat || !terminal.coordinates?.lng) {
        errors.push('Missing coordinates');
      }

      // Validate coordinate ranges
      if (terminal.coordinates) {
        const { lat, lng } = terminal.coordinates;
        if (lat < -90 || lat > 90) errors.push('Invalid latitude');
        if (lng < -180 || lng > 180) errors.push('Invalid longitude');
      }

      // Validate postal code format if present
      if (terminal.postalCode && !/^\d{5}$/.test(terminal.postalCode)) {
        errors.push('Invalid postal code format');
      }

      // Validate provider
      if (terminal.provider !== 'VENIPAK') {
        errors.push('Invalid provider');
      }

      // Validate terminal type
      if (!['PARCEL_LOCKER', 'PICKUP_POINT', 'POST_OFFICE'].includes(terminal.terminalType)) {
        errors.push('Invalid terminal type');
      }

      if (errors.length === 0) {
        valid.push(terminal);
      } else {
        invalid.push({ terminal, errors });
      }
    }

    console.log(`✅ Validation complete: ${valid.length} valid, ${invalid.length} invalid terminals`);
    
    if (invalid.length > 0) {
      console.log('❌ Validation errors found:');
      invalid.slice(0, 5).forEach(({ terminal, errors }, index) => {
        console.log(`  ${index + 1}. Terminal ${terminal.id || 'N/A'}: ${errors.join(', ')}`);
      });
      if (invalid.length > 5) {
        console.log(`  ... and ${invalid.length - 5} more`);
      }
    }

    return { valid, invalid };
  }

  async collectAndValidate(): Promise<TerminalImportType[]> {
    console.log('🚀 Starting Venipak data collection and validation...');
    
    try {
      // Fetch raw data
      const rawTerminals = await this.fetchData();
      
      if (rawTerminals.length === 0) {
        console.warn('⚠️  No Venipak terminals fetched');
        return [];
      }

      // Validate data
      const { valid, invalid } = await this.validateData(rawTerminals);

      if (invalid.length > 0) {
        const errorRate = (invalid.length / (valid.length + invalid.length)) * 100;
        console.warn(`⚠️  ${invalid.length} terminals failed validation (${errorRate.toFixed(1)}% error rate)`);
        
        if (errorRate > 20) {
          throw new Error(`High validation error rate: ${errorRate.toFixed(1)}%. Please check data source.`);
        }
      }

      console.log(`✅ Venipak data collection completed: ${valid.length} valid terminals`);
      return valid;

    } catch (error) {
      console.error('❌ Venipak data collection failed:', error);
      throw error;
    }
  }
}