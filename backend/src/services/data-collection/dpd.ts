import * as ExcelJS from 'exceljs';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { TerminalImportType } from '../../validation/schemas';
import { fileDownloader } from '../file-downloader';
import { config, GEOCODING_CONFIG } from '../../config';

interface DPDRawData {
  'ID': string; // Terminal ID
  'Miestas': string; // City
  'Adresas': string; // Address
  'Pašto kodas': number | string; // Postal code
  'Pavadinimas': string; // Terminal name
  'Paštomato vieta': string; // Location details
  'Siuntų surinkimo laikas': number; // Pickup time
  'Darbo laikas': string; // Working hours
}

interface CachedGeocodingData {
  [terminalId: string]: {
    lat: number;
    lng: number;
    geocoded: boolean;
    address: string;
    city: string;
    cachedAt: string;
  };
}

export class DPDDataCollector {
  private readonly excelUrl = config.dpdExcelUrl;
  private readonly geocodingCachePath = join(process.cwd(), 'data', 'dpd-geocoding-cache.json');
  private geocodingCache: CachedGeocodingData = {};

  
  private readonly headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/octet-stream,*/*',
    'Accept-Language': 'lt-LT,lt;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
    'Referer': process.env['DPD_REFERER_URL'] || 'https://www.dpd.com/lt/private/send_parcel/pickup_locations'
  };

  constructor() {
    this.loadGeocodingCache();
  }

  /**
   * Load cached geocoding data from file
   */
  private loadGeocodingCache(): void {
    try {
      if (existsSync(this.geocodingCachePath)) {
        const cacheData = readFileSync(this.geocodingCachePath, 'utf-8');
        this.geocodingCache = JSON.parse(cacheData);
        console.log(`📋 Loaded ${Object.keys(this.geocodingCache).length} cached DPD geocoding entries`);
      } else {
        console.log('📋 No DPD geocoding cache found - will use placeholder coordinates');
      }
    } catch (error) {
      console.warn('⚠️  Failed to load DPD geocoding cache:', error instanceof Error ? error.message : 'Unknown error');
      this.geocodingCache = {};
    }
  }

  /**
   * Get coordinates from cache or return placeholder
   */
  private getCoordinatesFromCache(terminalId: string): { coordinates: { lat: number; lng: number }; geocoded: boolean } {
    const cached = this.geocodingCache[terminalId];
    
    if (cached && cached.geocoded) {
      return {
        coordinates: { lat: cached.lat, lng: cached.lng },
        geocoded: true
      };
    }
    
    // Return placeholder coordinates (center of Lithuania) for fast sync
    // Run 'npm run preprocess-dpd' to geocode all terminals
    return {
      coordinates: { lat: 55.1694, lng: 23.8813 },
      geocoded: false
    };
  }

  /**
   * Check if geocoding cache needs refresh based on downloaded file age
   */
  private shouldRefreshGeocodingCache(downloadResult: { fromCache: boolean }): boolean {
    // If we just downloaded a fresh file (not from cache), 
    // we should check if geocoding cache needs updating
    if (!downloadResult.fromCache) {
      console.log('📁 Fresh file downloaded - checking geocoding cache freshness...');
      return true;
    }
    return false;
  }

  /**
   * Smart preprocessing: only geocode terminals that need it during sync
   * This is faster than full preprocessing but ensures critical updates
   */
  private async smartPreprocessGeocodingCache(terminals: DPDRawData[]): Promise<void> {
    try {
      console.log('🧠 Running smart geocoding preprocessing...');
      
      // Find terminals that need geocoding (new or address changed)
      const terminalsToGeocode = terminals.filter(terminal => {
        const terminalId = terminal['ID'];
        const cached = this.geocodingCache[terminalId];
        
        // Need geocoding if: no cache, failed geocoding, or address changed
        if (!cached || !cached.geocoded) {
          return true;
        }
        
        const currentAddress = terminal['Adresas'];
        const currentCity = terminal['Miestas'];
        const addressChanged = cached.address !== currentAddress || cached.city !== currentCity;
        
        return addressChanged;
      });

      if (terminalsToGeocode.length === 0) {
        console.log('✅ No terminals need geocoding updates');
        return;
      }

      console.log(`🌍 Smart geocoding ${terminalsToGeocode.length} terminals during sync...`);
      
      let successCount = 0;
      let failureCount = 0;

      // Geocode only critical terminals (limit configurable via env var)
      const smartLimit = config.geocoding.smartGeocodingLimit;
      const limitedTerminals = terminalsToGeocode.slice(0, smartLimit);
      if (limitedTerminals.length < terminalsToGeocode.length) {
        console.log(`⚡ Limiting to ${limitedTerminals.length} terminals during sync (${terminalsToGeocode.length - limitedTerminals.length} remaining for manual preprocessing)`);
      }

      for (let i = 0; i < limitedTerminals.length; i++) {
        const terminal = limitedTerminals[i];
        if (!terminal) continue;
        
        const terminalId = terminal['ID'];
        
        try {
          const coordinates = await this.simpleGeocode(terminal['Adresas'], terminal['Miestas']);
          
          if (coordinates) {
            this.geocodingCache[terminalId] = {
              lat: coordinates.lat,
              lng: coordinates.lng,
              geocoded: true,
              address: terminal['Adresas'],
              city: terminal['Miestas'],
              cachedAt: new Date().toISOString()
            };
            successCount++;
          } else {
            // Save failed attempt
            this.geocodingCache[terminalId] = {
              lat: 0,
              lng: 0,
              geocoded: false,
              address: terminal['Adresas'],
              city: terminal['Miestas'],
              cachedAt: new Date().toISOString()
            };
            failureCount++;
          }

          // Rate limiting: configurable delay during sync
          if (i < limitedTerminals.length - 1) {
            await new Promise(resolve => setTimeout(resolve, config.geocoding.smartGeocodingRateDelay));
          }

        } catch (error) {
          console.warn(`⚠️  Failed to geocode ${terminalId} during smart preprocessing`);
          failureCount++;
        }
      }

      // Save updated cache
      await this.saveGeocodingCache();

      console.log(`🎯 Smart geocoding completed: ${successCount} success, ${failureCount} failed`);
      
      if (terminalsToGeocode.length > limitedTerminals.length) {
        console.log(`💡 Run 'npm run preprocess-dpd' to geocode remaining ${terminalsToGeocode.length - limitedTerminals.length} terminals`);
      }

    } catch (error) {
      console.warn('⚠️  Smart preprocessing failed:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Preprocess DPD data by building geocoding cache for all terminals
   * This integrates the functionality that was previously in the separate script
   */
  async preprocessGeocodingCache(filePath?: string): Promise<void> {
    console.log('🌍 Starting DPD geocoding preprocessing...');
    
    try {
      let excelFilePath = filePath;
      
      // If no file path provided, download the file first
      if (!excelFilePath) {
        excelFilePath = join(process.cwd(), 'data', 'downloads', 'dpd-terminals.xlsx');
        
        const downloadResult = await fileDownloader.downloadFile({
          url: this.excelUrl,
          filePath: excelFilePath,
          headers: this.headers,
          timeout: 60000
        });

        if (!downloadResult.success) {
          throw new Error(`Download failed: ${downloadResult.error}`);
        }
        
        console.log(`📄 Downloaded ${downloadResult.fileSize} bytes to ${excelFilePath}`);
      }

      // Parse Excel file to get terminal data using ExcelJS
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(excelFilePath);

      const worksheet = workbook.getWorksheet(1); // Get first worksheet
      if (!worksheet) {
        throw new Error('No worksheets found in Excel file');
      }

      // Convert worksheet to JSON format
      const jsonData: DPDRawData[] = [];
      const headerRow = worksheet.getRow(1);
      const headers: string[] = [];

      // Extract headers
      headerRow.eachCell((cell, colNumber) => {
        headers[colNumber] = cell.text || '';
      });

      // Extract data rows
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) return; // Skip header row

        const rowData: any = {};
        row.eachCell((cell, colNumber) => {
          const header = headers[colNumber];
          if (header) {
            rowData[header] = cell.text || '';
          }
        });

        // Only add rows with required data
        if (rowData['ID'] && rowData['Miestas'] && rowData['Adresas'] && rowData['Pavadinimas']) {
          jsonData.push(rowData as DPDRawData);
        }
      });
      
      const validTerminals = jsonData.filter(record => 
        record && record['ID'] && record['Miestas'] && record['Adresas'] && record['Pavadinimas']
      );

      console.log(`📍 Found ${validTerminals.length} terminals in Excel file`);

      // Filter terminals that need geocoding (new terminals OR address changes)
      const terminalsToGeocode = validTerminals.filter(terminal => {
        const terminalId = terminal?.['ID'];
        if (!terminalId) return false;
        
        const cached = this.geocodingCache[terminalId as string];
        
        // Need geocoding if: no cache, failed geocoding, or address changed
        if (!cached || !cached.geocoded) {
          return true;
        }
        
        // Check if address or city changed
        const currentAddress = terminal['Adresas'];
        const currentCity = terminal['Miestas'];
        const addressChanged = cached.address !== currentAddress || cached.city !== currentCity;
        
        if (addressChanged) {
          console.log(`📍 Address changed for ${terminalId}: "${cached.address}, ${cached.city}" → "${currentAddress}, ${currentCity}"`);
          return true;
        }
        
        return false;
      });

      if (terminalsToGeocode.length === 0) {
        console.log('✅ All terminals already geocoded with current addresses!');
        return;
      }

      // Count new vs changed terminals
      const newTerminals = terminalsToGeocode.filter(t => {
        const cached = this.geocodingCache[t['ID']];
        return !cached || !cached.geocoded;
      }).length;
      const changedTerminals = terminalsToGeocode.length - newTerminals;

      console.log(`🌍 Geocoding ${terminalsToGeocode.length} terminals:`);
      if (newTerminals > 0) console.log(`   📍 ${newTerminals} new terminals`);
      if (changedTerminals > 0) console.log(`   🔄 ${changedTerminals} terminals with address changes`);
      
      let successCount = 0;
      let failureCount = 0;
      const startTime = Date.now();

      // Geocode all terminals (no limit for preprocessing)
      for (let i = 0; i < terminalsToGeocode.length; i++) {
        const terminal = terminalsToGeocode[i];
        if (!terminal) continue;
        
        const terminalId = terminal['ID'];
        
        try {
          console.log(`[${i + 1}/${terminalsToGeocode.length}] Geocoding ${terminal['Pavadinimas']} (${terminalId})`);
          
          const coordinates = await this.simpleGeocode(terminal['Adresas'], terminal['Miestas']);
          
          if (coordinates) {
            this.geocodingCache[terminalId] = {
              lat: coordinates.lat,
              lng: coordinates.lng,
              geocoded: true,
              address: terminal['Adresas'],
              city: terminal['Miestas'],
              cachedAt: new Date().toISOString()
            };
            successCount++;
          } else {
            // Save failed attempt to avoid retrying immediately
            this.geocodingCache[terminalId] = {
              lat: 0,
              lng: 0,
              geocoded: false,
              address: terminal['Adresas'],
              city: terminal['Miestas'],
              cachedAt: new Date().toISOString()
            };
            failureCount++;
          }

          // Rate limiting: use configured delay for full preprocessing
          if (i < terminalsToGeocode.length - 1) {
            await new Promise(resolve => setTimeout(resolve, config.geocoding.rateLimitDelay));
          }

        } catch (error) {
          console.warn(`⚠️  Failed to geocode ${terminalId}:`, error instanceof Error ? error.message : 'Unknown error');
          failureCount++;
        }
      }

      // Save updated cache
      await this.saveGeocodingCache();

      const totalTime = ((Date.now() - startTime) / 1000 / 60).toFixed(1);
      console.log(`🎉 Geocoding preprocessing completed in ${totalTime} minutes!`);
      console.log(`✅ Successfully geocoded: ${successCount} terminals`);
      console.log(`❌ Failed to geocode: ${failureCount} terminals`);
      
      const totalCached = Object.keys(this.geocodingCache).length;
      const geocodedCached = Object.values(this.geocodingCache).filter(c => c.geocoded).length;
      console.log(`📊 Cache now contains ${totalCached} terminals (${geocodedCached} successfully geocoded)`);

    } catch (error) {
      console.error('❌ Failed to preprocess geocoding cache:', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  /**
   * Simple geocoding using Nominatim API
   */
  private async simpleGeocode(address: string, city: string): Promise<{ lat: number; lng: number } | null> {
    try {
      const searchQuery = `${address}, ${city}, Lithuania`;
      const params = new URLSearchParams({
        q: searchQuery,
        format: 'json',
        limit: '1',
        countrycodes: 'lt'
      });

      const url = `${GEOCODING_CONFIG.baseUrl}?${params.toString()}`;
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'PostalTerminalAPI/1.0 (auto-geocoding)',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

             const results = await response.json() as Array<{ lat: string; lon: string }>;
       
       if (!results || results.length === 0) {
         return null;
       }

       const result = results[0];
       if (!result) {
         return null;
       }
       
       const coordinates = {
         lat: parseFloat(result.lat),
         lng: parseFloat(result.lon)
       };

      // Validate coordinates are in Lithuania
      if (coordinates.lat >= 53.8 && coordinates.lat <= 56.5 && 
          coordinates.lng >= 20.9 && coordinates.lng <= 26.9) {
        return coordinates;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Save geocoding cache to file
   */
  private async saveGeocodingCache(): Promise<void> {
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      // Ensure data directory exists
      const dataDir = path.dirname(this.geocodingCachePath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      fs.writeFileSync(this.geocodingCachePath, JSON.stringify(this.geocodingCache, null, 2));
      console.log(`💾 Saved ${Object.keys(this.geocodingCache).length} entries to geocoding cache`);
    } catch (error) {
      console.warn('⚠️  Failed to save geocoding cache:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async fetchData(): Promise<TerminalImportType[]> {
    console.log('🔄 Fetching DPD terminal data...');

    try {
      // Download file first
      const filePath = join(process.cwd(), 'data', 'downloads', 'dpd-terminals.xlsx');

      const downloadResult = await fileDownloader.downloadFile({
        url: this.excelUrl,
        filePath,
        headers: this.headers,
        timeout: 60000
      });

      if (!downloadResult.success) {
        throw new Error(`Download failed: ${downloadResult.error}`);
      }

      console.log(`📄 Downloaded ${downloadResult.fileSize} bytes to ${filePath}`);

      // Read and parse the downloaded file
      const arrayBuffer = readFileSync(filePath);
      console.log(`📄 Read ${arrayBuffer.byteLength} bytes from Excel file`);

      // Check if we should run smart preprocessing due to fresh file download
      if (this.shouldRefreshGeocodingCache(downloadResult)) {
        try {
          // Parse raw data first to check for geocoding needs
          const workbook = new ExcelJS.Workbook();
          await workbook.xlsx.load(arrayBuffer);
          const worksheet = workbook.getWorksheet(1);

          if (worksheet) {
            const jsonData: DPDRawData[] = [];
            const headerRow = worksheet.getRow(1);
            const headers: string[] = [];

            // Extract headers
            headerRow.eachCell((cell, colNumber) => {
              headers[colNumber] = cell.text || '';
            });

            // Extract data rows
            worksheet.eachRow((row, rowNumber) => {
              if (rowNumber === 1) return; // Skip header row

              const rowData: any = {};
              row.eachCell((cell, colNumber) => {
                const header = headers[colNumber];
                if (header) {
                  rowData[header] = cell.text || '';
                }
              });

              // Only add rows with required data
              if (rowData['ID'] && rowData['Miestas'] && rowData['Adresas'] && rowData['Pavadinimas']) {
                jsonData.push(rowData as DPDRawData);
              }
            });
            
            // Run smart preprocessing on fresh data
            await this.smartPreprocessGeocodingCache(jsonData);
          }
        } catch (error) {
          console.warn('⚠️  Smart preprocessing failed, continuing with regular parsing:', error instanceof Error ? error.message : 'Unknown error');
        }
      }

      return this.parseExcelData(arrayBuffer);
    } catch (error) {
      console.error('❌ Failed to fetch DPD data:', error);
      throw new Error(`DPD data collection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async parseExcelData(arrayBuffer: ArrayBuffer): Promise<TerminalImportType[]> {
    try {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(arrayBuffer);

      const worksheet = workbook.getWorksheet(1);
      if (!worksheet) {
        throw new Error('No worksheets found in Excel file');
      }

      // Convert to JSON with column headers
      const jsonData: DPDRawData[] = [];
      const headerRow = worksheet.getRow(1);
      const headers: string[] = [];

      // Extract headers
      headerRow.eachCell((cell, colNumber) => {
        headers[colNumber] = cell.text || '';
      });

      // Extract data rows
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) return; // Skip header row

        const rowData: any = {};
        row.eachCell((cell, colNumber) => {
          const header = headers[colNumber];
          if (header) {
            rowData[header] = cell.text || '';
          }
        });

        // Only add rows with required data
        if (rowData['ID'] && rowData['Miestas'] && rowData['Adresas'] && rowData['Pavadinimas']) {
          jsonData.push(rowData as DPDRawData);
        }
      });

      console.log(`📊 Found ${jsonData.length} rows in Excel file`);

      const terminals: TerminalImportType[] = [];
      let geocodedCount = 0;
      let placeholderCount = 0;

      for (let i = 0; i < jsonData.length; i++) {
        try {
          const record = jsonData[i];
          if (record && this.isValidRecord(record)) {
            const terminal = this.transformRecord(record);
            if (terminal) {
              terminals.push(terminal);
              if (terminal.metadata && terminal.metadata['geocoded']) {
                geocodedCount++;
              } else {
                placeholderCount++;
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️  Skipping invalid DPD record at row ${i + 2}:`, error instanceof Error ? error.message : 'Unknown error');
        }
      }

      console.log(`✅ Parsed ${terminals.length} DPD terminals (${geocodedCount} geocoded, ${placeholderCount} using placeholders)`);
      
      if (placeholderCount > 0) {
        console.log(`💡 ${placeholderCount} terminals using placeholder coordinates (center of Lithuania)`);
        console.log(`📍 To get accurate coordinates, run: npm run preprocess-dpd`);
        
        if (!existsSync(this.geocodingCachePath)) {
          console.log(`📋 No geocoding cache found - all terminals will use placeholder coordinates`);
        } else {
          const totalCached = Object.keys(this.geocodingCache).length;
          const successfullyCached = Object.values(this.geocodingCache).filter(c => c.geocoded).length;
          console.log(`📋 Cache: ${successfullyCached}/${totalCached} terminals successfully geocoded`);
        }
      }
      
      return terminals;
    } catch (error) {
      console.error('❌ Excel parsing error:', error);
      throw new Error(`Excel parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private isValidRecord(record: DPDRawData): boolean {
    // Check if record has required fields
    return !!(
      record &&
      record['ID'] &&
      record['Miestas'] &&
      record['Adresas'] &&
      record['Pavadinimas']
    );
  }

  private transformRecord(record: DPDRawData): TerminalImportType | null {
    // Validate required fields
    if (!record['Miestas'] || !record['Adresas'] || !record['Pavadinimas']) {
      throw new Error('Missing required fields');
    }

    const terminalId = record['ID']; // Use the actual terminal ID from Excel
    const city = record['Miestas'];
    const address = record['Adresas'];
    const name = record['Pavadinimas'];
    const locationDetails = record['Paštomato vieta'] || '';

    // Standardize postal code (preserve leading zeros)
    const postalCode = this.standardizePostalCode(String(record['Pašto kodas'] || ''));

    // Get coordinates from cache or use placeholder
    const { coordinates, geocoded } = this.getCoordinatesFromCache(terminalId);

    return {
      id: `dpd_${terminalId}`, // Add dpd_ prefix for consistency with other providers
      name: this.cleanText(name),
      city: this.cleanText(city),
      address: this.cleanText(address),
      postalCode,
      coordinates,
      provider: 'DPD',
      terminalType: 'PARCEL_LOCKER', // DPD uses parcel lockers
      metadata: {
        originalId: terminalId, // Store original ID for reference
        originalPostalCode: record['Pašto kodas'],
        locationDetails: this.cleanText(locationDetails),
        workingHours: record['Darbo laikas'],
        geocoded,
        needsGeocoding: !geocoded,
        source: 'dpd_excel'
      }
    };
  }

  private cleanText(text: string): string {
    if (!text) return '';
    
    return text
      // Remove all control characters (including \r, \n, \t, \v, \f, etc.)
      .replace(/[\x00-\x1F\x7F-\x9F]/g, ' ')
      // Remove any remaining line separators and paragraph separators
      .replace(/[\u2028\u2029]/g, ' ')
      // Replace multiple spaces with single space
      .replace(/\s+/g, ' ')
      .trim();
  }

  private standardizePostalCode(postalCode: string): string | undefined {
    if (!postalCode) return undefined;

    let cleaned = postalCode.trim();

    // Remove country prefix for DPD data
    if (cleaned.match(/^LT-?\d+/i)) {
      cleaned = cleaned.replace(/^LT-?/i, '');
    }

    // Remove any remaining non-numeric characters
    cleaned = cleaned.replace(/[^0-9]/g, '');

    // Pad with leading zeros to ensure 5 digits (preserve leading zeros)
    if (cleaned.length > 0 && cleaned.length <= 5) {
      cleaned = cleaned.padStart(5, '0');
    }

    // Validate length (Lithuanian postal codes are 5 digits)
    if (cleaned.length === 5 && /^\d{5}$/.test(cleaned)) {
      return cleaned;
    }

    return undefined;
  }

  async validateData(terminals: TerminalImportType[]): Promise<{
    valid: TerminalImportType[];
    invalid: Array<{ terminal: any; errors: string[] }>;
  }> {
    const valid: TerminalImportType[] = [];
    const invalid: Array<{ terminal: any; errors: string[] }> = [];

    for (const terminal of terminals) {
      const errors: string[] = [];

      // Skip coordinate validation for DPD terminals without geocoding (lat=0, lng=0)
      if (terminal.coordinates.lat !== 0 || terminal.coordinates.lng !== 0) {
        // Validate coordinates are in Lithuania (approximate bounds)
        if (terminal.coordinates.lat < 53.8 || terminal.coordinates.lat > 56.5) {
          errors.push('Latitude outside Lithuania bounds');
        }
        if (terminal.coordinates.lng < 20.9 || terminal.coordinates.lng > 26.9) {
          errors.push('Longitude outside Lithuania bounds');
        }
      }

      // Validate postal code format
      if (terminal.postalCode && !/^\d{5}$/.test(terminal.postalCode)) {
        errors.push('Invalid postal code format');
      }

      // Validate required string fields
      if (!terminal.name || terminal.name.length < 2) {
        errors.push('Invalid terminal name');
      }
      if (!terminal.city || terminal.city.length < 2) {
        errors.push('Invalid city name');
      }
      if (!terminal.address || terminal.address.length < 5) {
        errors.push('Invalid address');
      }

      if (errors.length === 0) {
        valid.push(terminal);
      } else {
        invalid.push({ terminal, errors });
      }
    }

    console.log(`✅ Validation complete: ${valid.length} valid, ${invalid.length} invalid terminals`);
    
    if (invalid.length > 0) {
      console.warn('⚠️  Invalid terminals found:', invalid.slice(0, 5)); // Log first 5 for debugging
    }

    return { valid, invalid };
  }

  async collectAndValidate(): Promise<TerminalImportType[]> {
    const rawData = await this.fetchData();
    const { valid, invalid } = await this.validateData(rawData);
    
    if (invalid.length > 0) {
      console.warn(`⚠️  ${invalid.length} terminals failed validation and will be skipped`);
    }
    
    return valid;
  }
}
