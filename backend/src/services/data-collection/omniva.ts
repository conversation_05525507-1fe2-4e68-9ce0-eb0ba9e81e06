import { readFileSync } from 'fs';
import { join } from 'path';
import { TerminalImportType } from '../../validation/schemas';
import { fileDownloader } from '../file-downloader';
import { config } from '../../config';

interface OmnivaRawData {
  ZIP: string;
  NAME: string;
  TYPE: number;
  A0_NAME: string; // Country
  A1_NAME: string; // Region/County
  A2_NAME: string; // City
  A3_NAME: string; // District
  A5_NAME: string; // Street
  A7_NAME: string; // House number
  A8_NAME: string; // Additional address info
  X_COORDINATE: string;
  Y_COORDINATE: string;
  SERVICE_HOURS?: string;
  TEMP_SERVICE_HOURS?: string;
  MODIFIED: string;
}

export class OmnivaDataCollector {
  private readonly jsonUrl = config.omnivaJsonUrl;
  
  private readonly headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'lt-LT,lt;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'Referer': process.env['OMNIVA_REFERER_URL'] || 'https://www.omniva.lt/private/send/parcel_terminals'
  };

  async fetchData(): Promise<TerminalImportType[]> {
    console.log('🔄 Fetching Omniva terminal data...');

    try {
      // Download file first
      const filePath = join(process.cwd(), 'data', 'downloads', 'omniva-terminals.json');

      const downloadResult = await fileDownloader.downloadFile({
        url: this.jsonUrl,
        filePath,
        headers: this.headers,
        timeout: 60000
      });

      if (!downloadResult.success) {
        throw new Error(`Download failed: ${downloadResult.error}`);
      }

      console.log(`📄 Downloaded ${downloadResult.fileSize} bytes to ${filePath}`);

      // Read and parse the downloaded file
      const jsonData = JSON.parse(readFileSync(filePath, 'utf-8'));
      console.log(`📄 Parsed JSON data with ${Array.isArray(jsonData) ? jsonData.length : 'unknown'} items`);

      if (!Array.isArray(jsonData)) {
        throw new Error('Invalid JSON data format - expected array');
      }
      return this.parseJSONData(jsonData);
    } catch (error) {
      console.error('❌ Failed to fetch Omniva data:', error);
      throw new Error(`Omniva data collection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async parseJSONData(jsonData: any[]): Promise<TerminalImportType[]> {
    if (!Array.isArray(jsonData)) {
      throw new Error('Invalid JSON data format - expected array');
    }

    const terminals: TerminalImportType[] = [];
    
    for (const record of jsonData) {
      try {
        // Filter for Lithuanian terminals only
        if (record.A0_NAME !== 'LT') {
          continue;
        }

        const terminal = this.transformRecord(record);
        if (terminal) {
          terminals.push(terminal);
        }
      } catch (error) {
        console.warn(`⚠️  Skipping invalid Omniva record:`, error instanceof Error ? error.message : 'Unknown error', record);
      }
    }

    console.log(`✅ Parsed ${terminals.length} Omniva terminals`);
    return terminals;
  }

  private transformRecord(record: OmnivaRawData): TerminalImportType | null {
    // Validate required fields
    if (!record.ZIP || !record.NAME || !record.A2_NAME) {
      throw new Error('Missing required fields');
    }

    // Parse coordinates (they come as strings from JSON)
    const lat = parseFloat(record.Y_COORDINATE);
    const lng = parseFloat(record.X_COORDINATE);

    if (isNaN(lat) || isNaN(lng)) {
      throw new Error('Invalid coordinates');
    }

    // Validate coordinate ranges
    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      throw new Error('Coordinates out of range');
    }

    // Build address from components
    const address = this.buildAddress(record);
    
    // Standardize postal code
    const postalCode = this.standardizePostalCode(record.ZIP);

    // Determine terminal type based on TYPE field
    const terminalType = this.determineTerminalType(record.TYPE);

    return {
      id: `omniva_${record.ZIP}`,
      name: record.NAME.trim(),
      city: record.A2_NAME.trim(),
      address: address.trim(),
      postalCode,
      coordinates: { lat, lng },
      provider: 'OMNIVA',
      terminalType,
      metadata: {
        originalZip: record.ZIP,
        type: record.TYPE,
        region: record.A1_NAME,
        district: record.A3_NAME,
        serviceHours: record.SERVICE_HOURS,
        tempServiceHours: record.TEMP_SERVICE_HOURS,
        lastModified: record.MODIFIED,
        source: 'omniva_json'
      }
    };
  }

  private buildAddress(record: OmnivaRawData): string {
    const parts: string[] = [];
    
    // Add street name
    if (record.A5_NAME) {
      parts.push(record.A5_NAME.trim());
    }
    
    // Add house number
    if (record.A7_NAME) {
      parts.push(record.A7_NAME.trim());
    }
    
    // Add additional address info
    if (record.A8_NAME) {
      parts.push(record.A8_NAME.trim());
    }
    
    // If no address components, use terminal name as fallback
    if (parts.length === 0) {
      return record.NAME;
    }
    
    return parts.join(' ');
  }

  private determineTerminalType(type: number): 'PARCEL_LOCKER' | 'PICKUP_POINT' | 'POST_OFFICE' {
    // Based on Omniva's type classification
    switch (type) {
      case 0: // Parcel terminal
        return 'PARCEL_LOCKER';
      case 1: // Post office
        return 'POST_OFFICE';
      case 2: // Pickup point
        return 'PICKUP_POINT';
      default:
        return 'PARCEL_LOCKER'; // Default to parcel locker
    }
  }

  private standardizePostalCode(postalCode: string): string | undefined {
    if (!postalCode) return undefined;

    // Remove any non-numeric characters
    let cleaned = postalCode.replace(/[^0-9]/g, '');

    // Pad with leading zeros to ensure 5 digits (preserve leading zeros)
    if (cleaned.length > 0 && cleaned.length <= 5) {
      cleaned = cleaned.padStart(5, '0');
    }

    // Validate length (Lithuanian postal codes are 5 digits)
    if (cleaned.length === 5 && /^\d{5}$/.test(cleaned)) {
      return cleaned;
    }

    return undefined;
  }

  async validateData(terminals: TerminalImportType[]): Promise<{
    valid: TerminalImportType[];
    invalid: Array<{ terminal: any; errors: string[] }>;
  }> {
    const valid: TerminalImportType[] = [];
    const invalid: Array<{ terminal: any; errors: string[] }> = [];

    for (const terminal of terminals) {
      const errors: string[] = [];

      // Validate coordinates are in Lithuania (approximate bounds)
      if (terminal.coordinates.lat < 53.8 || terminal.coordinates.lat > 56.5) {
        errors.push('Latitude outside Lithuania bounds');
      }
      if (terminal.coordinates.lng < 20.9 || terminal.coordinates.lng > 26.9) {
        errors.push('Longitude outside Lithuania bounds');
      }

      // Validate postal code format
      if (terminal.postalCode && !/^\d{5}$/.test(terminal.postalCode)) {
        errors.push('Invalid postal code format');
      }

      // Validate required string fields
      if (!terminal.name || terminal.name.length < 2) {
        errors.push('Invalid terminal name');
      }
      if (!terminal.city || terminal.city.length < 2) {
        errors.push('Invalid city name');
      }
      if (!terminal.address || terminal.address.length < 1) {
        errors.push('Invalid address');
      }

      if (errors.length === 0) {
        valid.push(terminal);
      } else {
        invalid.push({ terminal, errors });
      }
    }

    console.log(`✅ Validation complete: ${valid.length} valid, ${invalid.length} invalid terminals`);
    
    if (invalid.length > 0) {
      console.warn('⚠️  Invalid terminals found:', invalid.slice(0, 5)); // Log first 5 for debugging
    }

    return { valid, invalid };
  }

  async collectAndValidate(): Promise<TerminalImportType[]> {
    const rawData = await this.fetchData();
    const { valid, invalid } = await this.validateData(rawData);
    
    if (invalid.length > 0) {
      console.warn(`⚠️  ${invalid.length} terminals failed validation and will be skipped`);
    }
    
    return valid;
  }
}
