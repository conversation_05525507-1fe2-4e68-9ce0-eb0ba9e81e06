import { parse } from 'csv-parse';
import { readFileSync } from 'fs';
import { join } from 'path';
import { TerminalImportType } from '../../validation/schemas';
import { fileDownloader } from '../file-downloader';
import { config } from '../../config';

interface LPExpressRawData {
  id: string;
  countryCode: string;
  name: string;
  city: string;
  address: string;
  postalCode: string;
  latitude: string;
  longitude: string;
  updated: string;
  comment?: string;
}

export class LPExpressDataCollector {
  private readonly csvUrl = config.lpExpressCsvUrl;
  
  private readonly headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/csv,application/csv,text/plain,*/*',
    'Accept-Language': 'lt-LT,lt;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Cache-Control': 'max-age=0',
    'Origin': process.env['LP_EXPRESS_ORIGIN_URL'] || 'https://lpexpress.lt',
    'Referer': process.env['LP_EXPRESS_REFERER_URL'] || 'https://lpexpress.lt/'
  };

  async fetchData(): Promise<TerminalImportType[]> {
    console.log('🔄 Fetching LP Express terminal data...');

    try {
      // Download file first
      const filePath = join(process.cwd(), 'data', 'downloads', 'lp-express-terminals.csv');

      const downloadResult = await fileDownloader.downloadFile({
        url: this.csvUrl,
        filePath,
        headers: this.headers,
        timeout: 60000
      });

      if (!downloadResult.success) {
        throw new Error(`Download failed: ${downloadResult.error}`);
      }

      console.log(`📄 Downloaded ${downloadResult.fileSize} bytes to ${filePath}`);

      // Read and parse the downloaded file
      const csvData = readFileSync(filePath, 'utf-8');
      console.log(`📄 Read ${csvData.length} characters from CSV file`);

      return this.parseCSVData(csvData);
    } catch (error) {
      console.error('❌ Failed to fetch LP Express data:', error);
      throw new Error(`LP Express data collection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async parseCSVData(csvData: string): Promise<TerminalImportType[]> {
    return new Promise((resolve, reject) => {
      const terminals: TerminalImportType[] = [];
      let lineNumber = 0;
      let fixedCount = 0;
      
      const parser = parse(csvData, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
        delimiter: ',',
        quote: '"',
        escape: '"',
        relax_column_count: true, // Allow variable column count
        skip_records_with_error: false, // Handle errors manually for better logging
        relaxColumnCount: true,
        relaxQuotes: true
      });

      parser.on('readable', () => {
        let record: any;
        while (record = parser.read()) {
          lineNumber++;
          try {
            // Check if this is a malformed row by examining coordinate fields
            const keys = Object.keys(record);
            const hasInvalidCoordinates = !this.looksLikeCoordinate(record.latitude) || !this.looksLikeCoordinate(record.longitude);
            
            if (keys.length > 10 || hasInvalidCoordinates) {
              // Attempt to fix malformed CSV row
              const originalRecord = { ...record };
              record = this.fixMalformedCSVRecord(record, lineNumber);
              if (record) {
                fixedCount++;
                console.log(`🔧 Fixed malformed row ${lineNumber}: ID ${record.id} - Address: "${record.address}"`);
              } else {
                console.warn(`⚠️  Line ${lineNumber}: Could not fix malformed row, skipping:`, originalRecord);
                continue;
              }
            }

            // Validate that we have minimum required fields
            if (!record.id || !record.name || !record.latitude || !record.longitude) {
              console.warn(`⚠️  Line ${lineNumber}: Missing required fields, skipping:`, record);
              continue;
            }

            // Filter only Lithuanian terminals
            if (record.countryCode !== 'LT') {
              continue; // Skip non-Lithuanian terminals
            }

            const terminal = this.transformRecord(record);
            if (terminal) {
              terminals.push(terminal);
            }
          } catch (error) {
            console.warn(`⚠️  Line ${lineNumber}: Error processing record:`, error instanceof Error ? error.message : 'Unknown error', record);
          }
        }
      });

      parser.on('error', (error) => {
        console.error('❌ CSV parsing error:', error);
        reject(new Error(`CSV parsing failed: ${error.message}`));
      });

      parser.on('end', () => {
        console.log(`✅ Parsed ${terminals.length} LP Express terminals from ${lineNumber} total lines`);
        if (fixedCount > 0) {
          console.log(`🔧 Fixed ${fixedCount} malformed rows with address parsing issues`);
        }
        resolve(terminals);
      });
    });
  }

  private fixMalformedCSVRecord(record: any, lineNumber: number): LPExpressRawData | null {
    try {
      // For misaligned 10-column records, we need to realign the fields
      // Pattern: address fields got split by commas, pushing everything to the right
      
      const id = record.id;
      const countryCode = record.countryCode;
      const name = record.name;
      const city = record.city;
      
      if (!id || !countryCode || !name || !city) {
        return null;
      }

      // Gather all potential address and data fields after city
      const allFields = [
        record.address,        // First address part
        record.postalCode,     // Might be second address part
        record.latitude,       // Might be third address part OR postal code
        record.longitude,      // Might be postal code OR coordinate
        record.updated,        // Might be coordinate
        record.comment         // Might be coordinate or timestamp
      ].filter(field => field && field.toString().trim());

      // Now find the coordinates by scanning for valid coordinate pairs
      let foundCoordinateIndex = -1;
      
      for (let i = 0; i < allFields.length - 1; i++) {
        const potentialLat = allFields[i];
        const potentialLng = allFields[i + 1];
        
        if (this.looksLikeCoordinate(potentialLat) && this.looksLikeCoordinate(potentialLng)) {
          const lat = parseFloat(potentialLat);
          const lng = parseFloat(potentialLng);
          
          // Check if coordinates are in reasonable range for Lithuania
          if (!isNaN(lat) && !isNaN(lng) && 
              lat >= 53.8 && lat <= 56.5 && 
              lng >= 20.9 && lng <= 26.9) {
            foundCoordinateIndex = i;
            break;
          }
        }
      }
      
      if (foundCoordinateIndex === -1) {
        console.warn(`Line ${lineNumber}: Could not find valid coordinates in:`, allFields);
        return null;
      }
      
      // Reconstruct the fields
      const addressParts = allFields.slice(0, foundCoordinateIndex - 1); // Everything before postal code
      const postalCode = allFields[foundCoordinateIndex - 1] || ''; // Field just before coordinates
      const latitude = allFields[foundCoordinateIndex];
      const longitude = allFields[foundCoordinateIndex + 1];
      const updated = allFields[foundCoordinateIndex + 2] || '';
      const comment = allFields[foundCoordinateIndex + 3] || '';
      
      // Validate postal code
      if (!this.looksLikePostalCode(postalCode)) {
        // If the field before coordinates doesn't look like postal code,
        // include it in the address and leave postal code empty
        addressParts.push(postalCode);
        const reconstructedAddress = addressParts.join(', ');
        
        return {
          id,
          countryCode,
          name,
          city,
          address: reconstructedAddress,
          postalCode: '',
          latitude,
          longitude,
          updated,
          comment
        };
      }
      
      const reconstructedAddress = addressParts.join(', ');
      
      return {
        id,
        countryCode,
        name,
        city,
        address: reconstructedAddress,
        postalCode,
        latitude,
        longitude,
        updated,
        comment
      };
      
    } catch (error) {
      console.warn(`Line ${lineNumber}: Failed to fix malformed record:`, error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  private looksLikeCoordinate(value: string): boolean {
    if (!value || typeof value !== 'string') return false;
    
    const trimmed = value.trim();
    const parsed = parseFloat(trimmed);
    
    // Must be a valid number
    if (isNaN(parsed)) return false;
    
    // Must have decimal places (coordinates are precise)
    if (!trimmed.includes('.')) return false;
    
    // Must be in reasonable coordinate range
    if (parsed < -180 || parsed > 180) return false;
    
    // For Lithuanian coordinates, should be roughly in these ranges
    if (parsed >= 53 && parsed <= 57) return true; // Latitude range
    if (parsed >= 20 && parsed <= 27) return true; // Longitude range
    
    return false;
  }

  private looksLikePostalCode(value: string): boolean {
    if (!value || typeof value !== 'string') return false;
    
    const trimmed = value.trim();
    
    // Lithuanian postal codes are 5 digits, possibly with leading zeros
    return /^\d{4,5}$/.test(trimmed);
  }

  private transformRecord(record: LPExpressRawData): TerminalImportType | null {
    // Validate required fields
    if (!record.id || !record.name || !record.city || !record.address) {
      throw new Error('Missing required fields');
    }

    // Parse coordinates
    const lat = parseFloat(record.latitude);
    const lng = parseFloat(record.longitude);
    
    if (isNaN(lat) || isNaN(lng)) {
      throw new Error('Invalid coordinates');
    }

    // Validate coordinate ranges
    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      throw new Error('Coordinates out of range');
    }

    // Standardize postal code
    const postalCode = this.standardizePostalCode(record.postalCode);

    return {
      id: `lp_${record.id}`,
      name: record.name.trim(),
      city: record.city.trim(),
      address: record.address.trim(),
      postalCode,
      coordinates: { lat, lng },
      provider: 'LP_EXPRESS',
      terminalType: 'PARCEL_LOCKER',
      metadata: {
        originalId: record.id,
        lastUpdated: record.updated || new Date().toISOString(),
        source: 'lp_express_csv'
      }
    };
  }

  private standardizePostalCode(postalCode: string): string | undefined {
    if (!postalCode) return undefined;

    // Remove any non-numeric characters and country prefixes
    let cleaned = postalCode
      .replace(/^LT-?/i, '') // Remove LT prefix
      .replace(/[^0-9]/g, ''); // Keep only digits

    // Pad with leading zeros to ensure 5 digits (preserve leading zeros)
    if (cleaned.length > 0 && cleaned.length <= 5) {
      cleaned = cleaned.padStart(5, '0');
    }

    // Validate length (Lithuanian postal codes are 5 digits)
    if (cleaned.length === 5 && /^\d{5}$/.test(cleaned)) {
      return cleaned;
    }

    return undefined;
  }

  async validateData(terminals: TerminalImportType[]): Promise<{
    valid: TerminalImportType[];
    invalid: Array<{ terminal: any; errors: string[] }>;
  }> {
    const valid: TerminalImportType[] = [];
    const invalid: Array<{ terminal: any; errors: string[] }> = [];

    for (const terminal of terminals) {
      const errors: string[] = [];

      // Validate coordinates are in Lithuania (approximate bounds)
      if (terminal.coordinates.lat < 53.8 || terminal.coordinates.lat > 56.5) {
        errors.push('Latitude outside Lithuania bounds');
      }
      if (terminal.coordinates.lng < 20.9 || terminal.coordinates.lng > 26.9) {
        errors.push('Longitude outside Lithuania bounds');
      }

      // Validate postal code format
      if (terminal.postalCode && !/^\d{5}$/.test(terminal.postalCode)) {
        errors.push('Invalid postal code format');
      }

      // Validate required string fields
      if (!terminal.name || terminal.name.length < 2) {
        errors.push('Invalid terminal name');
      }
      if (!terminal.city || terminal.city.length < 2) {
        errors.push('Invalid city name');
      }
      if (!terminal.address || terminal.address.length < 5) {
        errors.push('Invalid address');
      }

      if (errors.length === 0) {
        valid.push(terminal);
      } else {
        invalid.push({ terminal, errors });
      }
    }

    console.log(`✅ Validation complete: ${valid.length} valid, ${invalid.length} invalid terminals`);
    
    if (invalid.length > 0) {
      console.warn('⚠️  Invalid terminals found:', invalid.slice(0, 5)); // Log first 5 for debugging
    }

    return { valid, invalid };
  }

  async collectAndValidate(): Promise<TerminalImportType[]> {
    const rawData = await this.fetchData();
    const { valid, invalid } = await this.validateData(rawData);
    
    if (invalid.length > 0) {
      console.warn(`⚠️  ${invalid.length} terminals failed validation and will be skipped`);
    }
    
    return valid;
  }
}
