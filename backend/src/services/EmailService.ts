/**
 * Email Service
 * 
 * Handles email sending functionality including verification emails,
 * password reset emails, and notification emails for the SaaS platform.
 */

import { Pool } from 'pg';
import { getDatabasePool } from '../database/connection';
import { generateSecureToken, hashToken } from '../utils/crypto';

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

export interface EmailVerificationToken {
  id: string;
  user_id: string;
  token: string;
  expires_at: Date;
  used: boolean;
  created_at: Date;
}

export interface PasswordResetToken {
  id: string;
  user_id: string;
  token: string;
  expires_at: Date;
  used: boolean;
  created_at: Date;
}

export class EmailService {
  private pool: Pool | null = null;

  constructor() {}

  private getPool(): Pool {
    if (!this.pool) {
      this.pool = getDatabasePool();
    }
    return this.pool;
  }

  // =============================================================================
  // EMAIL VERIFICATION
  // =============================================================================

  /**
   * Create email verification token
   */
  async createEmailVerificationToken(userId: string): Promise<string> {
    const pool = this.getPool();
    const token = generateSecureToken(32);
    const tokenHash = hashToken(token);
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Invalidate any existing tokens by marking them as used
    await pool.query(
      'UPDATE email_verification_tokens SET used_at = NOW() WHERE user_id = $1 AND used_at IS NULL',
      [userId]
    );

    // Create new token
    const query = `
      INSERT INTO email_verification_tokens (user_id, token_hash, expires_at)
      VALUES ($1, $2, $3)
      RETURNING id
    `;

    await pool.query(query, [userId, tokenHash, expiresAt]);
    return token;
  }

  /**
   * Verify email verification token
   */
  async verifyEmailToken(token: string): Promise<{ success: boolean; userId?: string; error?: string }> {
    const pool = this.getPool();
    const tokenHash = hashToken(token);

    const query = `
      SELECT user_id, expires_at, used_at
      FROM email_verification_tokens
      WHERE token_hash = $1
    `;

    const result = await pool.query(query, [tokenHash]);

    if (result.rows.length === 0) {
      return { success: false, error: 'Invalid verification token' };
    }

    const tokenData = result.rows[0];

    if (tokenData.used_at) {
      return { success: false, error: 'Verification token has already been used' };
    }

    if (new Date() > tokenData.expires_at) {
      return { success: false, error: 'Verification token has expired' };
    }

    // Mark token as used
    await pool.query(
      'UPDATE email_verification_tokens SET used_at = NOW() WHERE token_hash = $1',
      [tokenHash]
    );

    // Mark user email as verified
    await pool.query(
      'UPDATE users SET email_verified = true, email_verified_at = NOW() WHERE id = $1',
      [tokenData.user_id]
    );

    return { success: true, userId: tokenData.user_id };
  }

  /**
   * Send email verification email
   */
  async sendEmailVerification(userId: string, email: string, firstName?: string): Promise<void> {
    const token = await this.createEmailVerificationToken(userId);
    const verificationUrl = `${process.env['FRONTEND_URL']}/verify-email?token=${token}`;

    const template = this.getEmailVerificationTemplate(verificationUrl, firstName);
    
    await this.sendEmail(email, template.subject, template.htmlContent, template.textContent);
    
    console.log(`📧 Email verification sent to ${email} (Token: ${token})`);
  }

  // =============================================================================
  // PASSWORD RESET
  // =============================================================================

  /**
   * Create password reset token
   */
  async createPasswordResetToken(userId: string): Promise<string> {
    const pool = this.getPool();
    const token = generateSecureToken(32);
    const tokenHash = hashToken(token);
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    // Invalidate any existing tokens
    await pool.query(
      'UPDATE password_reset_tokens SET used_at = NOW() WHERE user_id = $1 AND used_at IS NULL',
      [userId]
    );

    // Create new token
    const query = `
      INSERT INTO password_reset_tokens (user_id, token_hash, expires_at)
      VALUES ($1, $2, $3)
      RETURNING id
    `;

    await pool.query(query, [userId, tokenHash, expiresAt]);
    return token;
  }

  /**
   * Verify password reset token
   */
  async verifyPasswordResetToken(token: string): Promise<{ success: boolean; userId?: string; error?: string }> {
    const pool = this.getPool();
    const tokenHash = hashToken(token);

    const query = `
      SELECT user_id, expires_at, used_at
      FROM password_reset_tokens
      WHERE token_hash = $1
    `;

    const result = await pool.query(query, [tokenHash]);

    if (result.rows.length === 0) {
      return { success: false, error: 'Invalid reset token' };
    }

    const tokenData = result.rows[0];

    if (tokenData.used_at) {
      return { success: false, error: 'Reset token has already been used' };
    }

    if (new Date() > tokenData.expires_at) {
      return { success: false, error: 'Reset token has expired' };
    }

    return { success: true, userId: tokenData.user_id };
  }

  /**
   * Mark password reset token as used
   */
  async markPasswordResetTokenUsed(token: string): Promise<void> {
    const pool = this.getPool();
    const tokenHash = hashToken(token);
    await pool.query(
      'UPDATE password_reset_tokens SET used_at = NOW() WHERE token_hash = $1',
      [tokenHash]
    );
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(userId: string, email: string, firstName?: string): Promise<void> {
    const token = await this.createPasswordResetToken(userId);
    const resetUrl = `${process.env['FRONTEND_URL']}/reset-password?token=${token}`;

    const template = this.getPasswordResetTemplate(resetUrl, firstName);
    
    await this.sendEmail(email, template.subject, template.htmlContent, template.textContent);
    
    console.log(`📧 Password reset sent to ${email} (Token: ${token})`);
  }

  // =============================================================================
  // EMAIL TEMPLATES
  // =============================================================================

  private getEmailVerificationTemplate(verificationUrl: string, firstName?: string): EmailTemplate {
    const greeting = firstName ? `Hi ${firstName}` : 'Hello';
    
    return {
      subject: 'Verify your email address - Postal Terminal API',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Email Verification</h2>
          <p>${greeting},</p>
          <p>Thank you for signing up for Postal Terminal API! Please verify your email address by clicking the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
          <p>This link will expire in 24 hours.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            If you didn't create an account, you can safely ignore this email.
          </p>
        </div>
      `,
      textContent: `
        ${greeting},

        Thank you for signing up for Postal Terminal API! Please verify your email address by visiting:

        ${verificationUrl}

        This link will expire in 24 hours.

        If you didn't create an account, you can safely ignore this email.
      `
    };
  }

  private getPasswordResetTemplate(resetUrl: string, firstName?: string): EmailTemplate {
    const greeting = firstName ? `Hi ${firstName}` : 'Hello';
    
    return {
      subject: 'Reset your password - Postal Terminal API',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Password Reset</h2>
          <p>${greeting},</p>
          <p>You requested to reset your password for your Postal Terminal API account. Click the button below to set a new password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          <p>This link will expire in 1 hour.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            If you didn't request a password reset, you can safely ignore this email.
          </p>
        </div>
      `,
      textContent: `
        ${greeting},

        You requested to reset your password for your Postal Terminal API account. Visit this link to set a new password:

        ${resetUrl}

        This link will expire in 1 hour.

        If you didn't request a password reset, you can safely ignore this email.
      `
    };
  }

  // =============================================================================
  // EMAIL SENDING
  // =============================================================================

  /**
   * Send email (mock implementation - replace with actual email service)
   */
  private async sendEmail(to: string, subject: string, htmlContent: string, textContent: string): Promise<void> {
    // In a real implementation, this would use a service like:
    // - SendGrid
    // - AWS SES
    // - Mailgun
    // - Nodemailer with SMTP
    
    console.log(`📧 EMAIL SENT:
To: ${to}
Subject: ${subject}
HTML: ${htmlContent.substring(0, 100)}...
Text: ${textContent.substring(0, 100)}...`);

    // For development, we'll just log the email
    // In production, implement actual email sending here
  }

  /**
   * Get email sending statistics
   */
  async getEmailStats(): Promise<{
    verificationTokens: { total: number; used: number; expired: number };
    passwordResetTokens: { total: number; used: number; expired: number };
  }> {
    const pool = this.getPool();
    
    const [verificationResult, passwordResetResult] = await Promise.all([
      pool.query(`
        SELECT
          COUNT(*) as total,
          COUNT(*) FILTER (WHERE used_at IS NOT NULL) as used,
          COUNT(*) FILTER (WHERE expires_at < NOW() AND used_at IS NULL) as expired
        FROM email_verification_tokens
      `),
      pool.query(`
        SELECT
          COUNT(*) as total,
          COUNT(*) FILTER (WHERE used_at IS NOT NULL) as used,
          COUNT(*) FILTER (WHERE expires_at < NOW() AND used_at IS NULL) as expired
        FROM password_reset_tokens
      `)
    ]);

    return {
      verificationTokens: {
        total: parseInt(verificationResult.rows[0].total),
        used: parseInt(verificationResult.rows[0].used),
        expired: parseInt(verificationResult.rows[0].expired)
      },
      passwordResetTokens: {
        total: parseInt(passwordResetResult.rows[0].total),
        used: parseInt(passwordResetResult.rows[0].used),
        expired: parseInt(passwordResetResult.rows[0].expired)
      }
    };
  }
}
