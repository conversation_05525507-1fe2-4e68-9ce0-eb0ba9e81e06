/**
 * Stripe Payment Service
 * 
 * Handles all Stripe integration including customer management, subscription lifecycle,
 * payment processing, and webhook handling for the SaaS platform.
 */

import Stripe from 'stripe';
import { SubscriptionService } from './SubscriptionService';
import { UserService } from './UserService';
// Types are used in method signatures

export class StripeService {
  private stripe: Stripe;
  private subscriptionService: SubscriptionService;
  private userService: UserService;

  constructor() {
    const stripeSecretKey = process.env['STRIPE_SECRET_KEY'];
    if (!stripeSecretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }

    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil',
      typescript: true
    });

    this.subscriptionService = new SubscriptionService();
    this.userService = new UserService();
  }

  // Removed unused getPool method - using services directly

  // =============================================================================
  // CUSTOMER MANAGEMENT
  // =============================================================================

  /**
   * Create or retrieve Stripe customer for user
   */
  async getOrCreateCustomer(userId: string): Promise<Stripe.Customer> {
    const user = await this.userService.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Check if user already has a Stripe customer ID
    const subscription = await this.subscriptionService.getUserActiveSubscription(userId);
    if (subscription?.stripe_customer_id) {
      try {
        const customer = await this.stripe.customers.retrieve(subscription.stripe_customer_id);
        if (!customer.deleted) {
          return customer as Stripe.Customer;
        }
      } catch (error) {
        console.warn(`Stripe customer ${subscription.stripe_customer_id} not found, creating new one`);
      }
    }

    // Create new Stripe customer
    const customer = await this.stripe.customers.create({
      email: user.email,
      name: user.first_name && user.last_name 
        ? `${user.first_name} ${user.last_name}` 
        : user.email,
      metadata: {
        user_id: userId,
        created_by: 'postal-terminal-api'
      }
    });

    return customer;
  }

  /**
   * Update Stripe customer information
   */
  async updateCustomer(customerId: string, updates: {
    email?: string;
    name?: string;
    metadata?: Record<string, string>;
  }): Promise<Stripe.Customer> {
    return await this.stripe.customers.update(customerId, updates);
  }

  // =============================================================================
  // SUBSCRIPTION MANAGEMENT
  // =============================================================================

  /**
   * Create checkout session for subscription
   */
  async createCheckoutSession(
    userId: string,
    planId: string,
    billingCycle: 'monthly' | 'yearly' = 'monthly',
    successUrl: string,
    cancelUrl: string
  ): Promise<Stripe.Checkout.Session> {
    const plan = await this.subscriptionService.getPlanById(planId);
    if (!plan || !plan.is_active) {
      throw new Error('Invalid or inactive subscription plan');
    }

    const customer = await this.getOrCreateCustomer(userId);
    
    // Get the appropriate Stripe price ID
    const priceId = billingCycle === 'yearly' 
      ? plan.stripe_price_id_yearly 
      : plan.stripe_price_id_monthly;

    if (!priceId) {
      throw new Error(`No Stripe price ID configured for ${billingCycle} billing`);
    }

    const session = await this.stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        user_id: userId,
        plan_id: planId,
        billing_cycle: billingCycle
      },
      subscription_data: {
        metadata: {
          user_id: userId,
          plan_id: planId,
          billing_cycle: billingCycle
        }
      }
    });

    return session;
  }

  /**
   * Create subscription change session
   */
  async createSubscriptionChangeSession(
    userId: string,
    newPlanId: string,
    billingCycle: 'monthly' | 'yearly' = 'monthly',
    successUrl: string,
    cancelUrl: string
  ): Promise<Stripe.Checkout.Session> {
    const currentSubscription = await this.subscriptionService.getUserActiveSubscription(userId);
    if (!currentSubscription || !currentSubscription.stripe_subscription_id) {
      throw new Error('No active subscription found');
    }

    const newPlan = await this.subscriptionService.getPlanById(newPlanId);
    if (!newPlan || !newPlan.is_active) {
      throw new Error('Invalid or inactive subscription plan');
    }

    const customer = await this.getOrCreateCustomer(userId);
    
    const priceId = billingCycle === 'yearly' 
      ? newPlan.stripe_price_id_yearly 
      : newPlan.stripe_price_id_monthly;

    if (!priceId) {
      throw new Error(`No Stripe price ID configured for ${billingCycle} billing`);
    }

    // Cancel current subscription at period end
    await this.stripe.subscriptions.update(currentSubscription.stripe_subscription_id, {
      cancel_at_period_end: true
    });

    // Create new checkout session
    const session = await this.stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        user_id: userId,
        plan_id: newPlanId,
        billing_cycle: billingCycle,
        change_from_subscription: currentSubscription.stripe_subscription_id
      },
      subscription_data: {
        metadata: {
          user_id: userId,
          plan_id: newPlanId,
          billing_cycle: billingCycle,
          previous_subscription: currentSubscription.stripe_subscription_id
        }
      }
    });

    return session;
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(userId: string, cancelAtPeriodEnd: boolean = true): Promise<Stripe.Subscription> {
    const subscription = await this.subscriptionService.getUserActiveSubscription(userId);
    if (!subscription || !subscription.stripe_subscription_id) {
      throw new Error('No active subscription found');
    }

    const stripeSubscription = await this.stripe.subscriptions.update(
      subscription.stripe_subscription_id,
      {
        cancel_at_period_end: cancelAtPeriodEnd,
        metadata: {
          canceled_by_user: 'true',
          canceled_at: new Date().toISOString()
        }
      }
    );

    // Update local subscription
    await this.subscriptionService.updateUserSubscription(subscription.id, {
      status: cancelAtPeriodEnd ? 'active' : 'canceled'
    });

    return stripeSubscription;
  }

  /**
   * Reactivate canceled subscription
   */
  async reactivateSubscription(userId: string): Promise<Stripe.Subscription> {
    const subscription = await this.subscriptionService.getUserActiveSubscription(userId);
    if (!subscription || !subscription.stripe_subscription_id) {
      throw new Error('No subscription found');
    }

    const stripeSubscription = await this.stripe.subscriptions.update(
      subscription.stripe_subscription_id,
      {
        cancel_at_period_end: false
      }
    );

    // Update local subscription
    await this.subscriptionService.updateUserSubscription(subscription.id, {
      status: 'active'
    });

    return stripeSubscription;
  }

  // =============================================================================
  // WEBHOOK PROCESSING
  // =============================================================================

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(payload: string, signature: string): Stripe.Event {
    const webhookSecret = process.env['STRIPE_WEBHOOK_SECRET'];
    if (!webhookSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET environment variable is required');
    }

    try {
      return this.stripe.webhooks.constructEvent(payload, signature, webhookSecret);
    } catch (error) {
      throw new Error(`Webhook signature verification failed: ${error}`);
    }
  }

  /**
   * Process webhook event
   */
  async processWebhookEvent(event: Stripe.Event): Promise<void> {
    console.log(`Processing Stripe webhook: ${event.type}`);

    try {
      switch (event.type) {
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;

        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;

        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        case 'checkout.session.completed':
          await this.handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
          break;

        default:
          console.log(`Unhandled webhook event type: ${event.type}`);
      }
    } catch (error) {
      console.error(`Error processing webhook ${event.type}:`, error);
      throw error;
    }
  }

  // =============================================================================
  // WEBHOOK HANDLERS
  // =============================================================================

  private async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<void> {
    const userId = subscription.metadata['user_id'];
    const planId = subscription.metadata['plan_id'];
    const billingCycle = subscription.metadata['billing_cycle'] as 'monthly' | 'yearly' || 'monthly';

    if (!userId || !planId) {
      console.warn('Missing user_id or plan_id in subscription metadata');
      return;
    }

    // Create or update local subscription
    const subscriptionData = {
      user_id: userId,
      plan_id: planId,
      billing_cycle: billingCycle,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer as string,
      status: this.mapStripeStatus(subscription.status),
      current_period_start: new Date((subscription as any).current_period_start * 1000),
      current_period_end: new Date((subscription as any).current_period_end * 1000),
      trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
      trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null
    };

    await this.subscriptionService.createUserSubscription(subscriptionData);

    // Log audit event
    await this.subscriptionService.logAuditEvent({
      user_id: userId,
      event_type: 'SUBSCRIPTION_CREATED_STRIPE',
      event_description: `Stripe subscription ${subscription.id} created`,
      new_values: subscriptionData,
      performed_by: 'stripe_webhook'
    });
  }

  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    // Find local subscription by Stripe ID
    const localSubscription = await this.subscriptionService.getSubscriptionByStripeId(subscription.id);
    if (!localSubscription) {
      console.warn(`Local subscription not found for Stripe ID: ${subscription.id}`);
      return;
    }

    // Update local subscription
    const updates = {
      status: this.mapStripeStatus(subscription.status),
      current_period_start: new Date((subscription as any).current_period_start * 1000),
      current_period_end: new Date((subscription as any).current_period_end * 1000),
      ...(subscription.canceled_at && { canceled_at: new Date(subscription.canceled_at * 1000) })
    };

    await this.subscriptionService.updateUserSubscription(localSubscription.id, updates);

    // Log audit event
    await this.subscriptionService.logAuditEvent({
      user_id: localSubscription.user_id,
      subscription_id: localSubscription.id,
      event_type: 'SUBSCRIPTION_UPDATED_STRIPE',
      event_description: `Stripe subscription ${subscription.id} updated`,
      new_values: updates,
      performed_by: 'stripe_webhook'
    });
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    const localSubscription = await this.subscriptionService.getSubscriptionByStripeId(subscription.id);
    if (!localSubscription) {
      console.warn(`Local subscription not found for Stripe ID: ${subscription.id}`);
      return;
    }

    // Update local subscription status
    await this.subscriptionService.updateUserSubscription(localSubscription.id, {
      status: 'canceled',
      canceled_at: new Date()
    });

    // Log audit event
    await this.subscriptionService.logAuditEvent({
      user_id: localSubscription.user_id,
      subscription_id: localSubscription.id,
      event_type: 'SUBSCRIPTION_CANCELED_STRIPE',
      event_description: `Stripe subscription ${subscription.id} canceled`,
      performed_by: 'stripe_webhook'
    });
  }

  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    if (!(invoice as any).subscription) return;

    const localSubscription = await this.subscriptionService.getSubscriptionByStripeId(
      (invoice as any).subscription as string
    );

    if (!localSubscription) {
      console.warn(`Local subscription not found for Stripe ID: ${(invoice as any).subscription}`);
      return;
    }

    // Create order record
    const orderData: any = {
      user_id: localSubscription.user_id,
      subscription_id: localSubscription.id,
      order_number: await this.subscriptionService.generateOrderNumber(),
      status: 'completed',
      subtotal: (invoice.subtotal || 0) / 100, // Convert from cents
      tax_amount: ((invoice as any).tax || 0) / 100,
      total_amount: (invoice.total || 0) / 100,
      currency: invoice.currency.toUpperCase(),
      stripe_payment_intent_id: (invoice as any).payment_intent as string,
      completed_at: new Date()
    };

    // Only add stripe_invoice_id if it exists
    if (invoice.id) {
      orderData.stripe_invoice_id = invoice.id;
    }

    await this.subscriptionService.createOrder(orderData);

    // Log audit event
    await this.subscriptionService.logAuditEvent({
      user_id: localSubscription.user_id,
      subscription_id: localSubscription.id,
      event_type: 'PAYMENT_SUCCEEDED',
      event_description: `Payment succeeded for invoice ${invoice.id}`,
      new_values: { invoice_id: invoice.id, amount: invoice.total },
      performed_by: 'stripe_webhook'
    });
  }

  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    if (!(invoice as any).subscription) return;

    const localSubscription = await this.subscriptionService.getSubscriptionByStripeId(
      (invoice as any).subscription as string
    );

    if (!localSubscription) {
      console.warn(`Local subscription not found for Stripe ID: ${(invoice as any).subscription}`);
      return;
    }

    // Update subscription status if needed
    if (localSubscription.status === 'active') {
      await this.subscriptionService.updateUserSubscription(localSubscription.id, {
        status: 'past_due'
      });
    }

    // Log audit event
    await this.subscriptionService.logAuditEvent({
      user_id: localSubscription.user_id,
      subscription_id: localSubscription.id,
      event_type: 'PAYMENT_FAILED',
      event_description: `Payment failed for invoice ${invoice.id}`,
      new_values: { 
        invoice_id: invoice.id, 
        amount: invoice.total,
        failure_reason: invoice.last_finalization_error?.message 
      },
      performed_by: 'stripe_webhook'
    });
  }

  private async handleCheckoutCompleted(session: Stripe.Checkout.Session): Promise<void> {
    const userId = session.metadata?.['user_id'];
    const planId = session.metadata?.['plan_id'];

    if (!userId || !planId) {
      console.warn('Missing user_id or plan_id in checkout session metadata');
      return;
    }

    // The subscription will be handled by the subscription.created webhook
    // This is mainly for logging the checkout completion
    await this.subscriptionService.logAuditEvent({
      user_id: userId,
      event_type: 'CHECKOUT_COMPLETED',
      event_description: `Checkout session ${session.id} completed`,
      new_values: { 
        session_id: session.id,
        plan_id: planId,
        amount_total: session.amount_total 
      },
      performed_by: 'stripe_webhook'
    });
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Map Stripe subscription status to local status
   */
  private mapStripeStatus(stripeStatus: string): 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing' {
    switch (stripeStatus) {
      case 'active':
        return 'active';
      case 'past_due':
        return 'past_due';
      case 'canceled':
      case 'cancelled':
        return 'canceled';
      case 'unpaid':
        return 'unpaid';
      case 'trialing':
        return 'trialing';
      default:
        console.warn(`Unknown Stripe status: ${stripeStatus}, defaulting to 'unpaid'`);
        return 'unpaid';
    }
  }

  /**
   * Get subscription details from Stripe
   */
  async getStripeSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    return await this.stripe.subscriptions.retrieve(subscriptionId);
  }

  /**
   * Get customer details from Stripe
   */
  async getStripeCustomer(customerId: string): Promise<Stripe.Customer> {
    const customer = await this.stripe.customers.retrieve(customerId);
    if (customer.deleted) {
      throw new Error('Customer has been deleted');
    }
    return customer as Stripe.Customer;
  }
}
