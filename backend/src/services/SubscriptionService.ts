/**
 * Subscription Management Service
 * 
 * Handles subscription plans, user subscriptions, orders, and billing operations.
 * Integrates with <PERSON>e for payment processing and provides comprehensive
 * subscription lifecycle management.
 */

import { Pool } from 'pg';
import { getDatabasePool } from '../database/connection';
import {
  SubscriptionPlan,
  UserSubscription,
  Order,
  SubscriptionAuditLog,
  CreateSubscriptionPlanRequest,
  UpdateSubscriptionPlanRequest,
  CreateSubscriptionRequest,
  UpdateSubscriptionRequest,
  CreateOrderRequest
} from '../types/subscription';

export class SubscriptionService {
  private pool: Pool | null = null;

  private getPool(): Pool {
    if (!this.pool) {
      this.pool = getDatabasePool();
    }
    return this.pool;
  }

  // =============================================================================
  // SUBSCRIPTION PLANS MANAGEMENT
  // =============================================================================

  /**
   * Get all active subscription plans
   */
  async getActivePlans(includePrivate: boolean = false): Promise<SubscriptionPlan[]> {
    const query = `
      SELECT * FROM subscription_plans
      WHERE is_active = true
      ${!includePrivate ? 'AND is_public = true' : ''}
      ORDER BY sort_order, name
    `;

    const result = await this.getPool().query(query);
    return result.rows;
  }

  /**
   * Get subscription plan by ID
   */
  async getPlanById(planId: string): Promise<SubscriptionPlan | null> {
    const result = await this.getPool().query(
      'SELECT * FROM subscription_plans WHERE id = $1',
      [planId]
    );

    return result.rows[0] || null;
  }

  /**
   * Get subscription plan by name
   */
  async getPlanByName(name: string): Promise<SubscriptionPlan | null> {
    const result = await this.getPool().query(
      'SELECT * FROM subscription_plans WHERE name = $1',
      [name]
    );

    return result.rows[0] || null;
  }

  /**
   * Create new subscription plan
   */
  async createPlan(planData: CreateSubscriptionPlanRequest): Promise<SubscriptionPlan> {
    const query = `
      INSERT INTO subscription_plans (
        name, display_name, description,
        price_eur, billing_interval,
        api_requests_per_month, api_requests_per_minute, max_api_keys,
        features, is_active, is_public, sort_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `;

    const values = [
      planData.name,
      planData.display_name,
      planData.description,
      planData.price_monthly,
      'monthly', // Default billing interval
      planData.api_requests_per_month,
      planData.api_requests_per_minute,
      planData.max_api_keys ?? 1,
      JSON.stringify(planData.features || {}),
      true, // is_active - default to true
      planData.is_public ?? true,
      planData.sort_order ?? 0
    ];

    const result = await this.getPool().query(query, values);
    return result.rows[0];
  }

  /**
   * Update subscription plan
   */
  async updatePlan(planId: string, updates: UpdateSubscriptionPlanRequest): Promise<SubscriptionPlan | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic UPDATE query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'features') {
          setParts.push(`${key} = $${paramIndex}::jsonb`);
          values.push(JSON.stringify(value));
        } else {
          setParts.push(`${key} = $${paramIndex}`);
          values.push(value);
        }
        paramIndex++;
      }
    });

    if (setParts.length === 0) {
      return this.getPlanById(planId);
    }

    setParts.push(`updated_at = NOW()`);
    values.push(planId);

    const query = `
      UPDATE subscription_plans
      SET ${setParts.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await this.getPool().query(query, values);
    return result.rows[0] || null;
  }

  /**
   * Delete subscription plan (soft delete by setting inactive)
   */
  async deletePlan(planId: string): Promise<boolean> {
    const result = await this.getPool().query(
      'UPDATE subscription_plans SET is_active = false, updated_at = NOW() WHERE id = $1',
      [planId]
    );

    return (result.rowCount || 0) > 0;
  }

  // =============================================================================
  // USER SUBSCRIPTIONS MANAGEMENT
  // =============================================================================

  /**
   * Get user's active subscription
   */
  async getUserActiveSubscription(userId: string): Promise<UserSubscription | null> {
    const query = `
      SELECT us.*, sp.name as plan_name, sp.display_name as plan_display_name
      FROM user_subscriptions us
      JOIN subscription_plans sp ON us.plan_id = sp.id
      WHERE us.user_id = $1 AND us.status = 'active'
      ORDER BY us.created_at DESC
      LIMIT 1
    `;
    
    const result = await this.getPool().query(query, [userId]);
    return result.rows[0] || null;
  }

  /**
   * Get user's subscription history
   */
  async getUserSubscriptionHistory(userId: string): Promise<UserSubscription[]> {
    const query = `
      SELECT us.*, sp.name as plan_name, sp.display_name as plan_display_name
      FROM user_subscriptions us
      JOIN subscription_plans sp ON us.plan_id = sp.id
      WHERE us.user_id = $1
      ORDER BY us.created_at DESC
    `;

    const result = await this.getPool().query(query, [userId]);
    return result.rows;
  }

  /**
   * Get subscription by Stripe subscription ID
   */
  async getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<UserSubscription | null> {
    const query = `
      SELECT us.*, sp.name as plan_name, sp.display_name as plan_display_name
      FROM user_subscriptions us
      JOIN subscription_plans sp ON us.plan_id = sp.id
      WHERE us.stripe_subscription_id = $1
    `;

    const result = await this.getPool().query(query, [stripeSubscriptionId]);
    return result.rows[0] || null;
  }

  /**
   * Create user subscription
   */
  async createUserSubscription(subscriptionData: CreateSubscriptionRequest & {
    user_id: string;
    stripe_subscription_id?: string;
    stripe_customer_id?: string;
    stripe_price_id?: string;
    current_period_start?: Date;
    current_period_end?: Date;
    trial_start?: Date | null;
    trial_end?: Date | null;
  }): Promise<UserSubscription> {
    const query = `
      INSERT INTO user_subscriptions (
        user_id, plan_id, status, billing_cycle,
        current_period_start, current_period_end,
        trial_start, trial_end,
        stripe_subscription_id, stripe_customer_id, stripe_price_id
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    const now = new Date();
    const periodStart = subscriptionData.current_period_start || now;
    const periodEnd = subscriptionData.current_period_end || (() => {
      const end = new Date(now);
      end.setMonth(end.getMonth() + (subscriptionData.billing_cycle === 'yearly' ? 12 : 1));
      return end;
    })();

    const trialEnd = subscriptionData.trial_end ||
      (subscriptionData.trial_days ? new Date(now.getTime() + subscriptionData.trial_days * 24 * 60 * 60 * 1000) : null);

    const values = [
      subscriptionData.user_id,
      subscriptionData.plan_id,
      subscriptionData.status || (trialEnd ? 'trialing' : 'active'),
      subscriptionData.billing_cycle || 'monthly',
      periodStart,
      periodEnd,
      subscriptionData.trial_start || (trialEnd ? now : null),
      trialEnd,
      subscriptionData.stripe_subscription_id || null,
      subscriptionData.stripe_customer_id || null,
      subscriptionData.stripe_price_id || null
    ];

    const result = await this.getPool().query(query, values);
    return result.rows[0];
  }

  /**
   * Update user subscription
   */
  async updateUserSubscription(subscriptionId: string, updates: UpdateSubscriptionRequest): Promise<UserSubscription | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic UPDATE query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        setParts.push(`${key} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (setParts.length === 0) {
      return this.getUserSubscriptionById(subscriptionId);
    }

    setParts.push(`updated_at = NOW()`);
    values.push(subscriptionId);

    const query = `
      UPDATE user_subscriptions
      SET ${setParts.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await this.getPool().query(query, values);
    return result.rows[0] || null;
  }

  /**
   * Get subscription by ID
   */
  async getUserSubscriptionById(subscriptionId: string): Promise<UserSubscription | null> {
    const result = await this.getPool().query(
      'SELECT * FROM user_subscriptions WHERE id = $1',
      [subscriptionId]
    );

    return result.rows[0] || null;
  }

  /**
   * Cancel user subscription
   */
  async cancelUserSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<UserSubscription | null> {
    const updates: any = {
      cancel_at_period_end: cancelAtPeriodEnd,
      canceled_at: cancelAtPeriodEnd ? undefined : new Date()
    };

    if (!cancelAtPeriodEnd) {
      updates.status = 'canceled';
    }

    return this.updateUserSubscription(subscriptionId, updates);
  }

  /**
   * Update subscription usage
   */
  async updateSubscriptionUsage(subscriptionId: string, usageUpdate: {
    api_requests_used: number;
    api_requests_reset_at?: Date;
  }): Promise<boolean> {
    const query = `
      UPDATE user_subscriptions
      SET
        api_requests_used = $2,
        api_requests_reset_at = COALESCE($3, api_requests_reset_at),
        updated_at = NOW()
      WHERE id = $1
    `;

    const result = await this.getPool().query(query, [
      subscriptionId,
      usageUpdate.api_requests_used,
      usageUpdate.api_requests_reset_at
    ]);

    return (result.rowCount || 0) > 0;
  }

  // =============================================================================
  // ORDERS MANAGEMENT
  // =============================================================================

  /**
   * Create order
   */
  async createOrder(orderData: CreateOrderRequest & {
    user_id: string;
    order_number: string;
    status?: string;
    total_amount: number;
    currency?: string;
    stripe_payment_intent_id?: string;
    stripe_invoice_id?: string;
    metadata?: Record<string, any>;
  }): Promise<Order> {
    const query = `
      INSERT INTO orders (
        user_id, subscription_id, order_number, status,
        subtotal, tax_amount, discount_amount, total_amount, currency,
        stripe_payment_intent_id, stripe_invoice_id,
        billing_address, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `;

    const values = [
      orderData.user_id,
      orderData.subscription_id,
      orderData.order_number,
      orderData.status || 'pending',
      orderData.subtotal,
      orderData.tax_amount || 0,
      orderData.discount_amount || 0,
      orderData.total_amount,
      orderData.currency || 'EUR',
      orderData.stripe_payment_intent_id,
      orderData.stripe_invoice_id,
      orderData.billing_address ? JSON.stringify(orderData.billing_address) : null,
      orderData.metadata ? JSON.stringify(orderData.metadata) : null
    ];

    const result = await this.getPool().query(query, values);
    return result.rows[0];
  }

  /**
   * Get user orders
   */
  async getUserOrders(userId: string, limit: number = 50): Promise<Order[]> {
    const result = await this.getPool().query(
      'SELECT * FROM orders WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2',
      [userId, limit]
    );

    return result.rows;
  }

  /**
   * Get order by ID
   */
  async getOrderById(orderId: string): Promise<Order | null> {
    const result = await this.getPool().query(
      'SELECT * FROM orders WHERE id = $1',
      [orderId]
    );

    return result.rows[0] || null;
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId: string, status: string, completedAt?: Date): Promise<Order | null> {
    const query = `
      UPDATE orders
      SET
        status = $2,
        completed_at = $3,
        updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `;

    const result = await this.getPool().query(query, [orderId, status, completedAt]);
    return result.rows[0] || null;
  }

  // =============================================================================
  // AUDIT LOGGING
  // =============================================================================

  /**
   * Log subscription audit event
   */
  async logAuditEvent(auditData: {
    user_id?: string;
    subscription_id?: string;
    order_id?: string;
    event_type: string;
    event_description: string;
    old_values?: any;
    new_values?: any;
    performed_by?: string;
    ip_address?: string;
    user_agent?: string | undefined;
  }): Promise<SubscriptionAuditLog> {
    const query = `
      INSERT INTO subscription_audit_log (
        user_id, subscription_id, order_id,
        event_type, event_description,
        old_values, new_values,
        performed_by, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;

    const values = [
      auditData.user_id,
      auditData.subscription_id,
      auditData.order_id,
      auditData.event_type,
      auditData.event_description,
      auditData.old_values ? JSON.stringify(auditData.old_values) : null,
      auditData.new_values ? JSON.stringify(auditData.new_values) : null,
      auditData.performed_by,
      auditData.ip_address,
      auditData.user_agent
    ];

    const result = await this.getPool().query(query, values);
    return result.rows[0];
  }

  /**
   * Get audit logs for user
   */
  async getUserAuditLogs(userId: string, limit: number = 100): Promise<SubscriptionAuditLog[]> {
    const result = await this.getPool().query(
      'SELECT * FROM subscription_audit_log WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2',
      [userId, limit]
    );

    return result.rows;
  }

  /**
   * Get audit logs for subscription
   */
  async getSubscriptionAuditLogs(subscriptionId: string, limit: number = 100): Promise<SubscriptionAuditLog[]> {
    const result = await this.getPool().query(
      'SELECT * FROM subscription_audit_log WHERE subscription_id = $1 ORDER BY created_at DESC LIMIT $2',
      [subscriptionId, limit]
    );

    return result.rows;
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Generate unique order number
   */
  async generateOrderNumber(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD-${timestamp}-${random}`;
  }

  /**
   * Check if user can upgrade/downgrade to plan
   */
  async canChangePlan(userId: string, newPlanId: string): Promise<{ canChange: boolean; reason?: string }> {
    const currentSubscription = await this.getUserActiveSubscription(userId);
    const newPlan = await this.getPlanById(newPlanId);

    if (!newPlan) {
      return { canChange: false, reason: 'Plan not found' };
    }

    if (!newPlan.is_active) {
      return { canChange: false, reason: 'Plan is not active' };
    }

    if (!currentSubscription) {
      return { canChange: true };
    }

    if (currentSubscription.plan_id === newPlanId) {
      return { canChange: false, reason: 'Already subscribed to this plan' };
    }

    // Add business logic for plan change restrictions here
    return { canChange: true };
  }

  /**
   * Get subscription statistics
   */
  async getSubscriptionStats(): Promise<{
    total_active_subscriptions: number;
    total_revenue_monthly: number;
    plans_distribution: Array<{ plan_name: string; count: number }>;
  }> {
    const [activeSubsResult, revenueResult, distributionResult] = await Promise.all([
      this.getPool().query("SELECT COUNT(*) as count FROM user_subscriptions WHERE status = 'active'"),
      this.getPool().query(`
        SELECT SUM(sp.price_eur) as total
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'active' AND sp.billing_interval = 'monthly'
      `),
      this.getPool().query(`
        SELECT sp.name as plan_name, COUNT(*) as count
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'active'
        GROUP BY sp.name
        ORDER BY count DESC
      `)
    ]);

    return {
      total_active_subscriptions: parseInt(activeSubsResult.rows[0].count),
      total_revenue_monthly: parseFloat(revenueResult.rows[0].total || '0'),
      plans_distribution: distributionResult.rows
    };
  }

  // =============================================================================
  // ADMIN-SPECIFIC METHODS
  // =============================================================================

  /**
   * Get subscriptions with advanced filtering for admin
   */
  async getSubscriptionsForAdmin(options: {
    page: number;
    limit: number;
    status?: string;
    planId?: string;
    userId?: string;
    expiringInDays?: number;
    createdAfter?: Date;
    createdBefore?: Date;
    updatedAfter?: Date;
    updatedBefore?: Date;
    stripeCustomerId?: string;
    cancelAtPeriodEnd?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    subscriptions: any[];
    pagination: any;
    summary?: any;
  }> {
    const {
      page,
      limit,
      status,
      planId,
      userId,
      expiringInDays,
      createdAfter,
      createdBefore,
      updatedAfter,
      updatedBefore,
      stripeCustomerId,
      cancelAtPeriodEnd,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options;

    const offset = (page - 1) * limit;
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (status) {
      conditions.push(`s.status = $${paramIndex++}`);
      params.push(status);
    }

    if (planId) {
      conditions.push(`s.plan_id = $${paramIndex++}`);
      params.push(planId);
    }

    if (userId) {
      conditions.push(`s.user_id = $${paramIndex++}`);
      params.push(userId);
    }

    if (expiringInDays) {
      conditions.push(`s.current_period_end <= NOW() + INTERVAL '${expiringInDays} days'`);
    }

    if (createdAfter) {
      conditions.push(`s.created_at >= $${paramIndex++}`);
      params.push(createdAfter);
    }

    if (createdBefore) {
      conditions.push(`s.created_at <= $${paramIndex++}`);
      params.push(createdBefore);
    }

    if (updatedAfter) {
      conditions.push(`s.updated_at >= $${paramIndex++}`);
      params.push(updatedAfter);
    }

    if (updatedBefore) {
      conditions.push(`s.updated_at <= $${paramIndex++}`);
      params.push(updatedBefore);
    }

    if (stripeCustomerId) {
      conditions.push(`s.stripe_customer_id = $${paramIndex++}`);
      params.push(stripeCustomerId);
    }

    if (cancelAtPeriodEnd !== undefined) {
      conditions.push(`s.cancel_at_period_end = $${paramIndex++}`);
      params.push(cancelAtPeriodEnd);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*)
      FROM user_subscriptions s
      JOIN users u ON s.user_id = u.id
      JOIN subscription_plans sp ON s.plan_id = sp.id
      ${whereClause}
    `;
    const countResult = await this.getPool().query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // Get subscriptions with user and plan details
    const subscriptionsQuery = `
      SELECT
        s.*,
        u.email as user_email,
        u.role as user_role,
        u.created_at as user_created_at,
        sp.name as plan_name,
        sp.price_eur,
        sp.currency,
        sp.billing_interval,
        sp.features
      FROM user_subscriptions s
      JOIN users u ON s.user_id = u.id
      JOIN subscription_plans sp ON s.plan_id = sp.id
      ${whereClause}
      ORDER BY s.${sortBy} ${sortOrder.toUpperCase()}
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;
    params.push(limit, offset);

    const subscriptionsResult = await this.getPool().query(subscriptionsQuery, params);

    // Get summary statistics
    const summaryQuery = `
      SELECT
        COUNT(*) FILTER (WHERE s.status = 'active') as total_active,
        COUNT(*) FILTER (WHERE s.status = 'past_due') as total_past_due,
        COUNT(*) FILTER (WHERE s.status = 'canceled') as total_canceled,
        COUNT(*) FILTER (WHERE s.current_period_end <= NOW() + INTERVAL '7 days' AND s.status = 'active') as expiring_soon,
        SUM(sp.price_eur) FILTER (WHERE s.status = 'active') as revenue_this_month
      FROM user_subscriptions s
      JOIN subscription_plans sp ON s.plan_id = sp.id
      ${whereClause}
    `;
    const summaryResult = await this.getPool().query(summaryQuery, params.slice(0, -2));

    return {
      subscriptions: subscriptionsResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      summary: summaryResult.rows[0]
    };
  }

  /**
   * Get detailed subscription information for admin
   */
  async getSubscriptionDetailsForAdmin(subscriptionId: string): Promise<any | null> {
    const query = `
      SELECT
        s.*,
        u.email as user_email,
        u.role as user_role,
        u.is_active as user_active,
        u.created_at as user_created_at,
        sp.name as plan_name,
        sp.price_eur,
        sp.currency,
        sp.billing_interval,
        sp.features
      FROM user_subscriptions s
      JOIN users u ON s.user_id = u.id
      JOIN subscription_plans sp ON s.plan_id = sp.id
      WHERE s.id = $1
    `;

    const result = await this.getPool().query(query, [subscriptionId]);
    if (result.rows.length === 0) return null;

    const subscription = result.rows[0];

    // Get related orders
    const ordersQuery = `
      SELECT * FROM orders
      WHERE subscription_id = $1
      ORDER BY created_at DESC
      LIMIT 10
    `;
    const ordersResult = await this.getPool().query(ordersQuery, [subscriptionId]);

    // Get audit log
    const auditQuery = `
      SELECT * FROM subscription_audit_log
      WHERE subscription_id = $1
      ORDER BY created_at DESC
      LIMIT 10
    `;
    const auditResult = await this.getPool().query(auditQuery, [subscriptionId]);

    return {
      ...subscription,
      orders: ordersResult.rows,
      audit_log: auditResult.rows
    };
  }

  /**
   * Update subscription status manually (admin)
   */
  async updateSubscriptionStatus(
    subscriptionId: string,
    status: string,
    options: {
      reason: string;
      notes?: string;
      adminId: string;
    }
  ): Promise<any> {
    const client = await this.getPool().connect();
    try {
      await client.query('BEGIN');

      // Update subscription status
      const updateQuery = `
        UPDATE user_subscriptions
        SET status = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `;
      const result = await client.query(updateQuery, [status, subscriptionId]);

      if (result.rows.length === 0) {
        throw new Error('Subscription not found');
      }

      // Log the change
      await client.query(`
        INSERT INTO subscription_audit_log (
          subscription_id, changed_by_user_id, change_type,
          old_values, new_values, reason, admin_notes, created_at
        ) VALUES ($1, $2, 'status_changed', $3, $4, $5, $6, NOW())
      `, [
        subscriptionId,
        options.adminId,
        JSON.stringify({ status: result.rows[0].status }),
        JSON.stringify({ status }),
        options.reason,
        options.notes
      ]);

      await client.query('COMMIT');
      return result.rows[0];
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Extend subscription period (admin)
   */
  async extendSubscription(
    subscriptionId: string,
    days: number,
    options: {
      reason: string;
      notes?: string;
      adminId: string;
    }
  ): Promise<any> {
    const client = await this.getPool().connect();
    try {
      await client.query('BEGIN');

      // Get current subscription
      const currentResult = await client.query(
        'SELECT * FROM user_subscriptions WHERE id = $1',
        [subscriptionId]
      );

      if (currentResult.rows.length === 0) {
        throw new Error('Subscription not found');
      }

      const current = currentResult.rows[0];
      const newEndDate = new Date(current.current_period_end);
      newEndDate.setDate(newEndDate.getDate() + days);

      // Update subscription
      const updateQuery = `
        UPDATE user_subscriptions
        SET current_period_end = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `;
      const result = await client.query(updateQuery, [newEndDate, subscriptionId]);

      // Log the change
      await client.query(`
        INSERT INTO subscription_audit_log (
          subscription_id, changed_by_user_id, change_type,
          old_values, new_values, reason, admin_notes, created_at
        ) VALUES ($1, $2, 'period_extended', $3, $4, $5, $6, NOW())
      `, [
        subscriptionId,
        options.adminId,
        JSON.stringify({ current_period_end: current.current_period_end }),
        JSON.stringify({ current_period_end: newEndDate, days_extended: days }),
        options.reason,
        options.notes
      ]);

      await client.query('COMMIT');
      return result.rows[0];
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Bulk subscription operations (admin)
   */
  async bulkSubscriptionAction(
    action: 'suspend' | 'reactivate' | 'cancel' | 'extend',
    subscriptionIds: string[],
    options: {
      reason: string;
      notes?: string;
      adminId: string;
      days?: number;
    }
  ): Promise<{ success: number; failed: number; errors: any[] }> {
    const results = { success: 0, failed: 0, errors: [] as any[] };

    for (const subscriptionId of subscriptionIds) {
      try {
        switch (action) {
          case 'suspend':
            await this.updateSubscriptionStatus(subscriptionId, 'past_due', options);
            break;
          case 'reactivate':
            await this.updateSubscriptionStatus(subscriptionId, 'active', options);
            break;
          case 'cancel':
            await this.updateSubscriptionStatus(subscriptionId, 'canceled', options);
            break;
          case 'extend':
            if (options.days) {
              await this.extendSubscription(subscriptionId, options.days, options);
            }
            break;
        }
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          subscriptionId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Get subscription audit log with pagination
   */
  async getSubscriptionAuditLog(
    subscriptionId: string,
    options: {
      page: number;
      limit: number;
      changeType?: string;
    }
  ): Promise<{
    entries: any[];
    pagination: any;
  }> {
    const { page, limit, changeType } = options;
    const offset = (page - 1) * limit;

    const conditions: string[] = ['subscription_id = $1'];
    const params: any[] = [subscriptionId];
    let paramIndex = 2;

    if (changeType) {
      conditions.push(`change_type = $${paramIndex++}`);
      params.push(changeType);
    }

    const whereClause = `WHERE ${conditions.join(' AND ')}`;

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM subscription_audit_log ${whereClause}`;
    const countResult = await this.getPool().query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // Get entries
    const entriesQuery = `
      SELECT sal.*, u.email as changed_by_email
      FROM subscription_audit_log sal
      LEFT JOIN users u ON sal.changed_by_user_id = u.id
      ${whereClause}
      ORDER BY sal.created_at DESC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;
    params.push(limit, offset);

    const entriesResult = await this.getPool().query(entriesQuery, params);

    return {
      entries: entriesResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Get expiring subscriptions
   */
  async getExpiringSubscriptions(options: {
    days: number;
    page: number;
    limit: number;
  }): Promise<{
    subscriptions: any[];
    pagination: any;
  }> {
    const { days, page, limit } = options;
    const offset = (page - 1) * limit;

    // Get total count
    const countQuery = `
      SELECT COUNT(*)
      FROM user_subscriptions s
      WHERE s.status = 'active'
      AND s.current_period_end <= NOW() + INTERVAL '${days} days'
      AND s.current_period_end > NOW()
    `;
    const countResult = await this.getPool().query(countQuery);
    const total = parseInt(countResult.rows[0].count);

    // Get subscriptions
    const subscriptionsQuery = `
      SELECT
        s.*,
        u.email as user_email,
        sp.name as plan_name,
        sp.price_eur,
        sp.currency
      FROM user_subscriptions s
      JOIN users u ON s.user_id = u.id
      JOIN subscription_plans sp ON s.plan_id = sp.id
      WHERE s.status = 'active'
      AND s.current_period_end <= NOW() + INTERVAL '${days} days'
      AND s.current_period_end > NOW()
      ORDER BY s.current_period_end ASC
      LIMIT $1 OFFSET $2
    `;
    const subscriptionsResult = await this.getPool().query(subscriptionsQuery, [limit, offset]);

    return {
      subscriptions: subscriptionsResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Get comprehensive subscription statistics for admin
   */
  async getSubscriptionStatistics(): Promise<any> {
    const query = `
      SELECT
        COUNT(*) as total_subscriptions,
        COUNT(*) FILTER (WHERE status = 'active') as active_subscriptions,
        COUNT(*) FILTER (WHERE status = 'past_due') as past_due_subscriptions,
        COUNT(*) FILTER (WHERE status = 'canceled') as canceled_subscriptions,
        COUNT(*) FILTER (WHERE status = 'trialing') as trial_subscriptions,
        COUNT(*) FILTER (WHERE cancel_at_period_end = true) as pending_cancellations,
        COUNT(*) FILTER (WHERE current_period_end <= NOW() + INTERVAL '7 days' AND status = 'active') as expiring_soon,
        COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_subscriptions_30d,
        AVG(EXTRACT(EPOCH FROM (current_period_end - current_period_start)) / 86400) as avg_subscription_length_days
      FROM user_subscriptions
    `;

    const revenueQuery = `
      SELECT
        SUM(sp.price_eur) FILTER (WHERE s.status = 'active') as monthly_recurring_revenue,
        SUM(sp.price_eur) FILTER (WHERE s.status = 'active' AND sp.billing_interval = 'yearly') / 12 as yearly_mrr_contribution
      FROM user_subscriptions s
      JOIN subscription_plans sp ON s.plan_id = sp.id
    `;

    const [statsResult, revenueResult] = await Promise.all([
      this.getPool().query(query),
      this.getPool().query(revenueQuery)
    ]);

    return {
      ...statsResult.rows[0],
      ...revenueResult.rows[0]
    };
  }
}
