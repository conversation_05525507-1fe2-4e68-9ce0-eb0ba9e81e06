/**
 * API Key Service
 * 
 * Handles user-facing API key management including creation, listing,
 * updating, and revocation. Integrates with subscription system for limits.
 */

import { Pool } from 'pg';
import { getDatabasePool } from '../database/connection';
import { generateApiKey, hashApi<PERSON>ey } from '../utils/crypto';
import { SubscriptionService } from './SubscriptionService';

// Types for API Key management
export interface ApiKeyData {
  id: string;
  name: string;
  description?: string;
  key_preview: string; // First 8 chars + "..." for security
  total_requests: number;
  requests_this_month: number;
  last_reset_date: Date;
  rate_limit_per_minute?: number;
  rate_limit_per_day?: number;
  rate_limit_burst?: number;
  allowed_ips?: string[];
  allowed_domains?: string[];
  last_used_at?: Date;
  last_used_ip?: string;
  is_active: boolean;
  expires_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CreateApiKeyRequest {
  name: string;
  description?: string;
  allowed_ips?: string[];
  allowed_domains?: string[];
  expires_at?: Date;
}

export interface UpdateApiKeyRequest {
  name?: string;
  description?: string;
  allowed_ips?: string[];
  allowed_domains?: string[];
  is_active?: boolean;
  expires_at?: Date;
}

export interface ApiKeyWithSecret {
  apiKey: ApiKeyData;
  secret: string; // Only returned on creation/regeneration
}

export interface ApiKeyUsageStats {
  total_requests: number;
  requests_this_month: number;
  requests_today: number;
  last_used_at?: Date;
  rate_limit_per_minute?: number;
  rate_limit_per_day?: number;
  usage_percentage_monthly: number;
  usage_percentage_daily: number;
}

export class ApiKeyService {
  private pool: Pool | null = null;
  private subscriptionService?: SubscriptionService;

  private getPool(): Pool {
    if (!this.pool) {
      this.pool = getDatabasePool();
    }
    return this.pool;
  }

  private getSubscriptionService(): SubscriptionService {
    if (!this.subscriptionService) {
      this.subscriptionService = new SubscriptionService();
    }
    return this.subscriptionService;
  }

  // =============================================================================
  // USER API KEY MANAGEMENT
  // =============================================================================

  /**
   * Get user's API keys
   */
  async getUserApiKeys(userId: string): Promise<ApiKeyData[]> {
    const query = `
      SELECT 
        id, name, description,
        CONCAT(LEFT(key_hash, 8), '...') as key_preview,
        total_requests, requests_this_month, last_reset_date,
        rate_limit_per_minute, rate_limit_per_day, rate_limit_burst,
        allowed_ips, allowed_domains,
        last_used_at, last_used_ip,
        is_active, expires_at, created_at, updated_at
      FROM api_keys
      WHERE user_id = $1
      ORDER BY created_at DESC
    `;

    const result = await this.getPool().query(query, [userId]);
    return result.rows;
  }

  /**
   * Get API key by ID (user must own it)
   */
  async getUserApiKeyById(userId: string, keyId: string): Promise<ApiKeyData | null> {
    const query = `
      SELECT 
        id, name, description,
        CONCAT(LEFT(key_hash, 8), '...') as key_preview,
        total_requests, requests_this_month, last_reset_date,
        rate_limit_per_minute, rate_limit_per_day, rate_limit_burst,
        allowed_ips, allowed_domains,
        last_used_at, last_used_ip,
        is_active, expires_at, created_at, updated_at
      FROM api_keys
      WHERE user_id = $1 AND id = $2
    `;

    const result = await this.getPool().query(query, [userId, keyId]);
    return result.rows[0] || null;
  }

  /**
   * Create new API key for user
   */
  async createApiKey(userId: string, keyData: CreateApiKeyRequest): Promise<ApiKeyWithSecret> {
    // Check subscription limits
    const subscription = await this.getSubscriptionService().getUserActiveSubscription(userId);
    if (!subscription) {
      throw new Error('No active subscription found');
    }

    const plan = await this.getSubscriptionService().getPlanById(subscription.plan_id);
    if (!plan) {
      throw new Error('Subscription plan not found');
    }

    // Check current API key count
    const existingKeys = await this.getUserApiKeys(userId);
    const activeKeys = existingKeys.filter(key => key.is_active);
    
    if (activeKeys.length >= plan.max_api_keys) {
      throw new Error(`Maximum API keys limit reached (${plan.max_api_keys})`);
    }

    // Generate API key
    const apiKeySecret = generateApiKey();
    const keyHash = hashApiKey(apiKeySecret);

    const query = `
      INSERT INTO api_keys (
        user_id, created_by_user_id, key_hash, name, description,
        rate_limit_per_minute, rate_limit_per_day, rate_limit_burst,
        allowed_ips, allowed_domains, expires_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING 
        id, name, description,
        CONCAT(LEFT(key_hash, 8), '...') as key_preview,
        total_requests, requests_this_month, last_reset_date,
        rate_limit_per_minute, rate_limit_per_day, rate_limit_burst,
        allowed_ips, allowed_domains,
        last_used_at, last_used_ip,
        is_active, expires_at, created_at, updated_at
    `;

    const values = [
      userId, // user_id
      userId, // created_by_user_id (self-created)
      keyHash,
      keyData.name,
      keyData.description || null,
      plan.api_requests_per_minute, // Use plan limits
      plan.rate_limit_per_day,
      plan.rate_limit_burst,
      keyData.allowed_ips || null,
      keyData.allowed_domains || null,
      keyData.expires_at || null
    ];

    const result = await this.getPool().query(query, values);
    const apiKeyData = result.rows[0];

    return {
      apiKey: apiKeyData,
      secret: apiKeySecret
    };
  }

  /**
   * Update API key
   */
  async updateApiKey(userId: string, keyId: string, updates: UpdateApiKeyRequest): Promise<ApiKeyData | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic UPDATE query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        setParts.push(`${key} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (setParts.length === 0) {
      return this.getUserApiKeyById(userId, keyId);
    }

    setParts.push(`updated_at = NOW()`);
    values.push(userId, keyId);

    const query = `
      UPDATE api_keys 
      SET ${setParts.join(', ')}
      WHERE user_id = $${paramIndex} AND id = $${paramIndex + 1}
      RETURNING 
        id, name, description,
        CONCAT(LEFT(key_hash, 8), '...') as key_preview,
        total_requests, requests_this_month, last_reset_date,
        rate_limit_per_minute, rate_limit_per_day, rate_limit_burst,
        allowed_ips, allowed_domains,
        last_used_at, last_used_ip,
        is_active, expires_at, created_at, updated_at
    `;

    const result = await this.getPool().query(query, values);
    return result.rows[0] || null;
  }

  /**
   * Revoke (deactivate) API key
   */
  async revokeApiKey(userId: string, keyId: string): Promise<boolean> {
    const result = await this.getPool().query(
      'UPDATE api_keys SET is_active = false, updated_at = NOW() WHERE user_id = $1 AND id = $2',
      [userId, keyId]
    );
    
    return (result.rowCount || 0) > 0;
  }

  /**
   * Regenerate API key (creates new secret, keeps same ID and settings)
   */
  async regenerateApiKey(userId: string, keyId: string): Promise<ApiKeyWithSecret | null> {
    // Verify user owns the key
    const existingKey = await this.getUserApiKeyById(userId, keyId);
    if (!existingKey) {
      return null;
    }

    // Generate new API key secret
    const apiKeySecret = generateApiKey();
    const keyHash = hashApiKey(apiKeySecret);

    const query = `
      UPDATE api_keys 
      SET key_hash = $1, updated_at = NOW()
      WHERE user_id = $2 AND id = $3
      RETURNING 
        id, name, description,
        CONCAT(LEFT(key_hash, 8), '...') as key_preview,
        total_requests, requests_this_month, last_reset_date,
        rate_limit_per_minute, rate_limit_per_day, rate_limit_burst,
        allowed_ips, allowed_domains,
        last_used_at, last_used_ip,
        is_active, expires_at, created_at, updated_at
    `;

    const result = await this.getPool().query(query, [keyHash, userId, keyId]);
    const apiKeyData = result.rows[0];

    return {
      apiKey: apiKeyData,
      secret: apiKeySecret
    };
  }

  /**
   * Get API key usage statistics
   */
  async getApiKeyUsage(userId: string, keyId: string): Promise<ApiKeyUsageStats | null> {
    const query = `
      SELECT 
        total_requests,
        requests_this_month,
        rate_limit_per_minute,
        rate_limit_per_day,
        last_used_at,
        -- Calculate today's usage from usage_analytics table
        COALESCE((
          SELECT SUM(request_count)
          FROM usage_analytics 
          WHERE api_key_id = $2 
          AND DATE(created_at) = CURRENT_DATE
        ), 0) as requests_today
      FROM api_keys
      WHERE user_id = $1 AND id = $2
    `;

    const result = await this.getPool().query(query, [userId, keyId]);
    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    
    // Calculate usage percentages
    const monthlyLimit = row.rate_limit_per_day * 30; // Approximate monthly limit
    const dailyLimit = row.rate_limit_per_day;
    
    return {
      total_requests: row.total_requests,
      requests_this_month: row.requests_this_month,
      requests_today: row.requests_today,
      last_used_at: row.last_used_at,
      rate_limit_per_minute: row.rate_limit_per_minute,
      rate_limit_per_day: row.rate_limit_per_day,
      usage_percentage_monthly: monthlyLimit > 0 ? (row.requests_this_month / monthlyLimit) * 100 : 0,
      usage_percentage_daily: dailyLimit > 0 ? (row.requests_today / dailyLimit) * 100 : 0
    };
  }

  /**
   * Delete API key permanently
   */
  async deleteApiKey(userId: string, keyId: string): Promise<boolean> {
    const result = await this.getPool().query(
      'DELETE FROM api_keys WHERE user_id = $1 AND id = $2',
      [userId, keyId]
    );
    
    return (result.rowCount || 0) > 0;
  }
}
