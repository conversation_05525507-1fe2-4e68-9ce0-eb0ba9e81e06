import { writeFileSync, existsSync, statSync, mkdirSync, readdirSync, unlinkSync } from 'fs';
import { dirname, join } from 'path';
import { CONFIG, FILE_DOWNLOAD_CONFIG } from '../config';
import { FastifyBaseLogger } from 'fastify';

export interface DownloadOptions {
  url: string;
  filePath: string;
  headers?: Record<string, string>;
  timeout?: number;
  maxRetries?: number;
  forceDownload?: boolean;
}

export interface DownloadResult {
  success: boolean;
  filePath: string;
  fileSize: number;
  downloadTime: number;
  fromCache: boolean;
  error?: string;
}

export class FileDownloader {
  private readonly defaultTimeout = FILE_DOWNLOAD_CONFIG.timeout; // Configurable timeout
  private readonly defaultMaxRetries = FILE_DOWNLOAD_CONFIG.maxRetries; // Configurable max retries
  private logger: FastifyBaseLogger | null = null;

  setLogger(logger: FastifyBaseLogger) {
    this.logger = logger;
  }

  private log(level: 'info' | 'warn' | 'error', message: string, ...args: any[]) {
    if (this.logger) {
      this.logger[level](message, ...args);
    }
  }

  async downloadFile(options: DownloadOptions): Promise<DownloadResult> {
    const startTime = Date.now();
    const {
      url,
      filePath,
      headers = {},
      timeout = this.defaultTimeout,
      maxRetries = this.defaultMaxRetries,
      forceDownload = false
    } = options;

    this.log('info', `📥 Downloading: ${url} -> ${filePath}`);

    // Check if file already exists and is recent (unless force download)
    if (!forceDownload && existsSync(filePath)) {
      const stats = statSync(filePath);
      const ageDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
      const cacheDurationDays = CONFIG.cache.fileCacheDurationDays;

      if (ageDays < cacheDurationDays) {
        this.log('info', `✅ Using cached file (${ageDays.toFixed(1)} days old): ${filePath}`);
        return {
          success: true,
          filePath,
          fileSize: stats.size,
          downloadTime: Date.now() - startTime,
          fromCache: true
        };
      } else {
        this.log('info', `🔄 Cache expired (${ageDays.toFixed(1)} days old, max: ${cacheDurationDays} days): ${filePath}`);
      }
    }

    // Ensure directory exists
    const dir = dirname(filePath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.log('info', `🔄 Download attempt ${attempt}/${maxRetries}...`);

        // Use dynamic import for node-fetch
        const { default: fetch } = await import('node-fetch');
        
        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
          const response = await fetch(url, {
            headers: {
              'User-Agent': FILE_DOWNLOAD_CONFIG.userAgent,
              ...headers
            },
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          if (!response.body) {
            throw new Error('No response body');
          }

          // Convert stream to buffer
          const buffer = await response.arrayBuffer();
          
          // Write to file
          writeFileSync(filePath, Buffer.from(buffer));
          
          // Verify file was written
          const stats = statSync(filePath);
          const downloadTime = Date.now() - startTime;
          
          this.log('info', `✅ Downloaded: ${stats.size} bytes in ${downloadTime}ms`);
          
          return {
            success: true,
            filePath,
            fileSize: stats.size,
            downloadTime,
            fromCache: false
          };

        } finally {
          clearTimeout(timeoutId);
        }

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < maxRetries) {
          this.log('warn', `⚠️  Attempt ${attempt} failed: ${lastError.message}`);
          
          // Exponential backoff with configurable base delay
          const delay = Math.pow(2, attempt - 1) * FILE_DOWNLOAD_CONFIG.retryBaseDelay;
          this.log('info', `⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    const error = `Failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`;
    this.log('error', `❌ Download failed: ${error}`);
    
    return {
      success: false,
      filePath,
      fileSize: 0,
      downloadTime: Date.now() - startTime,
      fromCache: false,
      error
    };
  }

  async downloadMultiple(downloads: DownloadOptions[]): Promise<DownloadResult[]> {
    this.log('info', `📦 Starting batch download of ${downloads.length} files...`);
    
    const results: DownloadResult[] = [];
    
    for (const download of downloads) {
      const result = await this.downloadFile(download);
      results.push(result);
      
      if (!result.success) {
        this.log('error', `❌ Failed to download ${download.url}: ${result.error}`);
      }
    }

    const successful = results.filter(r => r.success).length;
    this.log('info', `📊 Batch download complete: ${successful}/${downloads.length} successful`);
    
    return results;
  }

  async cleanOldFiles(directory: string, maxAge: number = 7): Promise<void> {
    try {
      if (!existsSync(directory)) {
        return;
      }

      const files = readdirSync(directory);
      const cutoffTime = Date.now() - (maxAge * 24 * 60 * 60 * 1000);
      
      let cleaned = 0;
      
      for (const file of files) {
        const filePath = join(directory, file);
        const stats = statSync(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          unlinkSync(filePath);
          const ageDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
          this.log('info', `🗑️  Cleaned old file: ${file} (${ageDays.toFixed(1)} days old)`);
          cleaned++;
        }
      }
      
      this.log('info', `🧹 Cleaned ${cleaned} old files from ${directory}`);
      
    } catch (error) {
      this.log('error', `Failed to clean old files from ${directory}:`, error);
    }
  }
}

export const fileDownloader = new FileDownloader();
