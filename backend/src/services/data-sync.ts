import cron from 'node-cron';
import { LPExpressDataCollector } from './data-collection/lp-express';
import { OmnivaDataCollector } from './data-collection/omniva';
import { DPDDataCollector } from './data-collection/dpd';
import { VenipakDataCollector } from './data-collection/venipak';
import { TerminalService } from './terminal-service';
import { TerminalImportType } from '../validation/schemas';
import { config, SYNC_CONFIG } from '../config';
import { getNotificationService } from './notification-service';
import { FastifyBaseLogger } from 'fastify';

interface SyncResult {
  provider: string;
  success: boolean;
  terminalCount: number;
  errors: string[];
  duration: number;
}

interface SyncStats {
  lastSync: Date;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  lastResults: SyncResult[];
}

export class DataSynchronizationService {
  private lpExpressCollector: LPExpressDataCollector;
  private omnivaCollector: OmnivaDataCollector;
  private dpdCollector: DPDDataCollector;
  private venipakCollector: VenipakDataCollector;
  private terminalService: TerminalService;
  private stats: SyncStats;
  private isRunning = false;
  private logger: FastifyBaseLogger | null = null;
  private notificationService = getNotificationService();

  constructor() {
    this.lpExpressCollector = new LPExpressDataCollector();
    this.omnivaCollector = new OmnivaDataCollector();
    this.dpdCollector = new DPDDataCollector();
    this.venipakCollector = new VenipakDataCollector();
    this.terminalService = new TerminalService();
    
    this.stats = {
      lastSync: new Date(0),
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastResults: []
    };
  }

  setLogger(logger: FastifyBaseLogger) {
    this.logger = logger;
    this.notificationService.setLogger(logger);
    
    // Set logger for all collectors if they support it
    if ('setLogger' in this.terminalService && typeof this.terminalService.setLogger === 'function') {
      this.terminalService.setLogger(logger);
    }
    if ('setLogger' in this.lpExpressCollector && typeof this.lpExpressCollector.setLogger === 'function') {
      this.lpExpressCollector.setLogger(logger);
    }
    if ('setLogger' in this.omnivaCollector && typeof this.omnivaCollector.setLogger === 'function') {
      this.omnivaCollector.setLogger(logger);
    }
    if ('setLogger' in this.dpdCollector && typeof this.dpdCollector.setLogger === 'function') {
      this.dpdCollector.setLogger(logger);
    }
    if ('setLogger' in this.venipakCollector && typeof this.venipakCollector.setLogger === 'function') {
      this.venipakCollector.setLogger(logger);
    }
  }

  private log(level: 'info' | 'warn' | 'error', message: string, ...args: any[]) {
    if (this.logger) {
      this.logger[level](message, ...args);
    }
  }

  async startScheduler(): Promise<void> {
    const syncSchedule = config.dataSyncSchedule || '0 2 * * *'; // Default: 2 AM daily
    
    this.log('info', `🕐 Starting data synchronization scheduler: ${syncSchedule}`);
    
    // Schedule automatic sync
    cron.schedule(syncSchedule, async () => {
      this.log('info', '⏰ Scheduled data synchronization starting...');
      await this.syncAllProviders();
    }, {
      scheduled: true,
      timezone: 'Europe/Vilnius'
    });

    // Run initial sync if configured
    if (config.runInitialSync) {
      this.log('info', '🚀 Running initial data synchronization...');
      setTimeout(() => this.syncAllProviders(), SYNC_CONFIG.initialDelay); // Configurable initial delay
    }
  }

  async syncAllProviders(): Promise<SyncStats> {
    if (this.isRunning) {
      this.log('warn', '⚠️  Data sync already in progress, skipping...');
      return this.stats;
    }

    this.isRunning = true;
    const syncStartTime = Date.now();
    const results: SyncResult[] = [];

    this.log('info', '🔄 Starting data synchronization for all providers...');

    try {
      // Check if any files need updating before proceeding (if smart sync enabled)
      if (config.enableSmartSync && config.skipSyncWhenNoChanges) {
        const filesChanged = await this.checkForFileChanges();
        
        if (!filesChanged) {
          this.log('info', '📁 All files are cached and fresh - skipping sync to avoid unnecessary database operations');
          this.log('info', `⚡ Smart sync completed in ${Date.now() - syncStartTime}ms (no changes detected)`);
          
          // Return success but with cached results
          return {
            ...this.stats,
            lastSync: new Date()
          };
        }
      }

      this.log('info', '📥 File changes detected - proceeding with full sync...');

      // Sync LP Express
      const lpResult = await this.syncProvider('LP_EXPRESS', async () => {
        return await this.lpExpressCollector.collectAndValidate();
      });
      results.push(lpResult);

      // Sync Omniva
      const omnivaResult = await this.syncProvider('OMNIVA', async () => {
        return await this.omnivaCollector.collectAndValidate();
      });
      results.push(omnivaResult);

      // Sync DPD (with longer timeout due to geocoding)
      const dpdResult = await this.syncProvider('DPD', async () => {
        return await this.dpdCollector.collectAndValidate();
      });
      results.push(dpdResult);

      // Sync Venipak
      const venipakResult = await this.syncProvider('VENIPAK', async () => {
        return await this.venipakCollector.collectAndValidate();
      });
      results.push(venipakResult);

      // Update statistics
      this.updateStats(results);

      const totalDuration = Date.now() - syncStartTime;
      const successfulProviders = results.filter(r => r.success).length;
      const totalTerminals = results.reduce((sum, r) => sum + r.terminalCount, 0);

      this.log('info', `✅ Data synchronization completed in ${totalDuration}ms`);
      this.log('info', `📊 Results: ${successfulProviders}/${results.length} providers successful, ${totalTerminals} terminals processed`);

      // Notify on significant failures
      const failedProviders = results.filter(r => !r.success);
      if (failedProviders.length > 0) {
        for (const failed of failedProviders) {
          await this.notificationService.notifyDataSyncFailure(
            failed.provider,
            failed.errors.join('; '),
            { duration: failed.duration, terminalCount: failed.terminalCount }
          );
        }
      }

      // Clean up old cache entries after successful sync
      await this.cleanupCache();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log('error', '❌ Data synchronization failed:', error);
      this.stats.failedSyncs++;
      
      // Notify admin of critical sync failure
      await this.notificationService.notifyDataSyncFailure(
        'ALL_PROVIDERS',
        errorMessage,
        { totalDuration: Date.now() - syncStartTime }
      );
    } finally {
      this.isRunning = false;
    }

    return this.stats;
  }

  /**
   * Check if any provider files have changed since last cache
   * Returns true if sync is needed, false if all files are cached and fresh
   */
  private async checkForFileChanges(): Promise<boolean> {
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      const cacheDir = path.join(process.cwd(), 'data', 'downloads');
      const cacheDurationMs = config.fileCacheDurationDays * 24 * 60 * 60 * 1000;
      const now = Date.now();
      
      const filesToCheck = [
        'lp-express-terminals.csv',
        'omniva-terminals.json', 
        'dpd-terminals.xlsx',
        'venipak-terminals.json'
      ];

      let hasChanges = false;
      
      for (const filename of filesToCheck) {
        const filePath = path.join(cacheDir, filename);
        
        if (!fs.existsSync(filePath)) {
          this.log('info', `📄 File not cached: ${filename} - sync needed`);
          hasChanges = true;
          continue;
        }
        
        const stats = fs.statSync(filePath);
        const fileAge = now - stats.mtime.getTime();
        const ageDays = fileAge / (24 * 60 * 60 * 1000);
        
        if (fileAge > cacheDurationMs) {
          this.log('info', `📄 File expired: ${filename} (${ageDays.toFixed(1)} days old) - sync needed`);
          hasChanges = true;
        } else {
          this.log('info', `📁 File cached: ${filename} (${ageDays.toFixed(1)} days old) - up to date`);
        }
      }
      
      return hasChanges;
      
    } catch (error) {
      this.log('warn', '⚠️  Failed to check file changes, proceeding with sync:', error);
      return true; // If we can't check, assume changes exist
    }
  }

  private async syncProvider(
    providerName: string,
    collector: () => Promise<TerminalImportType[]>
  ): Promise<SyncResult> {
    const startTime = Date.now();
    const result: SyncResult = {
      provider: providerName,
      success: false,
      terminalCount: 0,
      errors: [],
      duration: 0
    };

    try {
      this.log('info', `🔄 Syncing ${providerName} terminals...`);
      
      // Collect data from provider
      const terminals = await collector();
      
      if (terminals.length === 0) {
        result.errors.push('No terminals collected');
        this.log('warn', `⚠️  No terminals collected from ${providerName}`);
        
        // Notify if no data collected (potential issue)
        await this.notificationService.notifyDataSyncFailure(
          providerName,
          'No terminals collected from provider',
          { expectedTerminals: '> 0', actualTerminals: 0 }
        );
        
        return result;
      }

      // Import terminals to database
      const importResult = await this.terminalService.bulkImport({
        terminals,
        provider: providerName as any,
        replaceExisting: true
      });

      result.success = importResult.success;
      result.terminalCount = importResult.imported;
      result.errors = importResult.errors;

      if (result.success) {
        this.log('info', `✅ ${providerName}: ${result.terminalCount} terminals synced successfully`);
      } else {
        this.log('error', `❌ ${providerName}: sync failed with ${result.errors.length} errors`);
        
        // Notify admin of import failures
        await this.notificationService.notifyDataSyncFailure(
          providerName,
          `Import failed: ${result.errors.slice(0, 3).join('; ')}${result.errors.length > 3 ? '...' : ''}`,
          { errorCount: result.errors.length, terminalCount: result.terminalCount }
        );
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      this.log('error', `❌ ${providerName} sync failed:`, error);
      
      // Notify admin of collection failures
      await this.notificationService.notifyDataSyncFailure(
        providerName,
        errorMessage,
        { stage: 'data_collection' }
      );
    } finally {
      result.duration = Date.now() - startTime;
    }

    return result;
  }

  private updateStats(results: SyncResult[]): void {
    this.stats.lastSync = new Date();
    this.stats.totalSyncs++;
    this.stats.lastResults = results;

    const allSuccessful = results.every(r => r.success);
    if (allSuccessful) {
      this.stats.successfulSyncs++;
    } else {
      this.stats.failedSyncs++;
    }
  }

  private async cleanupCache(): Promise<void> {
    try {
      this.log('info', '🧹 Cleaning up expired cache entries...');
      const { getDatabasePool } = await import('../database/connection');
      const pool = getDatabasePool();
      await pool.query('SELECT clean_expired_cache()');
      this.log('info', '🧹 Cache cleanup completed');
    } catch (error) {
      this.log('error', '⚠️  Cache cleanup failed:', error);
    }
  }

  async syncSingleProvider(provider: 'LP_EXPRESS' | 'OMNIVA' | 'DPD' | 'VENIPAK'): Promise<SyncResult> {
    if (this.isRunning) {
      throw new Error('Data sync already in progress');
    }

    this.isRunning = true;

    try {
      let result: SyncResult;

      switch (provider) {
        case 'LP_EXPRESS':
          result = await this.syncProvider('LP_EXPRESS', async () => {
            return await this.lpExpressCollector.collectAndValidate();
          });
          break;
        case 'OMNIVA':
          result = await this.syncProvider('OMNIVA', async () => {
            return await this.omnivaCollector.collectAndValidate();
          });
          break;
        case 'DPD':
          result = await this.syncProvider('DPD', async () => {
            return await this.dpdCollector.collectAndValidate();
          });
          break;
        case 'VENIPAK':
          result = await this.syncProvider('VENIPAK', async () => {
            return await this.venipakCollector.collectAndValidate();
          });
          break;
        default:
          throw new Error(`Unknown provider: ${provider}`);
      }

      return result;
    } finally {
      this.isRunning = false;
    }
  }

  getStats(): SyncStats {
    return { ...this.stats };
  }

  isCurrentlyRunning(): boolean {
    return this.isRunning;
  }

  async forceSync(): Promise<SyncStats> {
    this.log('info', '🔄 Force sync requested...');
    return await this.syncAllProviders();
  }

  stopScheduler(): void {
    this.log('info', '🛑 Stopping data synchronization scheduler...');
    // Note: node-cron doesn't provide a direct way to stop all tasks
    // In a production environment, you might want to track task references
  }
}
