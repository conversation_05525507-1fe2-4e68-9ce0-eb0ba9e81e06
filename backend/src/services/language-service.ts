/**
 * Language service for loading and managing translations
 */

import { readFileSync } from 'fs';
import { join } from 'path';

interface LanguageData {
  tracking: {
    status: Record<string, string>;
    providers: Record<string, Record<string, string>>;
  };
}

class LanguageService {
  private static instance: LanguageService;
  private translations: Map<string, LanguageData> = new Map();

  private constructor() {
    this.loadTranslations();
  }

  static getInstance(): LanguageService {
    if (!LanguageService.instance) {
      LanguageService.instance = new LanguageService();
    }
    return LanguageService.instance;
  }

  private loadTranslations(): void {
    try {
      // Load English translations
      const enPath = join(__dirname, '../config/languages/en.json');
      const enData = JSON.parse(readFileSync(enPath, 'utf-8')) as LanguageData;
      this.translations.set('en', enData);

      // Load Lithuanian translations
      const ltPath = join(__dirname, '../config/languages/lt.json');
      const ltData = JSON.parse(readFileSync(ltPath, 'utf-8')) as LanguageData;
      this.translations.set('lt', ltData);
    } catch (error) {
      console.error('Failed to load language translations:', error);
      // Fallback to empty translations
      this.translations.set('en', { tracking: { status: {}, providers: {} } });
      this.translations.set('lt', { tracking: { status: {}, providers: {} } });
    }
  }

  /**
   * Get status translation for a given language
   */
  getStatusTranslation(status: string, language: 'en' | 'lt'): string {
    const translations = this.translations.get(language);
    return translations?.tracking.status[status] || status;
  }

  /**
   * Get provider-specific status translation
   */
  getProviderStatusTranslation(
    provider: string,
    statusCode: string,
    language: 'en' | 'lt'
  ): string {
    const translations = this.translations.get(language);
    return translations?.tracking.providers[provider]?.[statusCode] || statusCode;
  }

  /**
   * Get all available languages
   */
  getAvailableLanguages(): string[] {
    return Array.from(this.translations.keys());
  }
}

export const languageService = LanguageService.getInstance();
