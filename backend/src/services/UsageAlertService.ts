/**
 * Usage Alert Service
 * 
 * Handles usage threshold monitoring, alert generation, and notification dispatch.
 * Integrates with the analytics system to provide proactive usage monitoring.
 */

import { Pool } from 'pg';
import { getDatabasePool } from '../database/connection';
import { UsageAnalyticsService } from './UsageAnalyticsService';

// Types for usage alerts
export interface AlertThreshold {
  id: string;
  userId: string;
  apiKeyId?: string;
  thresholdType: 'requests_per_minute' | 'requests_per_day' | 'requests_per_month' | 'data_transfer';
  thresholdValue: number;
  alertPercentages: number[]; // e.g., [80, 95, 100] for 80%, 95%, and 100% alerts
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AlertNotification {
  id: string;
  userId: string;
  apiKeyId?: string;
  alertType: 'usage_threshold' | 'rate_limit_exceeded' | 'quota_exceeded' | 'unusual_activity';
  severity: 'info' | 'warning' | 'critical';
  title: string;
  message: string;
  data: any;
  isRead: boolean;
  isSent: boolean;
  sentAt?: string;
  createdAt: string;
}

export interface UsageMonitoringConfig {
  checkIntervalMinutes: number;
  enableEmailNotifications: boolean;
  enableWebhookNotifications: boolean;
  webhookUrl?: string;
  emailTemplates: {
    [key: string]: {
      subject: string;
      template: string;
    };
  };
}

export class UsageAlertService {
  private pool: Pool | null = null;
  private analyticsService: UsageAnalyticsService;
  private monitoringInterval?: ReturnType<typeof setInterval> | undefined;
  
  private config: UsageMonitoringConfig = {
    checkIntervalMinutes: 5,
    enableEmailNotifications: true,
    enableWebhookNotifications: false,
    emailTemplates: {
      usage_threshold: {
        subject: 'API Usage Alert - {{percentage}}% of quota used',
        template: 'You have used {{percentage}}% of your {{period}} API request quota ({{used}}/{{limit}} requests).'
      },
      quota_exceeded: {
        subject: 'API Quota Exceeded',
        template: 'Your {{period}} API request quota has been exceeded. Please upgrade your plan or wait for the next period.'
      },
      rate_limit_exceeded: {
        subject: 'Rate Limit Exceeded',
        template: 'Your API key has exceeded the rate limit. Please reduce your request frequency.'
      }
    }
  };

  constructor() {
    this.analyticsService = new UsageAnalyticsService();
  }

  private getPool(): Pool {
    if (!this.pool) {
      this.pool = getDatabasePool();
    }
    return this.pool;
  }

  /**
   * Start the usage monitoring system
   */
  startMonitoring(): void {
    if (this.monitoringInterval) {
      return; // Already running
    }

    this.monitoringInterval = setInterval(
      () => this.checkAllUsageThresholds(),
      this.config.checkIntervalMinutes * 60 * 1000
    );

    console.log(`Usage monitoring started (checking every ${this.config.checkIntervalMinutes} minutes)`);
  }

  /**
   * Stop the usage monitoring system
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
      console.log('Usage monitoring stopped');
    }
  }

  /**
   * Check usage thresholds for all users
   */
  private async checkAllUsageThresholds(): Promise<void> {
    try {
      const pool = this.getPool();
      
      // Get all active users with subscriptions
      const usersResult = await pool.query(`
        SELECT DISTINCT u.id, u.email, u.first_name, u.last_name
        FROM users u
        JOIN user_subscriptions us ON u.id = us.user_id
        WHERE us.status = 'active'
      `);

      for (const user of usersResult.rows) {
        await this.checkUserUsageThresholds(user.id);
      }
    } catch (error) {
      console.error('Error checking usage thresholds:', error);
    }
  }

  /**
   * Check usage thresholds for a specific user
   */
  async checkUserUsageThresholds(userId: string): Promise<void> {
    try {
      // Get user's current quotas
      const quotas = await this.analyticsService.getUsageQuotas(userId);
      
      for (const quota of quotas) {
        const usagePercentage = (quota.requestsUsed / quota.requestsLimit) * 100;
        
        // Check various threshold levels
        await this.checkThresholdLevel(userId, quota, usagePercentage, 80, 'warning');
        await this.checkThresholdLevel(userId, quota, usagePercentage, 95, 'critical');
        
        if (quota.isExceeded) {
          await this.createQuotaExceededAlert(userId, quota);
        }
      }
    } catch (error) {
      console.error(`Error checking thresholds for user ${userId}:`, error);
    }
  }

  /**
   * Check a specific threshold level
   */
  private async checkThresholdLevel(
    userId: string,
    quota: any,
    usagePercentage: number,
    thresholdPercentage: number,
    severity: 'info' | 'warning' | 'critical'
  ): Promise<void> {
    if (usagePercentage >= thresholdPercentage) {
      // Check if we've already sent this alert recently
      const recentAlert = await this.getRecentAlert(
        userId,
        'usage_threshold',
        `${thresholdPercentage}_${quota.quotaPeriod}`
      );

      if (!recentAlert) {
        await this.createUsageThresholdAlert(
          userId,
          quota,
          usagePercentage,
          thresholdPercentage,
          severity
        );
      }
    }
  }

  /**
   * Create a usage threshold alert
   */
  private async createUsageThresholdAlert(
    userId: string,
    quota: any,
    usagePercentage: number,
    thresholdPercentage: number,
    severity: 'info' | 'warning' | 'critical'
  ): Promise<void> {
    const title = `${thresholdPercentage}% of ${quota.quotaPeriod} quota used`;
    const template = this.config.emailTemplates['usage_threshold'];
    const message = (template?.template || 'Usage threshold alert')
      .replace('{{percentage}}', usagePercentage.toFixed(1))
      .replace('{{period}}', quota.quotaPeriod)
      .replace('{{used}}', quota.requestsUsed.toString())
      .replace('{{limit}}', quota.requestsLimit.toString());

    await this.createAlert({
      userId,
      alertType: 'usage_threshold',
      severity,
      title,
      message,
      data: {
        quotaId: quota.id,
        quotaPeriod: quota.quotaPeriod,
        usagePercentage,
        thresholdPercentage,
        requestsUsed: quota.requestsUsed,
        requestsLimit: quota.requestsLimit
      }
    });
  }

  /**
   * Create a quota exceeded alert
   */
  private async createQuotaExceededAlert(userId: string, quota: any): Promise<void> {
    const recentAlert = await this.getRecentAlert(userId, 'quota_exceeded', quota.quotaPeriod);

    if (!recentAlert) {
      const title = `${quota.quotaPeriod} quota exceeded`;
      const template = this.config.emailTemplates['quota_exceeded'];
      const message = (template?.template || 'Quota exceeded alert')
        .replace('{{period}}', quota.quotaPeriod);

      await this.createAlert({
        userId,
        alertType: 'quota_exceeded',
        severity: 'critical',
        title,
        message,
        data: {
          quotaId: quota.id,
          quotaPeriod: quota.quotaPeriod,
          requestsUsed: quota.requestsUsed,
          requestsLimit: quota.requestsLimit,
          exceededAt: quota.exceededAt
        }
      });
    }
  }

  /**
   * Create a new alert notification
   */
  private async createAlert(alertData: {
    userId: string;
    apiKeyId?: string;
    alertType: string;
    severity: 'info' | 'warning' | 'critical';
    title: string;
    message: string;
    data: any;
  }): Promise<string> {
    const pool = this.getPool();
    
    const query = `
      INSERT INTO usage_alerts (
        user_id, api_key_id, alert_type, threshold_type,
        threshold_value, current_value, message, severity
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id
    `;

    const result = await pool.query(query, [
      alertData.userId,
      alertData.apiKeyId || null,
      alertData.alertType,
      alertData.data.quotaPeriod || 'requests_per_day',
      alertData.data.requestsLimit || 0,
      alertData.data.requestsUsed || 0,
      alertData.message,
      alertData.severity
    ]);

    const alertId = result.rows[0].id;

    // Send notification
    await this.sendNotification(alertId, alertData);

    return alertId;
  }

  /**
   * Send notification for an alert
   */
  private async sendNotification(alertId: string, alertData: any): Promise<void> {
    try {
      // In a real implementation, this would send emails, webhooks, etc.
      console.log(`📢 Alert Notification [${alertData.severity.toUpperCase()}]:`, {
        alertId,
        userId: alertData.userId,
        title: alertData.title,
        message: alertData.message,
        timestamp: new Date().toISOString()
      });

      // Mark notification as sent
      // In a real implementation, you'd update a notifications table
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  /**
   * Get recent alert to avoid spam
   */
  private async getRecentAlert(
    userId: string,
    alertType: string,
    identifier: string
  ): Promise<any> {
    const pool = this.getPool();
    
    const query = `
      SELECT id, created_at
      FROM usage_alerts
      WHERE user_id = $1 
        AND alert_type = $2
        AND message LIKE $3
        AND created_at > NOW() - INTERVAL '1 hour'
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [userId, alertType, `%${identifier}%`]);
    return result.rows[0] || null;
  }

  /**
   * Get all alerts for a user
   */
  async getUserAlerts(
    userId: string,
    includeResolved: boolean = false,
    limit: number = 50
  ): Promise<any[]> {
    const pool = this.getPool();
    
    let whereClause = 'WHERE user_id = $1';
    const params: any[] = [userId];

    if (!includeResolved) {
      whereClause += ' AND is_resolved = false';
    }

    const query = `
      SELECT 
        id, user_id, api_key_id, alert_type, threshold_type,
        threshold_value, current_value, message, severity,
        is_resolved, created_at, resolved_at
      FROM usage_alerts
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${params.length + 1}
    `;

    params.push(limit);

    const result = await pool.query(query, params);
    return result.rows;
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: string): Promise<void> {
    const pool = this.getPool();
    
    const query = `
      UPDATE usage_alerts 
      SET is_resolved = true, resolved_at = NOW()
      WHERE id = $1
    `;

    await pool.query(query, [alertId]);
  }

  /**
   * Create a rate limit exceeded alert
   */
  async createRateLimitAlert(
    userId: string,
    apiKeyId: string,
    currentRate: number,
    limit: number
  ): Promise<void> {
    const title = 'Rate limit exceeded';
    const message = `Your API key has exceeded the rate limit (${currentRate}/${limit} requests per minute).`;

    await this.createAlert({
      userId,
      apiKeyId,
      alertType: 'rate_limit_exceeded',
      severity: 'warning',
      title,
      message,
      data: {
        currentRate,
        limit,
        apiKeyId
      }
    });
  }

  /**
   * Update monitoring configuration
   */
  updateConfig(newConfig: Partial<UsageMonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart monitoring with new interval if changed
    if (newConfig.checkIntervalMinutes && this.monitoringInterval) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }

  /**
   * Get monitoring statistics
   */
  async getMonitoringStats(): Promise<{
    totalAlerts: number;
    activeAlerts: number;
    alertsByType: any;
    alertsBySeverity: any;
  }> {
    const pool = this.getPool();
    
    const [totalResult, activeResult, typeResult, severityResult] = await Promise.all([
      pool.query('SELECT COUNT(*) as count FROM usage_alerts'),
      pool.query('SELECT COUNT(*) as count FROM usage_alerts WHERE is_resolved = false'),
      pool.query('SELECT alert_type, COUNT(*) as count FROM usage_alerts GROUP BY alert_type'),
      pool.query('SELECT severity, COUNT(*) as count FROM usage_alerts GROUP BY severity')
    ]);

    return {
      totalAlerts: parseInt(totalResult.rows[0].count),
      activeAlerts: parseInt(activeResult.rows[0].count),
      alertsByType: typeResult.rows.reduce((acc: any, row: any) => {
        acc[row.alert_type] = parseInt(row.count);
        return acc;
      }, {}),
      alertsBySeverity: severityResult.rows.reduce((acc: any, row: any) => {
        acc[row.severity] = parseInt(row.count);
        return acc;
      }, {})
    };
  }
}
