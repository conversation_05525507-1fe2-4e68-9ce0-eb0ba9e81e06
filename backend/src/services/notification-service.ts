import { FastifyBaseLogger } from 'fastify';
import { WEBHOOK_CONFIG } from '../config';

export interface NotificationConfig {
  adminEmail?: string | undefined;
  fromEmail?: string | undefined;
  smtpHost?: string | undefined;
  smtpPort?: number | undefined;
  smtpUser?: string | undefined;
  smtpPassword?: string | undefined;
  webhookUrl?: string | undefined;
  enableEmailNotifications: boolean;
  enableWebhookNotifications: boolean;
  // Batching configuration
  enableEmailBatching: boolean;
  batchingIntervals: {
    info: number;      // milliseconds
    warning: number;   // milliseconds
    error: number;     // milliseconds
    critical: number;  // milliseconds (0 = immediate)
  };
  maxBatchSize: number;
  maxBatchWaitTime: number; // maximum time to wait before sending batch
}

export interface NotificationMessage {
  subject: string;
  message: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  details?: Record<string, any>;
  timestamp: Date;
  type: NotificationType;
  id: string; // unique identifier for deduplication
}

export type NotificationType =
  | 'data_sync_failure'
  | 'download_failure'
  | 'geocoding_failure'
  | 'database_issue'
  | 'high_error_rate'
  | 'tracking_service_failure'
  | 'security_incident'
  | 'system_error';

export interface BatchedNotification {
  type: NotificationType;
  severity: 'info' | 'warning' | 'error' | 'critical';
  notifications: NotificationMessage[];
  firstTimestamp: Date;
  lastTimestamp: Date;
  count: number;
}

export interface NotificationBatch {
  id: string;
  createdAt: Date;
  scheduledSendTime: Date;
  notifications: Map<string, NotificationMessage>; // key: notification.id
  batchesByType: Map<NotificationType, BatchedNotification>;
}

export class NotificationService {
  private config: NotificationConfig;
  private logger: FastifyBaseLogger | null = null;
  private activeBatches: Map<string, NotificationBatch> = new Map();
  private batchTimers: Map<string, ReturnType<typeof setTimeout>> = new Map();

  constructor(config: NotificationConfig) {
    this.config = config;
  }

  setLogger(logger: FastifyBaseLogger) {
    this.logger = logger;
  }

  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private getBatchKey(severity: 'info' | 'warning' | 'error' | 'critical'): string {
    return `batch_${severity}`;
  }

  async notifyDataSyncFailure(provider: string, error: string, details?: Record<string, any>): Promise<void> {
    const message: NotificationMessage = {
      id: this.generateNotificationId(),
      type: 'data_sync_failure',
      subject: `🚨 Data Sync Failure - ${provider}`,
      message: `Data synchronization failed for provider ${provider}: ${error}`,
      severity: 'error',
      details: {
        provider,
        error,
        ...details,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.sendNotification(message);
  }

  async notifyDownloadFailure(url: string, error: string, attempts: number): Promise<void> {
    const message: NotificationMessage = {
      id: this.generateNotificationId(),
      type: 'download_failure',
      subject: `📥 File Download Failure`,
      message: `Failed to download file after ${attempts} attempts: ${url}. Error: ${error}`,
      severity: 'error',
      details: {
        url,
        error,
        attempts,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.sendNotification(message);
  }

  async notifyGecodingFailure(terminalCount: number, provider: string, successRate: number): Promise<void> {
    const message: NotificationMessage = {
      id: this.generateNotificationId(),
      type: 'geocoding_failure',
      subject: `🌍 Geocoding Issues Detected`,
      message: `Low geocoding success rate for ${provider}: ${successRate.toFixed(1)}% (${terminalCount} terminals affected)`,
      severity: 'warning',
      details: {
        provider,
        terminalCount,
        successRate,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.sendNotification(message);
  }

  async notifyDatabaseIssue(operation: string, error: string): Promise<void> {
    const message: NotificationMessage = {
      id: this.generateNotificationId(),
      type: 'database_issue',
      subject: `🗄️ Database Issue`,
      message: `Database operation "${operation}" failed: ${error}`,
      severity: 'critical',
      details: {
        operation,
        error,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.sendNotification(message);
  }

  async notifyHighErrorRate(endpoint: string, errorRate: number, timeWindow: string): Promise<void> {
    const message: NotificationMessage = {
      id: this.generateNotificationId(),
      type: 'high_error_rate',
      subject: `⚠️ High Error Rate Detected`,
      message: `High error rate detected on ${endpoint}: ${errorRate.toFixed(1)}% in the last ${timeWindow}`,
      severity: 'warning',
      details: {
        endpoint,
        errorRate,
        timeWindow,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.sendNotification(message);
  }

  async notifyTrackingServiceFailure(provider: string, error: string, failureCount: number, circuitBreakerActivated: boolean): Promise<void> {
    const severity = circuitBreakerActivated ? 'critical' : 'error';
    const subject = circuitBreakerActivated
      ? `🚨 Tracking Service Circuit Breaker Activated - ${provider}`
      : `📦 Tracking Service Failure - ${provider}`;

    const message: NotificationMessage = {
      id: this.generateNotificationId(),
      type: 'tracking_service_failure',
      subject,
      message: circuitBreakerActivated
        ? `Tracking service for ${provider} has been temporarily disabled due to repeated failures (${failureCount} consecutive failures). Circuit breaker activated.`
        : `Tracking service failure for ${provider}: ${error}`,
      severity,
      details: {
        provider,
        error,
        failureCount,
        circuitBreakerActivated,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.sendNotification(message);
  }

  async notifySecurityIncident(incident: string, details: Record<string, any>, severity: 'warning' | 'error' | 'critical' = 'warning'): Promise<void> {
    const message: NotificationMessage = {
      id: this.generateNotificationId(),
      type: 'security_incident',
      subject: `🛡️ Security Incident: ${incident}`,
      message: `Security incident detected: ${incident}`,
      severity,
      details: {
        incident,
        ...details,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.sendNotification(message);
  }

  private async sendNotification(message: NotificationMessage): Promise<void> {
    try {
      // Log the notification regardless of delivery method
      const logLevel = this.getLogLevel(message.severity);
      if (this.logger) {
        this.logger[logLevel]({
          notification: message,
          subject: message.subject,
          severity: message.severity,
          type: message.type,
          id: message.id
        }, `Notification: ${message.subject}`);
      }

      // Always send webhooks immediately (no batching for webhooks)
      if (this.config.enableWebhookNotifications && this.config.webhookUrl) {
        this.sendWebhookNotification(message).catch(error => {
          if (this.logger) {
            this.logger.error({ error, notificationId: message.id }, 'Failed to send webhook notification');
          }
        });
      }

      // Handle email notifications with batching
      if (this.config.enableEmailNotifications && this.config.adminEmail) {
        if (this.shouldBatchNotification(message)) {
          await this.addToBatch(message);
        } else {
          // Send immediately for critical notifications or when batching is disabled
          await this.sendEmailNotification(message);
        }
      }

      // If no delivery methods configured, just log
      if (!this.config.enableEmailNotifications && !this.config.enableWebhookNotifications) {
        if (this.logger) {
          this.logger.warn('No notification delivery methods configured. Notification logged only.');
        }
      }

    } catch (error) {
      if (this.logger) {
        this.logger.error({ error, notification: message }, 'Failed to send notification');
      }
    }
  }

  private shouldBatchNotification(message: NotificationMessage): boolean {
    // Never batch critical notifications
    if (message.severity === 'critical') {
      return false;
    }

    // Don't batch if batching is disabled
    if (!this.config.enableEmailBatching) {
      return false;
    }

    // Don't batch if interval is 0 (immediate)
    const interval = this.config.batchingIntervals[message.severity];
    return interval > 0;
  }

  private async addToBatch(message: NotificationMessage): Promise<void> {
    const batchKey = this.getBatchKey(message.severity);
    let batch = this.activeBatches.get(batchKey);

    if (!batch) {
      // Create new batch
      const now = new Date();
      const interval = this.config.batchingIntervals[message.severity];

      batch = {
        id: `batch_${Date.now()}_${message.severity}`,
        createdAt: now,
        scheduledSendTime: new Date(now.getTime() + interval),
        notifications: new Map(),
        batchesByType: new Map()
      };

      this.activeBatches.set(batchKey, batch);

      // Schedule batch sending (use minimum of interval and max wait time)
      const actualInterval = Math.min(interval, this.config.maxBatchWaitTime);
      const timer = setTimeout(() => {
        this.sendBatch(batchKey).catch(error => {
          if (this.logger) {
            this.logger.error({ error, batchKey }, 'Failed to send batch');
          }
        });
      }, actualInterval);

      this.batchTimers.set(batchKey, timer);
    }

    // Add notification to batch
    batch.notifications.set(message.id, message);

    // Update batched notification by type
    let batchedNotification = batch.batchesByType.get(message.type);
    if (!batchedNotification) {
      batchedNotification = {
        type: message.type,
        severity: message.severity,
        notifications: [],
        firstTimestamp: message.timestamp,
        lastTimestamp: message.timestamp,
        count: 0
      };
      batch.batchesByType.set(message.type, batchedNotification);
    }

    batchedNotification.notifications.push(message);
    batchedNotification.count++;
    batchedNotification.lastTimestamp = message.timestamp;

    // Check if batch should be sent early due to size limit
    if (batch.notifications.size >= this.config.maxBatchSize) {
      // Clear the timer and send immediately
      const timer = this.batchTimers.get(batchKey);
      if (timer) {
        clearTimeout(timer);
        this.batchTimers.delete(batchKey);
      }

      await this.sendBatch(batchKey);
    }
  }

  private async sendBatch(batchKey: string): Promise<void> {
    const batch = this.activeBatches.get(batchKey);
    if (!batch || batch.notifications.size === 0) {
      return;
    }

    try {
      // Create batched email
      const batchedEmail = this.createBatchedEmail(batch);
      await this.sendEmailNotification(batchedEmail);

      if (this.logger) {
        this.logger.info({
          batchId: batch.id,
          notificationCount: batch.notifications.size,
          typeCount: batch.batchesByType.size,
          duration: Date.now() - batch.createdAt.getTime()
        }, 'Sent batched email notification');
      }

    } catch (error) {
      if (this.logger) {
        this.logger.error({ error, batchId: batch.id }, 'Failed to send batched email');
      }
    } finally {
      // Clean up
      this.activeBatches.delete(batchKey);
      const timer = this.batchTimers.get(batchKey);
      if (timer) {
        clearTimeout(timer);
        this.batchTimers.delete(batchKey);
      }
    }
  }

  private createBatchedEmail(batch: NotificationBatch): NotificationMessage {
    const notificationCount = batch.notifications.size;
    const typeCount = batch.batchesByType.size;
    const duration = Date.now() - batch.createdAt.getTime();

    // Determine overall severity (highest severity wins)
    let overallSeverity: 'info' | 'warning' | 'error' | 'critical' = 'info';
    for (const notification of batch.notifications.values()) {
      if (notification.severity === 'critical') {
        overallSeverity = 'critical';
        break;
      } else if (notification.severity === 'error') {
        overallSeverity = 'error';
      } else if (notification.severity === 'warning' && overallSeverity === 'info') {
        overallSeverity = 'warning';
      }
    }

    // Create summary by type
    const typeSummaries: string[] = [];
    for (const [type, batchedNotification] of batch.batchesByType) {
      const typeLabel = this.getTypeLabel(type);
      const emoji = this.getTypeEmoji(type);

      if (batchedNotification.count === 1) {
        typeSummaries.push(`${emoji} ${typeLabel}: ${batchedNotification.notifications[0]?.message || 'Unknown error'}`);
      } else {
        typeSummaries.push(`${emoji} ${typeLabel}: ${batchedNotification.count} incidents`);

        // Add details for each incident
        batchedNotification.notifications.forEach((notif, index) => {
          if (index < 3) { // Show first 3 incidents
            typeSummaries.push(`  • ${notif.message}`);
          } else if (index === 3) {
            typeSummaries.push(`  • ... and ${batchedNotification.count - 3} more`);
          }
        });
      }
    }

    const subject = `📊 System Alert Digest - ${notificationCount} notification${notificationCount > 1 ? 's' : ''} (${this.formatDuration(duration)})`;

    const message = `
System Alert Digest Summary:
• Total Notifications: ${notificationCount}
• Notification Types: ${typeCount}
• Time Period: ${batch.createdAt.toISOString()} to ${new Date().toISOString()}
• Duration: ${this.formatDuration(duration)}

${typeSummaries.join('\n')}

This digest contains ${notificationCount} notification${notificationCount > 1 ? 's' : ''} that occurred over the past ${this.formatDuration(duration)}.
    `.trim();

    return {
      id: `batched_${batch.id}`,
      type: 'system_error',
      subject,
      message,
      severity: overallSeverity,
      details: {
        batchId: batch.id,
        notificationCount,
        typeCount,
        duration,
        notifications: Array.from(batch.notifications.values()).map(n => ({
          id: n.id,
          type: n.type,
          severity: n.severity,
          subject: n.subject,
          timestamp: n.timestamp.toISOString()
        })),
        timestamp: new Date().toISOString()
      },
      timestamp: new Date()
    };
  }

  private getTypeLabel(type: NotificationType): string {
    const labels: Record<NotificationType, string> = {
      'data_sync_failure': 'Data Sync Failures',
      'download_failure': 'Download Failures',
      'geocoding_failure': 'Geocoding Issues',
      'database_issue': 'Database Issues',
      'high_error_rate': 'High Error Rates',
      'tracking_service_failure': 'Tracking Service Failures',
      'security_incident': 'Security Incidents',
      'system_error': 'System Errors'
    };
    return labels[type] || type;
  }

  private getTypeEmoji(type: NotificationType): string {
    const emojis: Record<NotificationType, string> = {
      'data_sync_failure': '🚨',
      'download_failure': '📥',
      'geocoding_failure': '🌍',
      'database_issue': '🗄️',
      'high_error_rate': '⚠️',
      'tracking_service_failure': '📦',
      'security_incident': '🛡️',
      'system_error': '💥'
    };
    return emojis[type] || '📋';
  }

  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  // Method to manually flush all batches (useful for shutdown)
  public async flushAllBatches(): Promise<void> {
    const batchKeys = Array.from(this.activeBatches.keys());

    for (const batchKey of batchKeys) {
      try {
        await this.sendBatch(batchKey);
      } catch (error) {
        if (this.logger) {
          this.logger.error({ error, batchKey }, 'Failed to flush batch during shutdown');
        }
      }
    }
  }

  private async sendEmailNotification(message: NotificationMessage): Promise<void> {
    try {
      if (!this.config.adminEmail) {
        throw new Error('Admin email not configured');
      }

      // Check if SMTP is properly configured
      if (!this.config.smtpHost || !this.config.smtpUser || !this.config.smtpPassword) {
        if (this.logger) {
          this.logger.warn('SMTP not fully configured. Email notification logged only.');
          this.logger.info({
            to: this.config.adminEmail,
            subject: message.subject,
            severity: message.severity,
            message: message.message
          }, 'Email notification (SMTP not configured)');
        }
        return;
      }

      const emailBody = this.formatEmailBody(message);

      try {
        // Dynamic import to avoid requiring nodemailer if not used
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const nodemailer = require('nodemailer');

        const transporter = nodemailer.createTransporter({
          host: this.config.smtpHost,
          port: this.config.smtpPort || 587,
          secure: this.config.smtpPort === 465,
          auth: {
            user: this.config.smtpUser,
            pass: this.config.smtpPassword
          },
          // Add timeout and retry configuration
          connectionTimeout: 10000,
          greetingTimeout: 5000,
          socketTimeout: 10000
        });

        // Verify SMTP connection
        await transporter.verify();

        await transporter.sendMail({
          from: this.config.fromEmail || this.config.smtpUser,
          to: this.config.adminEmail,
          subject: message.subject,
          html: emailBody,
          text: message.message // Fallback text version
        });

        if (this.logger) {
          this.logger.info({
            to: this.config.adminEmail,
            subject: message.subject,
            severity: message.severity
          }, 'Email notification sent successfully');
        }

      } catch (emailError) {
        // If nodemailer is not installed or SMTP fails, log the notification
        if (this.logger) {
          this.logger.warn({
            error: emailError,
            to: this.config.adminEmail,
            subject: message.subject,
            severity: message.severity
          }, 'Email sending failed, notification logged only');
        }

        // Don't throw error to prevent notification system from failing completely
        // The notification is still logged for manual review
      }

    } catch (error) {
      if (this.logger) {
        this.logger.error({ error }, 'Failed to send email notification');
      }
      throw error;
    }
  }

  private async sendWebhookNotification(message: NotificationMessage): Promise<void> {
    try {
      if (!this.config.webhookUrl) {
        throw new Error('Webhook URL not configured');
      }

      const webhookPayload = {
        notification: message,
        service: 'postal-terminal-api',
        environment: process.env['NODE_ENV'] || 'development',
        timestamp: message.timestamp.toISOString()
      };

      // Use dynamic import for node-fetch
      const { default: fetch } = await import('node-fetch');
      
      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), WEBHOOK_CONFIG.timeout);
      
      try {
        const response = await fetch(this.config.webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'PostalTerminalAPI/1.0'
          },
          body: JSON.stringify(webhookPayload),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`Webhook failed with status ${response.status}: ${response.statusText}`);
        }

        if (this.logger) {
          this.logger.info({ webhookUrl: this.config.webhookUrl, status: response.status }, 'Webhook notification sent successfully');
        }
      } finally {
        clearTimeout(timeoutId);
      }

    } catch (error) {
      if (this.logger) {
        this.logger.error({ error, webhookUrl: this.config.webhookUrl }, 'Failed to send webhook notification');
      }
      throw error;
    }
  }

  private formatEmailBody(message: NotificationMessage): string {
    const severityColors = {
      info: '#3498db',
      warning: '#f39c12',
      error: '#e74c3c',
      critical: '#c0392b'
    };

    const color = severityColors[message.severity];
    const detailsHtml = message.details 
      ? `<pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">${JSON.stringify(message.details, null, 2)}</pre>`
      : '';

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: ${color}; color: white; padding: 20px; border-radius: 5px 5px 0 0;">
          <h2 style="margin: 0;">${message.subject}</h2>
          <p style="margin: 5px 0 0 0; opacity: 0.9;">Severity: ${message.severity.toUpperCase()}</p>
        </div>
        <div style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 0 0 5px 5px;">
          <p style="margin: 0 0 15px 0; font-size: 16px; line-height: 1.5;">${message.message}</p>
          ${detailsHtml}
          <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
            <p>Time: ${message.timestamp.toISOString()}</p>
            <p>Service: Postal Terminal API</p>
            <p>Environment: ${process.env['NODE_ENV'] || 'development'}</p>
          </div>
        </div>
      </div>
    `;
  }

  private getLogLevel(severity: NotificationMessage['severity']): 'info' | 'warn' | 'error' {
    switch (severity) {
      case 'info': return 'info';
      case 'warning': return 'warn';
      case 'error':
      case 'critical': return 'error';
      default: return 'info';
    }
  }
}

// Singleton instance
let notificationService: NotificationService | null = null;

export function getNotificationService(): NotificationService {
  if (!notificationService) {
    const config: NotificationConfig = {
      adminEmail: process.env['ADMIN_EMAIL'],
      fromEmail: process.env['EMAIL_FROM_ADDRESS'],
      smtpHost: process.env['SMTP_HOST'],
      smtpPort: process.env['SMTP_PORT'] ? parseInt(process.env['SMTP_PORT']) : 587,
      smtpUser: process.env['SMTP_USER'],
      smtpPassword: process.env['SMTP_PASSWORD'],
      webhookUrl: process.env['WEBHOOK_URL'],
      enableEmailNotifications: process.env['ENABLE_EMAIL_NOTIFICATIONS'] === 'true',
      enableWebhookNotifications: process.env['ENABLE_WEBHOOK_NOTIFICATIONS'] === 'true',
      // Batching configuration
      enableEmailBatching: process.env['ENABLE_EMAIL_BATCHING'] !== 'false', // Default to true
      batchingIntervals: {
        info: parseInt(process.env['BATCH_INTERVAL_INFO_MS'] || '900000'),      // 15 minutes
        warning: parseInt(process.env['BATCH_INTERVAL_WARNING_MS'] || '300000'), // 5 minutes
        error: parseInt(process.env['BATCH_INTERVAL_ERROR_MS'] || '60000'),     // 1 minute
        critical: parseInt(process.env['BATCH_INTERVAL_CRITICAL_MS'] || '0')    // Immediate
      },
      maxBatchSize: parseInt(process.env['MAX_BATCH_SIZE'] || '50'),
      maxBatchWaitTime: parseInt(process.env['MAX_BATCH_WAIT_TIME_MS'] || '3600000') // 1 hour
    };

    notificationService = new NotificationService(config);
  }

  return notificationService;
} 