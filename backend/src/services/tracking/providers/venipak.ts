import { ITrackingProvider } from './base';
import { StandardizedTrackingResult, StandardizedTrackingEvent, StandardTrackingStatus } from '../../../types/standardized-tracking';
import { StatusMapper } from '../../../utils/status-mapping';
import { TRACKING_CONFIG } from '../../../config';

export class VenipakTrackingProvider implements ITrackingProvider {
  readonly name = 'VENIPAK' as const;
  private readonly endpoint = TRACKING_CONFIG.venipakTrackingUrl;

  async track(trackingNumber: string): Promise<StandardizedTrackingResult> {
    const res = await fetch(`${this.endpoint}/${trackingNumber}`, {
      signal: AbortSignal.timeout(TRACKING_CONFIG.defaultTimeout)
    });
    if (!res.ok) {
      throw new Error(`Venipak tracking failed: ${res.status}`);
    }

    const text = await res.text();

    let events: StandardizedTrackingEvent[] = [];

    try {
      const json = JSON.parse(text);
      if (json.history) {
        events = json.history.map((e: any) => {
          const mapping = StatusMapper.mapVenipakStatus('UNKNOWN', e.event);
          return {
            status: mapping.standard,
            statusText: {
              en: mapping.textEn,
              lt: mapping.textLt,
              original: e.event
            },
            location: e.city,
            timestamp: StatusMapper.normalizeTimestamp(e.date),
            rawData: e
          };
        });
      } else if (json.parcels) {
        // HTML wrapped inside json.parcels
        events = this.parseHtmlStandardized(json.parcels);
      }
    } catch (_e) {
      // Not JSON, maybe HTML directly
      events = this.parseHtmlStandardized(text);
    }

    const latestEvent = events.at(-1);
    const latestMapping = latestEvent ?
      { standard: latestEvent.status, textEn: latestEvent.statusText.en, textLt: latestEvent.statusText.lt } :
      { standard: StandardTrackingStatus.EXCEPTION, textEn: 'Unknown', textLt: 'Nežinoma' };

    return {
      provider: this.name,
      trackingNumber,
      status: latestMapping.standard,
      statusText: {
        en: latestMapping.textEn,
        lt: latestMapping.textLt,
        original: latestEvent?.statusText.original || 'UNKNOWN'
      },
      events,
      lastUpdated: latestEvent?.timestamp || new Date().toISOString()
    };
  }

  // Helper to parse Venipak HTML snippet with standardized output
  private parseHtmlStandardized(html: string): StandardizedTrackingEvent[] {
    const list: StandardizedTrackingEvent[] = [];
    const regex = /<div class="parcel-info[^>]*data-status="(\d+)"[^>]*>.*?<div class="info-time">([^<]+)<\/div>.*?<div class="info-place">([^<]*)<\/div>/gs;
    let match: RegExpExecArray | null;
    while ((match = regex.exec(html)) !== null) {
      const [, rawStatusCode, timeStr, place] = match;
      const code = String(rawStatusCode);
      const mapping = StatusMapper.mapVenipakStatus(code, 'UNKNOWN');

      list.push({
        status: mapping.standard,
        statusText: {
          en: mapping.textEn,
          lt: mapping.textLt,
          original: mapping.textEn
        },
        location: place ? place.trim() : undefined,
        timestamp: StatusMapper.normalizeTimestamp((timeStr ?? '').trim()),
        providerStatusCode: code,
        rawData: { statusCode: code, time: timeStr, place }
      });
    }
    return list;
  }
}