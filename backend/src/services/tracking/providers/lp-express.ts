import { ITrackingProvider } from './base';
import { StandardizedTrackingResult, StandardizedTrackingEvent, StandardTrackingStatus } from '../../../types/standardized-tracking';
import { StatusMapper } from '../../../utils/status-mapping';
import { TRACKING_CONFIG } from '../../../config';

export class LPExpressTrackingProvider implements ITrackingProvider {
  readonly name = 'LP_EXPRESS' as const;

  private readonly endpoint = TRACKING_CONFIG.lpExpressTrackingUrl;

  async track(trackingNumber: string): Promise<StandardizedTrackingResult> {
    const url = `${this.endpoint}/${trackingNumber}/events`;
    const res = await fetch(url, {
      headers: { 'accept': 'application/json' },
      signal: AbortSignal.timeout(TRACKING_CONFIG.defaultTimeout)
    });

    if (!res.ok) {
      throw new Error(`LP Express tracking request failed: ${res.status} ${res.statusText}`);
    }

    const data: any = await res.json();
    const rawEvents: any[] = Array.isArray(data) ? data : data.events || data.EventList || [];

    const events: StandardizedTrackingEvent[] = rawEvents.map((e: any) => {
      const mapping = StatusMapper.mapLPExpressStatus(
        e.publicStateType || 'UNKNOWN',
        e.publicStateText || e.publicEventText || 'UNKNOWN'
      );

      return {
        status: mapping.standard,
        statusText: {
          en: mapping.textEn,
          lt: mapping.textLt,
          original: e.publicStateText || e.publicEventText || 'UNKNOWN'
        },
        location: e.location || e.place,
        timestamp: StatusMapper.normalizeTimestamp(e.eventDate || e.updated),
        providerEventId: e.id,
        providerStatusCode: e.publicStateType,
        rawData: e
      };
    });

    const latestEvent = events[events.length - 1];
    const latestMapping = latestEvent ?
      StatusMapper.mapLPExpressStatus(latestEvent.providerStatusCode || 'UNKNOWN', latestEvent.statusText.original) :
      { standard: StandardTrackingStatus.EXCEPTION, textEn: 'Unknown', textLt: 'Nežinoma' };

    return {
      provider: this.name,
      trackingNumber,
      status: latestMapping.standard,
      statusText: {
        en: latestMapping.textEn,
        lt: latestMapping.textLt,
        original: latestEvent?.statusText.original || 'UNKNOWN'
      },
      events,
      lastUpdated: latestEvent?.timestamp || new Date().toISOString()
    };
  }
}