import { ITrackingProvider } from './base';
import { StandardizedTrackingResult, StandardizedTrackingEvent, StandardTrackingStatus } from '../../../types/standardized-tracking';
import { StatusMapper } from '../../../utils/status-mapping';
import { TRACKING_CONFIG } from '../../../config';

export class DPDTrackingProvider implements ITrackingProvider {
  readonly name = 'DPD' as const;

  async track(trackingNumber: string): Promise<StandardizedTrackingResult> {
    if (!TRACKING_CONFIG.enableDpdTracking) {
      const err: any = new Error('DPD tracking not enabled');
      err.statusCode = 503;
      throw err;
    }

    // Lazy import to avoid heavy deps when flag is off
    const puppeteerExtra = await import('puppeteer-extra');
    const StealthPlugin = (await import('puppeteer-extra-plugin-stealth')).default;
    puppeteerExtra.default.use(StealthPlugin());
    const puppeteer = puppeteerExtra.default;

    const browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu'
      ]
    });

    try {
      const page = await browser.newPage();
      await page.setUserAgent(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      );

      // Navigate directly to details URL with query param
      const detailsUrl = `${TRACKING_CONFIG.dpdTrackingUrl}?parcelNumber=${encodeURIComponent(
        trackingNumber
      )}`;

      await page.goto(detailsUrl, {
        waitUntil: 'networkidle2',
        timeout: TRACKING_CONFIG.dpdTimeout
      });

      // Accept cookies banner if present
      try {
        await page.click('#onetrust-accept-btn-handler', { timeout: 3000 });
      } catch {}

      // Wait for parcel status timeline to render
      await page.waitForSelector('.parcelStatus', { timeout: TRACKING_CONFIG.dpdTimeout });

      const rawEvents = await page.$$eval('.parcelStatus .row', (rows: Element[]) => {
        return rows.map((row: Element) => {
          const status = (row.querySelector('.col-xs-7 span') as HTMLElement)?.innerText.trim();
          const date = (row.querySelector('.col-xs-5 .inlineDate') as HTMLElement)?.innerText.trim();
          return { status, timestamp: date ?? '' };
        });
      });

      const events: StandardizedTrackingEvent[] = rawEvents.map((e: any) => {
        const mapping = StatusMapper.mapDPDStatus(e.status || 'UNKNOWN');
        return {
          status: mapping.standard,
          statusText: {
            en: mapping.textEn,
            lt: mapping.textLt,
            original: e.status || 'UNKNOWN'
          },
          timestamp: StatusMapper.normalizeTimestamp(e.timestamp),
          rawData: e
        };
      });

      const latestEvent = events.at(-1);
      const latestMapping = latestEvent ?
        { standard: latestEvent.status, textEn: latestEvent.statusText.en, textLt: latestEvent.statusText.lt } :
        { standard: StandardTrackingStatus.EXCEPTION, textEn: 'Unknown', textLt: 'Nežinoma' };

      return {
        provider: this.name,
        trackingNumber,
        status: latestMapping.standard,
        statusText: {
          en: latestMapping.textEn,
          lt: latestMapping.textLt,
          original: latestEvent?.statusText.original || 'UNKNOWN'
        },
        events,
        lastUpdated: latestEvent?.timestamp || new Date().toISOString()
      };
    } finally {
      await browser.close();
    }
  }
}