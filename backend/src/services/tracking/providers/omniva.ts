import { ITrackingProvider } from './base';
import { StandardizedTrackingResult, StandardizedTrackingEvent, StandardTrackingStatus } from '../../../types/standardized-tracking';
import { StatusMapper } from '../../../utils/status-mapping';
import { TRACKING_CONFIG } from '../../../config';

export class OmnivaTrackingProvider implements ITrackingProvider {
  readonly name = 'OMNIVA' as const;
  private readonly endpoint = TRACKING_CONFIG.omnivaTrackingUrl;

  /**
   * Omniva's Cloudflare configuration appears to block requests made with Node's
   * built-in `fetch` (Undici) – the same URL succeeds with cURL and Axios but
   * returns a 403 when fetched via Undici. Switching to Axios (which uses the
   * classic `http` module) reliably bypasses the WAF without resorting to headless
   * browsers or other heavy work-arounds.
   */
  async track(trackingNumber: string): Promise<StandardizedTrackingResult> {
    const { default: axios } = await import('axios');

    const url = `${this.endpoint}/${trackingNumber}`;

    let response;
    try {
      response = await axios.get(url, {
        headers: {
          Accept: 'application/json, text/plain, */*',
          'Accept-Language': 'en',
          // A lightweight UA string avoids Cloudflare bot heuristics
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        },
        // Use configured timeout
        timeout: TRACKING_CONFIG.defaultTimeout
      });
    } catch (err: any) {
      const status = err.response?.status ?? 'unknown';
      throw new Error(`Omniva tracking failed: ${status}`);
    }

    const data: any = response.data;

    const events: StandardizedTrackingEvent[] = (data?.events ?? []).map((e: any) => {
      const mapping = StatusMapper.mapOmnivaStatus(
        e.eventCode || 'UNKNOWN',
        e.eventName || 'UNKNOWN'
      );

      return {
        status: mapping.standard,
        statusText: {
          en: mapping.textEn,
          lt: mapping.textLt,
          original: e.eventName || 'UNKNOWN'
        },
        location: e.location?.locationName,
        timestamp: StatusMapper.normalizeTimestamp(e.eventDate),
        providerEventId: e.eventId?.toString(),
        providerStatusCode: e.eventCode,
        rawData: e
      };
    });

    const latestEvent = events[events.length - 1];
    const latestMapping = latestEvent ?
      StatusMapper.mapOmnivaStatus(latestEvent.providerStatusCode || 'UNKNOWN', latestEvent.statusText.original) :
      { standard: StandardTrackingStatus.EXCEPTION, textEn: 'Unknown', textLt: 'Nežinoma' };

    return {
      provider: this.name,
      trackingNumber,
      status: latestMapping.standard,
      statusText: {
        en: latestMapping.textEn,
        lt: latestMapping.textLt,
        original: latestEvent?.statusText.original || data?.currentStateCode || 'UNKNOWN'
      },
      events,
      lastUpdated: latestEvent?.timestamp || new Date().toISOString()
    };
  }
}