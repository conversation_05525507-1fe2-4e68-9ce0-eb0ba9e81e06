import { PostgreSQLCacheService } from '../cache-service';
import { StandardizedTrackingResult, StandardTrackingStatus } from '../../types/standardized-tracking';
import { TRACKING_SERVICE_CONFIG } from '../../config';
import { getNotificationService } from '../notification-service';
import { LPExpressTrackingProvider } from './providers/lp-express';
import { OmnivaTrackingProvider } from './providers/omniva';
import { VenipakTrackingProvider } from './providers/venipak';
import { DPDTrackingProvider } from './providers/dpd';
import { ITrackingProvider } from './providers/base';

export class TrackingService {
  private cache = new PostgreSQLCacheService();
  private providers: Record<string, ITrackingProvider>;
  private failures: Record<string, { count: number; openUntil: number }> = {};
  private notificationService = getNotificationService();

  constructor() {
    this.providers = {
      LP_EXPRESS: new LPExpressTrackingProvider(),
      OMNIVA: new OmnivaTrackingProvider(),
      VENIPAK: new VenipakTrackingProvider(),
      DPD: new DPDTrackingProvider()
    } as const;
  }

  async track(provider: string, trackingNumber: string, refresh = false): Promise<{ result: StandardizedTrackingResult; cacheHit: boolean }> {
    const provKey = provider.toUpperCase();
    const adapter = this.providers[provKey];
    if (!adapter) {
      throw new Error(`Unsupported provider: ${provider}`);
    }

    const cacheKey = this.cache.generateKey('tracking', provKey, trackingNumber);

    if (refresh) {
      await this.cache.delete(cacheKey);
    } else {
      const cached = await this.cache.get<StandardizedTrackingResult>(cacheKey);
      if (cached) {
        return { result: cached, cacheHit: true };
      }
    }

    // Circuit-breaker logic (same as existing)
    const breakState = this.failures[provKey];
    if (breakState && breakState.openUntil > Date.now()) {
      const err: any = new Error('Provider temporarily unavailable');
      err.statusCode = 503;
      throw err;
    }

    try {
      const result = await adapter.track(trackingNumber);

      // Reset failure count
      if (this.failures[provKey]) this.failures[provKey].count = 0;

      // Persist to DB (fire-and-forget)
      this.persist(result).catch(() => {/* ignore */});

      // Adaptive TTL - configurable cache durations
      const deliveredStatuses = [StandardTrackingStatus.DELIVERED, StandardTrackingStatus.RETURNED];
      const ttl = deliveredStatuses.includes(result.status) ? TRACKING_SERVICE_CONFIG.deliveredCacheTtl : TRACKING_SERVICE_CONFIG.activeCacheTtl;

      await this.cache.set(cacheKey, result, { ttl });
      return { result, cacheHit: false };
    } catch (err) {
      // Same error handling as existing track method
      const entry = this.failures[provKey] || { count: 0, openUntil: 0 };
      entry.count += 1;

      const errorMessage = err instanceof Error ? err.message : 'Unknown tracking error';

      if (entry.count >= TRACKING_SERVICE_CONFIG.maxFailureCount) {
        entry.openUntil = Date.now() + TRACKING_SERVICE_CONFIG.circuitBreakerTimeout;

        // Notify about circuit breaker activation
        this.notificationService.notifyTrackingServiceFailure(
          provKey,
          errorMessage,
          entry.count,
          true // Circuit breaker activated
        ).catch(() => {/* ignore notification failures */});

        entry.count = 0;
      } else {
        // Notify about tracking failure (but not on every failure to avoid spam)
        if (entry.count === 1 || entry.count % 3 === 0) {
          this.notificationService.notifyTrackingServiceFailure(
            provKey,
            errorMessage,
            entry.count,
            false // Circuit breaker not activated
          ).catch(() => {/* ignore notification failures */});
        }
      }

      this.failures[provKey] = entry;

      if ((err as any).statusCode == null) {
        (err as any).statusCode = 503;
      }
      throw err;
    }
  }

  private async persist(result: StandardizedTrackingResult): Promise<void> {
    const { getDatabasePool } = await import('../../database/connection');
    const pool = getDatabasePool();

    await pool.query(
      `INSERT INTO tracking_snapshots (provider, tracking_number, status, last_event_time, snapshot)
       VALUES ($1,$2,$3,$4,$5)
       ON CONFLICT (provider, tracking_number)
       DO UPDATE SET status = EXCLUDED.status, last_event_time = EXCLUDED.last_event_time, snapshot = EXCLUDED.snapshot, updated_at = NOW()`,
      [result.provider, result.trackingNumber, result.status, result.lastUpdated, result]
    );

    // Insert standardized events
    for (const ev of result.events) {
      await pool.query(
        `INSERT INTO tracking_events (provider, tracking_number, status, location, event_time)
         VALUES ($1,$2,$3,$4,$5)`,
        [result.provider, result.trackingNumber, ev.status, ev.location || null, ev.timestamp]
      );
    }
  }
} 