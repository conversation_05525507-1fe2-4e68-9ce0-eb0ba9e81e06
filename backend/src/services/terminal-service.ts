import { getDatabasePool, withBulkTransaction } from '../database/connection';
import { cacheService } from './cache-service';
import {
  BulkImportType,
  TerminalResponseType,
  GetTerminalsQueryType,
  SearchTerminalsQueryType,
  NearbyTerminalsQueryType,
  PaginatedResponseType
} from '../validation/schemas';
import { CONFIG } from '../config';

// Structured logger
const logger = {
  info: (message: string, data?: any) => console.log(`[${new Date().toISOString()}] INFO: ${message}`, data || ''),
  error: (message: string, error?: any) => console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error || ''),
  debug: (message: string, data?: any) => console.log(`[${new Date().toISOString()}] DEBUG: ${message}`, data || '')
};

interface ImportResult {
  success: boolean;
  imported: number;
  updated: number;
  errors: string[];
}

interface SearchOptions {
  useFullTextSearch?: boolean;
  useTrigrams?: boolean;
  includeInactive?: boolean;
}

export class TerminalService {
  
  async getTerminalById(id: string, tenantId?: string): Promise<TerminalResponseType | null> {
    const cacheKey = cacheService.generateTerminalKey('detail', id);

    return await cacheService.getOrSet(
      cacheKey,
      async () => {
        const pool = getDatabasePool();

        try {
          const result = await pool.query(`
            SELECT
              id,
              name,
              city,
              address,
              postal_code,
              ST_Y(coordinates::geometry) as lat,
              ST_X(coordinates::geometry) as lng,
              updated,
              provider,
              terminal_type,
              is_active
            FROM terminals
            WHERE id = $1 AND is_active = true
          `, [id]);

          if (result.rows.length === 0) {
            return null;
          }

          const row = result.rows[0];
          return this.mapRowToTerminal(row);
        } catch (error) {
          logger.error('Error fetching terminal by ID:', error);
          throw new Error('Failed to fetch terminal');
        }
      },
      { ttl: CONFIG.cache.terminalDetailTtl, ...(tenantId && { tenantId }) } // Configurable terminal detail cache TTL
    );
  }

  async getTerminals(query: GetTerminalsQueryType, tenantId?: string): Promise<PaginatedResponseType> {
    const cacheKey = cacheService.generateTerminalKey(
      'list',
      query.page,
      query.limit,
      query.sortBy,
      query.sortOrder,
      query.city || 'all',
      query.postalCode || 'all',
      query.provider || 'all',
      query.terminalType || 'all'
    );

    return await cacheService.getOrSet(
      cacheKey,
      async () => {
        const pool = getDatabasePool();
        const startTime = Date.now();

        try {
          const { whereClause, params, paramCount } = this.buildWhereClause(query);
          const { orderClause } = this.buildOrderClause(query.sortBy, query.sortOrder);
          const offset = (query.page - 1) * query.limit;

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM terminals 
        ${whereClause}
      `;
      const countResult = await pool.query(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results
      const dataQuery = `
        SELECT 
          id,
          name,
          city,
          address,
          postal_code,
          ST_Y(coordinates::geometry) as lat,
          ST_X(coordinates::geometry) as lng,
          updated,
          provider,
          terminal_type,
          is_active
        FROM terminals 
        ${whereClause}
        ${orderClause}
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
      `;
      
      const dataResult = await pool.query(dataQuery, [...params, query.limit, offset]);
      
      const terminals = dataResult.rows.map(row => this.mapRowToTerminal(row));
      const totalPages = Math.ceil(total / query.limit);
      
          return {
            data: terminals,
            pagination: {
              page: query.page,
              limit: query.limit,
              total,
              totalPages,
              hasNext: query.page < totalPages,
              hasPrev: query.page > 1
            },
            meta: {
              requestId: 'generated-request-id', // This should come from request context
              timestamp: new Date().toISOString(),
              processingTime: Date.now() - startTime
            }
          };
        } catch (error) {
          logger.error('Error fetching terminals:', error);
          throw new Error('Failed to fetch terminals');
        }
      },
      { ttl: CONFIG.cache.terminalListTtl, ...(tenantId && { tenantId }) } // Configurable terminal list cache TTL
    );
  }

  async searchTerminals(query: SearchTerminalsQueryType, options: SearchOptions = {}): Promise<PaginatedResponseType> {
    const pool = getDatabasePool();
    const startTime = Date.now();
    
    try {
      const searchQuery = query.q.toLowerCase().trim();
      const { whereClause, params, paramCount } = this.buildSearchWhereClause(searchQuery, query, options);
      const { orderClause } = this.buildSearchOrderClause(query.sortBy, query.sortOrder);
      const offset = (query.page - 1) * query.limit;

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM terminals 
        ${whereClause}
      `;
      const countResult = await pool.query(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results with relevance scoring
      const relevanceScoreExpression = searchQuery ? this.buildRelevanceScore(1) : '0'; // Use parameter $1 since searchQuery is first in params
      
      const dataQuery = `
        SELECT
          id,
          name,
          city,
          address,
          postal_code,
          ST_Y(coordinates::geometry) as lat,
          ST_X(coordinates::geometry) as lng,
          updated,
          provider,
          terminal_type,
          is_active,
          ${relevanceScoreExpression} as relevance_score
        FROM terminals
        ${whereClause}
        ${orderClause}
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
      `;

      // Don't add searchQuery again - it's already in params from buildSearchWhereClause
      const dataResult = await pool.query(dataQuery, [...params, query.limit, offset]);
      
      const terminals = dataResult.rows.map(row => this.mapRowToTerminal(row));
      const totalPages = Math.ceil(total / query.limit);
      
      return {
        data: terminals,
        pagination: {
          page: query.page,
          limit: query.limit,
          total,
          totalPages,
          hasNext: query.page < totalPages,
          hasPrev: query.page > 1
        },
        meta: {
          requestId: 'generated-request-id',
          timestamp: new Date().toISOString(),
          processingTime: Date.now() - startTime
        }
      };
    } catch (error) {
      logger.error('Error searching terminals:', error);
      throw new Error('Failed to search terminals');
    }
  }

  async getNearbyTerminals(query: NearbyTerminalsQueryType): Promise<PaginatedResponseType> {
    const pool = getDatabasePool();
    const startTime = Date.now();
    
    try {
      const { whereClause, params, paramCount } = this.buildNearbyWhereClause(query);
      const offset = (query.page - 1) * query.limit;

      // Get total count within radius
      const countQuery = `
        SELECT COUNT(*) as total
        FROM terminals 
        ${whereClause}
      `;
      const countResult = await pool.query(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results with distance
      const dataQuery = `
        SELECT 
          id,
          name,
          city,
          address,
          postal_code,
          ST_Y(coordinates::geometry) as lat,
          ST_X(coordinates::geometry) as lng,
          updated,
          provider,
          terminal_type,
          is_active,
          ST_Distance(
            coordinates,
            ST_SetSRID(ST_MakePoint($${paramCount + 3}, $${paramCount + 4}), 4326)::geography
          ) / 1000 as distance
        FROM terminals 
        ${whereClause}
        ORDER BY distance ASC
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
      `;
      
      const dataResult = await pool.query(dataQuery, [...params, query.limit, offset, query.lng, query.lat]);
      
      const terminals = dataResult.rows.map(row => {
        const terminal = this.mapRowToTerminal(row);
        terminal.distance = parseFloat(row.distance);
        return terminal;
      });
      
      const totalPages = Math.ceil(total / query.limit);
      
      return {
        data: terminals,
        pagination: {
          page: query.page,
          limit: query.limit,
          total,
          totalPages,
          hasNext: query.page < totalPages,
          hasPrev: query.page > 1
        },
        meta: {
          requestId: 'generated-request-id',
          timestamp: new Date().toISOString(),
          processingTime: Date.now() - startTime
        }
      };
    } catch (error) {
      logger.error('Error fetching nearby terminals:', error);
      throw new Error('Failed to fetch nearby terminals');
    }
  }

  async bulkImport(importData: BulkImportType): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      imported: 0,
      updated: 0,
      errors: []
    };

    const startTime = Date.now();
    const batchSize = 100; // Process in batches of 100

    return await withBulkTransaction(async (pool) => {

      // If replacing existing, delete old terminals from this provider
      if (importData.replaceExisting) {
        const deleteResult = await pool.query(
          'DELETE FROM terminals WHERE provider = $1',
          [importData.provider]
        );
        logger.info('🗑️  Deleted existing terminals', {
          provider: importData.provider,
          deletedCount: deleteResult.rowCount || 0
        });
      }

      // Process terminals in batches for better performance
      const terminals = importData.terminals;
      const totalBatches = Math.ceil(terminals.length / batchSize);

      logger.info(`📦 Processing ${terminals.length} terminals in ${totalBatches} batches of ${batchSize}`);

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const batchStart = batchIndex * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, terminals.length);
        const batch = terminals.slice(batchStart, batchEnd);

        try {
          // Build batch insert query with multiple VALUES
          const values: any[] = [];
          const placeholders: string[] = [];
          let paramIndex = 1;

          for (const terminal of batch) {
            placeholders.push(`($${paramIndex}, $${paramIndex + 1}, $${paramIndex + 2}, $${paramIndex + 3}, $${paramIndex + 4}, ST_SetSRID(ST_MakePoint($${paramIndex + 5}, $${paramIndex + 6}), 4326)::geography, $${paramIndex + 7}, $${paramIndex + 8}, $${paramIndex + 9}, NOW())`);

            values.push(
              terminal.id,
              terminal.name,
              terminal.city,
              terminal.address,
              terminal.postalCode,
              terminal.coordinates.lng,
              terminal.coordinates.lat,
              terminal.provider,
              terminal.terminalType,
              JSON.stringify(terminal.metadata)
            );

            paramIndex += 10;
          }

          const batchQuery = `
            INSERT INTO terminals (
              id, name, city, address, postal_code, coordinates,
              provider, terminal_type, metadata, updated
            ) VALUES ${placeholders.join(', ')}
            ON CONFLICT (id) DO UPDATE SET
              name = EXCLUDED.name,
              city = EXCLUDED.city,
              address = EXCLUDED.address,
              postal_code = EXCLUDED.postal_code,
              coordinates = EXCLUDED.coordinates,
              provider = EXCLUDED.provider,
              terminal_type = EXCLUDED.terminal_type,
              metadata = EXCLUDED.metadata,
              updated = NOW()
          `;

          const batchResult = await pool.query(batchQuery, values);
          const batchImported = batchResult.rowCount || 0;
          result.imported += batchImported;

          logger.info(`📦 Batch ${batchIndex + 1}/${totalBatches}: ${batchImported} terminals processed`);

        } catch (error) {
          const errorMsg = `Batch ${batchIndex + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
          logger.error(`❌ ${errorMsg}`);

          // Continue with next batch instead of failing completely
          continue;
        }
      }

      result.success = result.errors.length === 0;

      const duration = Date.now() - startTime;
      logger.info('✅ Bulk import completed', {
        imported: result.imported,
        errors: result.errors.length,
        duration: `${duration}ms`,
        rate: `${Math.round(result.imported / (duration / 1000))} terminals/sec`
      });

      return result;
    });
  }

  private mapRowToTerminal(row: any): TerminalResponseType {
    return {
      id: row.id,
      name: row.name,
      city: row.city,
      address: row.address,
      postalCode: row.postal_code,
      coordinates: {
        lat: parseFloat(row.lat),
        lng: parseFloat(row.lng)
      },
      updated: row.updated.toISOString(),
      provider: row.provider,
      terminalType: row.terminal_type,
      isActive: row.is_active,
      ...(row.distance !== undefined && { distance: parseFloat(row.distance) })
    };
  }

  private buildWhereClause(query: GetTerminalsQueryType): { whereClause: string; params: any[]; paramCount: number } {
    const conditions: string[] = ['is_active = true'];
    const params: any[] = [];
    let paramCount = 0;

    if (query.city) {
      paramCount++;
      conditions.push(`city ILIKE $${paramCount}`);
      params.push(`%${query.city}%`);
    }

    if (query.postalCode) {
      paramCount++;
      conditions.push(`postal_code = $${paramCount}`);
      params.push(query.postalCode);
    }

    if (query.provider) {
      paramCount++;
      conditions.push(`provider = $${paramCount}`);
      params.push(query.provider);
    }

    if (query.terminalType) {
      paramCount++;
      conditions.push(`terminal_type = $${paramCount}`);
      params.push(query.terminalType);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { whereClause, params, paramCount };
  }

  private buildSearchWhereClause(
    searchQuery: string,
    query: SearchTerminalsQueryType,
    options: SearchOptions
  ): { whereClause: string; params: any[]; paramCount: number } {
    const conditions: string[] = [];
    const params: any[] = [];
    let paramCount = 0;

    // Base condition for active terminals
    if (!options.includeInactive) {
      conditions.push('is_active = true');
    }

    // Search conditions
    if (searchQuery) {
      paramCount++;
      const searchConditions: string[] = [];

      // Full-text search using search vector
      if (options.useFullTextSearch !== false) {
        searchConditions.push(`search_vector @@ plainto_tsquery('simple', $${paramCount})`);
      }

      // Trigram similarity search
      if (options.useTrigrams !== false) {
        searchConditions.push(`(
          name % $${paramCount} OR
          city % $${paramCount} OR
          address % $${paramCount} OR
          postal_code % $${paramCount}
        )`);
      }

      // Fallback ILIKE search
      searchConditions.push(`(
        name ILIKE '%' || $${paramCount} || '%' OR
        city ILIKE '%' || $${paramCount} || '%' OR
        address ILIKE '%' || $${paramCount} || '%' OR
        postal_code ILIKE '%' || $${paramCount} || '%'
      )`);

      conditions.push(`(${searchConditions.join(' OR ')})`);
      params.push(searchQuery);
    }

    // Additional filters
    if (query.city) {
      paramCount++;
      conditions.push(`city ILIKE $${paramCount}`);
      params.push(`%${query.city}%`);
    }

    if (query.provider) {
      paramCount++;
      conditions.push(`provider = $${paramCount}`);
      params.push(query.provider);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { whereClause, params, paramCount };
  }

  private buildNearbyWhereClause(query: NearbyTerminalsQueryType): { whereClause: string; params: any[]; paramCount: number } {
    const conditions: string[] = ['is_active = true'];
    const params: any[] = [];
    let paramCount = 0;

    // Distance condition
    conditions.push(`ST_DWithin(
      coordinates,
      ST_SetSRID(ST_MakePoint($1, $2), 4326)::geography,
      $3
    )`);
    params.push(query.lng, query.lat, query.radius * 1000); // Convert km to meters
    paramCount = 3;

    // Additional filters
    if (query.city) {
      paramCount++;
      conditions.push(`city ILIKE $${paramCount}`);
      params.push(`%${query.city}%`);
    }

    if (query.provider) {
      paramCount++;
      conditions.push(`provider = $${paramCount}`);
      params.push(query.provider);
    }

    if (query.terminalType) {
      paramCount++;
      conditions.push(`terminal_type = $${paramCount}`);
      params.push(query.terminalType);
    }

    const whereClause = `WHERE ${conditions.join(' AND ')}`;
    return { whereClause, params, paramCount };
  }

  private buildOrderClause(sortBy: string, sortOrder: string): { orderClause: string } {
    const validSortFields: Record<string, string> = {
      name: 'name',
      city: 'city',
      updated: 'updated',
      popularity: 'popularity_score'
    };

    const field = validSortFields[sortBy] || 'name';
    const order = sortOrder === 'desc' ? 'DESC' : 'ASC';

    return { orderClause: `ORDER BY ${field} ${order}` };
  }

  private buildSearchOrderClause(sortBy: string, sortOrder: string): { orderClause: string } {
    if (sortBy === 'relevance' || !sortBy) {
      // Use the correct parameter index for relevance score (searchQuery is parameter $1)
      return { orderClause: `ORDER BY ${this.buildRelevanceScore(1)} DESC, name ASC` };
    }

    return this.buildOrderClause(sortBy, sortOrder);
  }

  private buildRelevanceScore(paramIndex: number): string {
    // Use parameterized query placeholders with the correct parameter index
    return `(
      CASE
        WHEN name ILIKE $${paramIndex} || '%' THEN 100
        WHEN name ILIKE '%' || $${paramIndex} || '%' THEN 80
        WHEN city ILIKE $${paramIndex} || '%' THEN 60
        WHEN address ILIKE '%' || $${paramIndex} || '%' THEN 40
        WHEN postal_code = $${paramIndex} THEN 90
        ELSE 20
      END +
      COALESCE(popularity_score, 0) * 0.1
    )`;
  }
}
