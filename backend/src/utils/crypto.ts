import { createHmac, randomBytes } from 'crypto';
import { validateEnvironment } from '../config';

// API key hashing
export function hashApiKey(apiKey: string): string {
  const env = validateEnvironment();
  return createHmac('sha256', env.API_KEY_SECRET)
    .update(apiKey)
    .digest('hex');
}

// Generate secure API key
export function generateApiKey(): string {
  return `ptapi_${  randomBytes(32).toString('hex')}`;
}

// Validate API key format
export function validateApiKeyFormat(apiKey: string): boolean {
  return /^ptapi_[a-f0-9]{64}$/.test(apiKey);
}

// Generate request ID
export function generateRequestId(): string {
  return `req_${  randomBytes(8).toString('hex')}`;
}

// Generate secure token for email verification and password reset
export function generateSecureToken(length: number = 32): string {
  return randomBytes(length).toString('hex');
}

// Hash token for secure storage
export function hashToken(token: string): string {
  const env = validateEnvironment();
  return createHmac('sha256', env.API_KEY_SECRET)
    .update(token)
    .digest('hex');
}
