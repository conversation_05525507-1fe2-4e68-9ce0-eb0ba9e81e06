/**
 * Configuration masking utilities to prevent sensitive data exposure
 */

export interface MaskedConfig {
  [key: string]: any;
}

// Sensitive configuration keys that should be masked
const SENSITIVE_KEYS = [
  'DATABASE_URL',
  'API_KEY_SECRET',
  'password',
  'secret',
  'key',
  'token',
  'auth',
  'credential',
  'private'
];

// Patterns for sensitive values
const SENSITIVE_VALUE_PATTERNS = [
  // Database URLs
  /^(postgresql|postgres|mysql|mongodb):\/\/.*$/i,
  
  // API keys
  /^[a-zA-Z0-9_-]{32,}$/,
  
  // JWT tokens
  /^eyJ[a-zA-Z0-9_-]*\.[a-zA-Z0-9_-]*\.[a-zA-Z0-9_-]*$/,
  
  // Base64 encoded secrets (likely)
  /^[A-Za-z0-9+/]{40,}={0,2}$/,
  
  // Hex encoded secrets (likely)
  /^[a-fA-F0-9]{32,}$/
];

/**
 * Check if a key is sensitive
 */
export function isSensitiveKey(key: string): boolean {
  const lowerKey = key.toLowerCase();
  return SENSITIVE_KEYS.some(sensitiveKey => 
    lowerKey.includes(sensitiveKey.toLowerCase())
  );
}

/**
 * Check if a value appears to be sensitive
 */
export function isSensitiveValue(value: any): boolean {
  if (typeof value !== 'string') {
    return false;
  }
  
  // Check against patterns
  return SENSITIVE_VALUE_PATTERNS.some(pattern => pattern.test(value));
}

/**
 * Mask a sensitive value
 */
export function maskValue(value: any, showLength: boolean = true): string {
  if (value === null || value === undefined) {
    return '[NULL]';
  }
  
  const stringValue = String(value);
  
  if (stringValue.length === 0) {
    return '[EMPTY]';
  }
  
  if (stringValue.length <= 4) {
    return '[REDACTED]';
  }
  
  // Show first 2 and last 2 characters with length info
  const masked = showLength 
    ? `${stringValue.substring(0, 2)}***${stringValue.substring(stringValue.length - 2)} (${stringValue.length} chars)`
    : `${stringValue.substring(0, 2)}***${stringValue.substring(stringValue.length - 2)}`;
    
  return masked;
}

/**
 * Mask configuration object recursively
 */
export function maskConfiguration(config: any, depth: number = 0): any {
  if (depth > 10) {
    return '[MAX_DEPTH_REACHED]';
  }

  if (config === null || config === undefined) {
    return config;
  }

  if (typeof config === 'string' || typeof config === 'number' || typeof config === 'boolean') {
    return config;
  }
  
  if (Array.isArray(config)) {
    return config.map(item => maskConfiguration(item, depth + 1));
  }
  
  if (typeof config === 'object') {
    const masked: MaskedConfig = {};
    
    for (const [key, value] of Object.entries(config)) {
      if (isSensitiveKey(key) || isSensitiveValue(value)) {
        masked[key] = maskValue(value);
      } else if (typeof value === 'object') {
        masked[key] = maskConfiguration(value, depth + 1);
      } else {
        masked[key] = value;
      }
    }
    
    return masked;
  }
  
  return config;
}

/**
 * Get masked environment variables
 */
export function getMaskedEnvironment(): MaskedConfig {
  const maskedEnv: MaskedConfig = {};
  
  for (const [key, value] of Object.entries(process.env)) {
    if (isSensitiveKey(key) || isSensitiveValue(value)) {
      maskedEnv[key] = maskValue(value);
    } else {
      maskedEnv[key] = value;
    }
  }
  
  return maskedEnv;
}

/**
 * Safe configuration logging
 */
export function logConfiguration(config: any, label: string = 'Configuration') {
  const masked = maskConfiguration(config);
  console.log(`${label}:`, JSON.stringify(masked, null, 2));
}

/**
 * Safe environment logging
 */
export function logEnvironment(label: string = 'Environment Variables') {
  const masked = getMaskedEnvironment();
  console.log(`${label}:`, JSON.stringify(masked, null, 2));
}

/**
 * Create safe configuration for API responses
 */
export function createSafeConfig(config: any): MaskedConfig {
  const safe = maskConfiguration(config);
  
  // Remove completely sensitive sections
  const toRemove = ['database', 'secrets', 'auth', 'security'];
  
  for (const section of toRemove) {
    if (safe[section]) {
      safe[section] = '[REDACTED_SECTION]';
    }
  }
  
  return safe;
}

/**
 * Validate that no sensitive data is exposed
 */
export function validateNoSensitiveExposure(data: any): boolean {
  const stringified = JSON.stringify(data);
  
  // Check for common sensitive patterns in the stringified data
  const exposurePatterns = [
    // Database connection strings
    /postgresql:\/\/[^"'\s]+/gi,
    /postgres:\/\/[^"'\s]+/gi,
    /mysql:\/\/[^"'\s]+/gi,
    
    // API keys (our format)
    /ptapi_[a-f0-9]{64}/gi,
    
    // JWT tokens
    /eyJ[a-zA-Z0-9_-]*\.[a-zA-Z0-9_-]*\.[a-zA-Z0-9_-]*/gi,
    
    // Long hex strings (likely secrets)
    /[a-fA-F0-9]{40,}/g,
    
    // Long base64 strings (likely secrets)
    /[A-Za-z0-9+/]{40,}={0,2}/g
  ];
  
  for (const pattern of exposurePatterns) {
    if (pattern.test(stringified)) {
      console.warn('Potential sensitive data exposure detected:', pattern);
      return false;
    }
  }
  
  return true;
}

/**
 * Create development-safe configuration
 */
export function createDevSafeConfig(config: any): MaskedConfig {
  // In development, show more info but still mask secrets
  const masked = { ...config };
  
  const sensitiveFields = ['DATABASE_URL', 'API_KEY_SECRET'];
  
  for (const field of sensitiveFields) {
    if (masked[field]) {
      masked[field] = maskValue(masked[field], true);
    }
  }
  
  return masked;
}

/**
 * Create production-safe configuration
 */
export function createProdSafeConfig(config: any): MaskedConfig {
  // In production, be very restrictive
  const safe: MaskedConfig = {
    environment: config.environment || 'unknown',
    version: config.version || 'unknown',
    port: config.port || 'unknown'
  };
  
  // Everything else is redacted in production
  return safe;
}
