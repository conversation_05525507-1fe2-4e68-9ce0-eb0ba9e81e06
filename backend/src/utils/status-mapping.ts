/**
 * Status mapping utilities for standardizing tracking events across providers
 */

import { StandardTrackingStatus } from '../types/standardized-tracking';
import { languageService } from '../services/language-service';

export interface StatusMapping {
  standard: StandardTrackingStatus;
  textEn: string;
  textLt?: string;
}

export class StatusMapper {
  // Provider status code to standard status mapping
  private static readonly LP_EXPRESS_STATUS_MAPPING: Record<string, StandardTrackingStatus> = {
    'LABEL_CREATED': StandardTrackingStatus.CREATED,
    'PARCEL_RECEIVED': StandardTrackingStatus.ACCEPTED,
    'ON_THE_WAY': StandardTrackingStatus.IN_TRANSIT,
    'PARCEL_DELIVERED': StandardTrackingStatus.DELIVERED,
    'RETURNED': StandardTrackingStatus.RETURNED
  };

  private static readonly OMNIVA_STATUS_MAPPING: Record<string, StandardTrackingStatus> = {
    'TRT_SHIPMENT_REGISTERED': StandardTrackingStatus.CREATED,
    'TRT_SHIPMENT_HANDOVER_TO_PM': StandardTrackingStatus.ACCEPTED,
    'TRT_SHIPMENT_DEPARTED_FROM_PM': StandardTrackingStatus.IN_TRANSIT,
    'TRT_SHIPMENT_ARRIVED_TO_PO': StandardTrackingStatus.IN_TRANSIT,
    'TRT_SHIPMENT_STORED_IN_PM': StandardTrackingStatus.OUT_FOR_DELIVERY,
    'TRT_SHIPMENT_DELIVERED': StandardTrackingStatus.DELIVERED
  };

  private static readonly VENIPAK_STATUS_MAPPING: Record<string, StandardTrackingStatus> = {
    '1': StandardTrackingStatus.IN_TRANSIT,
    '2': StandardTrackingStatus.OUT_FOR_DELIVERY,
    '4': StandardTrackingStatus.OUT_FOR_DELIVERY,
    '5': StandardTrackingStatus.DELIVERED,
    '9': StandardTrackingStatus.DELIVERED
  };

  /**
   * Map LP Express status to standardized format
   */
  static mapLPExpressStatus(providerStatus: string, originalText: string): StatusMapping {
    const standardStatus = this.LP_EXPRESS_STATUS_MAPPING[providerStatus] || StandardTrackingStatus.EXCEPTION;

    return {
      standard: standardStatus,
      textEn: languageService.getProviderStatusTranslation('LP_EXPRESS', providerStatus, 'en') ||
              languageService.getStatusTranslation(standardStatus, 'en') ||
              originalText,
      textLt: languageService.getProviderStatusTranslation('LP_EXPRESS', providerStatus, 'lt') ||
              languageService.getStatusTranslation(standardStatus, 'lt') ||
              originalText
    };
  }

  /**
   * Map Omniva status to standardized format
   */
  static mapOmnivaStatus(eventCode: string, originalText: string): StatusMapping {
    const standardStatus = this.OMNIVA_STATUS_MAPPING[eventCode] || StandardTrackingStatus.EXCEPTION;

    return {
      standard: standardStatus,
      textEn: languageService.getProviderStatusTranslation('OMNIVA', eventCode, 'en') ||
              languageService.getStatusTranslation(standardStatus, 'en') ||
              originalText,
      textLt: languageService.getProviderStatusTranslation('OMNIVA', eventCode, 'lt') ||
              languageService.getStatusTranslation(standardStatus, 'lt') ||
              originalText
    };
  }

  /**
   * Map Venipak status to standardized format
   */
  static mapVenipakStatus(statusCode: string, originalText: string): StatusMapping {
    const standardStatus = this.VENIPAK_STATUS_MAPPING[statusCode] || StandardTrackingStatus.EXCEPTION;

    return {
      standard: standardStatus,
      textEn: languageService.getProviderStatusTranslation('VENIPAK', statusCode, 'en') ||
              languageService.getStatusTranslation(standardStatus, 'en') ||
              originalText,
      textLt: languageService.getProviderStatusTranslation('VENIPAK', statusCode, 'lt') ||
              languageService.getStatusTranslation(standardStatus, 'lt') ||
              originalText
    };
  }

  /**
   * Map DPD status to standardized format based on text patterns
   */
  static mapDPDStatus(statusText: string): StatusMapping {
    const lowerStatus = statusText.toLowerCase();
    let standardStatus = StandardTrackingStatus.EXCEPTION;
    let statusKey = 'unknown';

    if (lowerStatus.includes('delivered') || lowerStatus.includes('pristatyta')) {
      standardStatus = StandardTrackingStatus.DELIVERED;
      statusKey = 'delivered';
    } else if (lowerStatus.includes('transit') || lowerStatus.includes('kelyje')) {
      standardStatus = StandardTrackingStatus.IN_TRANSIT;
      statusKey = 'transit';
    } else if (lowerStatus.includes('out for delivery') || lowerStatus.includes('pristatoma')) {
      standardStatus = StandardTrackingStatus.OUT_FOR_DELIVERY;
      statusKey = 'out_for_delivery';
    } else if (lowerStatus.includes('accepted') || lowerStatus.includes('priimta')) {
      standardStatus = StandardTrackingStatus.ACCEPTED;
      statusKey = 'accepted';
    } else if (lowerStatus.includes('created') || lowerStatus.includes('sukurta')) {
      standardStatus = StandardTrackingStatus.CREATED;
      statusKey = 'created';
    }

    return {
      standard: standardStatus,
      textEn: languageService.getProviderStatusTranslation('DPD', statusKey, 'en') ||
              languageService.getStatusTranslation(standardStatus, 'en') ||
              statusText,
      textLt: languageService.getProviderStatusTranslation('DPD', statusKey, 'lt') ||
              languageService.getStatusTranslation(standardStatus, 'lt') ||
              statusText
    };
  }

  /**
   * Normalize timestamp to ISO 8601 format
   */
  static normalizeTimestamp(timestamp: string | undefined): string {
    if (!timestamp) return new Date().toISOString();
    
    try {
      return new Date(timestamp).toISOString();
    } catch {
      return new Date().toISOString();
    }
  }
}
