// Postal code standardization and validation
export function standardizePostalCode(postalCode: string | undefined, provider: string): string | undefined {
  if (!postalCode || typeof postalCode !== 'string') {
    return undefined;
  }
  
  let cleaned = postalCode.trim();
  
  // Remove country prefix for DPD data (e.g., "LT-12345" -> "12345")
  if (provider === 'DPD' && cleaned.startsWith('LT-')) {
    cleaned = cleaned.substring(3); // Remove "LT-" prefix
  } else if (provider === 'DPD' && cleaned.startsWith('LT')) {
    cleaned = cleaned.substring(2); // Remove "LT" prefix without dash
  }
  
  // Remove any remaining non-numeric characters except leading zeros
  cleaned = cleaned.replace(/[^0-9]/g, '');
  
  // Validate that result contains only digits
  if (!/^\d+$/.test(cleaned)) {
    console.warn(`Invalid postal code after standardization: ${postalCode} -> ${cleaned}`);
    return undefined;
  }
  
  // Ensure minimum length (Lithuanian postal codes are typically 5 digits)
  if (cleaned.length < 4 || cleaned.length > 6) {
    console.warn(`Postal code length out of range: ${cleaned} (length: ${cleaned.length})`);
    return undefined;
  }
  
  return cleaned;
}

// Validate postal code format for database storage
export function validatePostalCodeFormat(postalCode: string): boolean {
  // Must be numeric string only, 4-6 digits
  return /^\d{4,6}$/.test(postalCode);
}

// Coordinate validation
export function isValidCoordinate(lat: number, lng: number): boolean {
  return !isNaN(lat) && !isNaN(lng) && 
         lat >= -90 && lat <= 90 && 
         lng >= -180 && lng <= 180;
}

// Validate Baltic states coordinates (Lithuania, Latvia, Estonia)
export function isValidBalticCoordinate(lat: number, lng: number): boolean {
  return isValidCoordinate(lat, lng) &&
         lat >= 53.8 && lat <= 59.7 &&  // Baltic states latitude bounds (Lithuania to Estonia)
         lng >= 20.9 && lng <= 28.2;    // Baltic states longitude bounds
}

// Legacy function - kept for compatibility but now uses Baltic bounds
export function isValidLithuanianCoordinate(lat: number, lng: number): boolean {
  return isValidBalticCoordinate(lat, lng);
}
