#!/usr/bin/env node

import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });
config();

import { createDatabasePool, closeDatabasePool } from '../database/connection';
import { runMigrations, getMigrationStatus, rollbackLastMigration, resetDatabase } from '../database/migrations';

async function main() {
  const command = process.argv[2];
  
  try {
    // Initialize database connection
    console.log('🔗 Connecting to database...');
    createDatabasePool();
    
    switch (command) {
      case 'up':
      case undefined:
        await runMigrations();
        break;
        
      case 'status': {
        const status = await getMigrationStatus();
        console.log('\n📊 Migration Status:');
        console.log(`✅ Executed: ${status.executed.length} migrations`);
        status.executed.forEach(id => console.log(`   - ${id}`));
        console.log(`⏳ Pending: ${status.pending.length} migrations`);
        status.pending.forEach(id => console.log(`   - ${id}`));
        break;
      }
        
      case 'rollback':
        await rollbackLastMigration();
        break;
        
      case 'reset':
        console.log('⚠️  WARNING: This will DROP ALL TABLES and re-run migrations!');
        console.log('⚠️  All data will be lost. Press Ctrl+C to cancel...');
        // Give user 3 seconds to cancel
        await new Promise(resolve => setTimeout(resolve, 3000));
        await resetDatabase();
        break;
        
      default:
        console.log('Usage: npm run migrate [up|status|rollback|reset]');
        console.log('  up       - Run all pending migrations (default)');
        console.log('  status   - Show migration status');
        console.log('  rollback - Rollback last migration');
        console.log('  reset    - DROP ALL TABLES and re-run migrations (⚠️  DESTRUCTIVE)');
        process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await closeDatabasePool();
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down...');
  await closeDatabasePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down...');
  await closeDatabasePool();
  process.exit(0);
});

if (require.main === module) {
  main();
}
