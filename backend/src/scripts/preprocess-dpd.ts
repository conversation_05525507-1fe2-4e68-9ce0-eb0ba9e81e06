#!/usr/bin/env tsx

import { DPDDataCollector } from '../services/data-collection/dpd';

async function main() {
  console.log('🚀 Starting DPD geocoding preprocessing...');
  
  try {
    const dpdCollector = new DPDDataCollector();
    await dpdCollector.preprocessGeocodingCache();
    
    console.log('✅ DPD preprocessing completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ DPD preprocessing failed:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Preprocessing interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Preprocessing terminated');
  process.exit(1);
});

main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
}); 