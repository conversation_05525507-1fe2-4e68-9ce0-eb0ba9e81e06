#!/usr/bin/env node

import { config } from 'dotenv';

// Load environment variables from .env.local first, then .env
config({ path: '.env.local' });
config();
import { createDatabasePool, closeDatabasePool, getDatabasePool } from '../database/connection';
import { generateApiKey, hashApiKey } from '../utils/crypto';

interface CreateApiKeyOptions {
  name: string;
  description?: string;
  userId?: string;
  rateLimitPerMinute?: number;
  rateLimitPerDay?: number;
  rateLimitBurst?: number;
  subscriptionTier?: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  expiresAt?: Date;
}

async function createApiKeyInDatabase(options: CreateApiKeyOptions) {
  const apiKey = generateApiKey();
  const keyHash = hashApiKey(apiKey);
  const finalUserId = options.userId || null;

  const pool = getDatabasePool();

  try {
    const result = await pool.query(`
      INSERT INTO api_keys (
        key_hash, user_id, name, description,
        rate_limit_per_minute, rate_limit_per_day, rate_limit_burst,
        expires_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, created_at
    `, [
      keyHash,
      finalUserId,
      options.name,
      options.description || 'Created via script',
      options.rateLimitPerMinute || 1000,
      options.rateLimitPerDay || 50000,
      options.rateLimitBurst || 2000,
      options.expiresAt || null
    ]);

    const keyData = result.rows[0];

    console.log('✅ API Key created successfully!');
    console.log('');
    console.log('📋 API Key Details:');
    console.log(`  ID: ${keyData.id}`);
    console.log(`  Name: ${options.name}`);
    console.log(`  User ID: ${finalUserId}`);
    console.log(`  Tier: ${options.subscriptionTier || 'FREE'}`);
    console.log(`  Created: ${keyData.created_at}`);
    console.log(`  Rate Limit: ${options.rateLimitPerMinute || 1000} requests/minute`);
    console.log(`  Daily Limit: ${options.rateLimitPerDay || 50000} requests/day`);
    console.log('');
    console.log('🔑 API Key (save this securely):');
    console.log(`  ${apiKey}`);
    console.log('');
    console.log('💡 Usage:');
    console.log('  Add this header to your requests:');
    console.log(`  X-API-Key: ${apiKey}`);
    console.log('');
    console.log('⚠️  Important: This API key will not be shown again!');

    return {
      id: keyData.id,
      apiKey,
      createdAt: keyData.created_at
    };

  } catch (error) {
    console.error('❌ Failed to create API key:', error);
    throw error;
  }
}

async function main() {
  const args = process.argv.slice(2);

  // Parse command line arguments
  const options: CreateApiKeyOptions = {
    name: 'Development Key'
  };

  for (let i = 0; i < args.length; i += 2) {
    const flag = args[i];
    const value = args[i + 1];

    switch (flag) {
      case '--name':
        options.name = value || 'Development Key';
        break;
      case '--user-id':
        if (value) {
          options.userId = value;
        }
        break;
      case '--rate-limit-minute':
        options.rateLimitPerMinute = parseInt(value || '1000');
        break;
      case '--rate-limit-day':
        options.rateLimitPerDay = parseInt(value || '50000');
        break;
      case '--rate-limit-burst':
        options.rateLimitBurst = parseInt(value || '2000');
        break;
      case '--tier':
        options.subscriptionTier = value as any;
        break;
      case '--expires':
        if (value) {
          options.expiresAt = new Date(value);
        }
        break;
      case '--help':
      case '-h':
        console.log('Usage: npm run create-api-key [options]');
        console.log('Options:');
        console.log('  --name <n>              API key name (default: "Development Key")');
        console.log('  --user-id <uuid>           User ID (optional, for user-specific keys)');
        console.log('  --rate-limit-minute <num>  Rate limit per minute (default: 1000)');
        console.log('  --rate-limit-day <num>     Rate limit per day (default: 50000)');
        console.log('  --rate-limit-burst <num>   Burst rate limit (default: 2000)');
        console.log('  --tier <tier>              Subscription tier (FREE|BASIC|PREMIUM|ENTERPRISE)');
        console.log('  --expires <date>           Expiration date (ISO format)');
        console.log('  --help, -h                 Show this help message');
        process.exit(0);
        break;
      default:
        if (flag && flag.startsWith('--')) {
          console.log(`Unknown flag: ${flag}`);
          console.log('Use --help for usage information');
          process.exit(1);
        }
        i--; // Adjust for single argument
        break;
    }
  }

  try {
    console.log('🔗 Connecting to database...');
    createDatabasePool();

    console.log('🔑 Creating API key...');
    console.log(`📝 Name: ${options.name}`);
    console.log(`👤 User ID: ${options.userId || 'none (system key)'}`);
    console.log(`📊 Tier: ${options.subscriptionTier || 'FREE'}`);
    console.log(`⚡ Rate Limit: ${options.rateLimitPerMinute || 1000} requests/minute`);
    console.log(`📊 Daily Limit: ${options.rateLimitPerDay || 50000} requests/day`);
    console.log('');

    await createApiKeyInDatabase(options);

  } catch (error) {
    console.error('💥 Error:', error);
    process.exit(1);
  } finally {
    await closeDatabasePool();
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n� Received SIGINT, shutting down...');
  await closeDatabasePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n� Received SIGTERM, shutting down...');
  await closeDatabasePool();
  process.exit(0);
});

if (require.main === module) {
  main();
}
