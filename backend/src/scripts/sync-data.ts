#!/usr/bin/env node

/**
 * Data Synchronization Script
 * 
 * This script synchronizes postal terminal data from all providers:
 * - LP Express (CSV)
 * - Omniva (JSON)
 * - DPD (Excel)
 */

import { createDatabasePool, closeDatabasePool } from '../database/connection';
import { DataSynchronizationService } from '../services/data-sync';

interface SyncOptions {
  provider?: string;
  force?: boolean;
  dryRun?: boolean;
}

async function main(): Promise<void> {
  console.log('🚀 Starting postal terminal data synchronization...');
  
  try {
    // Parse command line arguments
    const options = parseArguments();
    
    // Initialize database connection
    createDatabasePool();
    console.log('🔗 Database connection established');
    
    // Initialize data sync service
    const dataSyncService = new DataSynchronizationService();
    
    if (options.dryRun) {
      console.log('🧪 Running in DRY RUN mode - no data will be modified');
    }
    
    // Perform synchronization
    if (options.provider) {
      console.log(`📡 Syncing data from provider: ${options.provider}`);
      const singleResult = await dataSyncService.syncSingleProvider(options.provider as any);

      // Display single provider result
      console.log('\n📊 Synchronization Results:');
      console.log('============================');
      console.log(`\n🏢 ${singleResult.provider}:`);
      console.log(`  ✅ Success: ${singleResult.success}`);
      console.log(`  📥 Terminals: ${singleResult.terminalCount}`);
      console.log(`  ⏱️  Duration: ${singleResult.duration}ms`);

      if (singleResult.errors.length > 0) {
        console.log(`  ❌ Errors: ${singleResult.errors.length}`);
        singleResult.errors.forEach((error: string, index: number) => {
          console.log(`    ${index + 1}. ${error}`);
        });
      }

      if (singleResult.success) {
        console.log('\n🎉 Data synchronization completed successfully!');
      } else {
        console.log('\n⚠️  Data synchronization completed with errors');
        process.exit(1);
      }
    } else {
      console.log('📡 Syncing data from all providers...');
      const stats = await dataSyncService.syncAllProviders();

      // Display all providers results
      console.log('\n📊 Synchronization Results:');
      console.log('============================');

      for (const providerResult of stats.lastResults) {
        console.log(`\n🏢 ${providerResult.provider}:`);
        console.log(`  ✅ Success: ${providerResult.success}`);
        console.log(`  📥 Terminals: ${providerResult.terminalCount}`);
        console.log(`  ⏱️  Duration: ${providerResult.duration}ms`);

        if (providerResult.errors.length > 0) {
          console.log(`  ❌ Errors: ${providerResult.errors.length}`);
          providerResult.errors.forEach((error: string, index: number) => {
            console.log(`    ${index + 1}. ${error}`);
          });
        }
      }

      const totalTerminals = stats.lastResults.reduce((sum, r) => sum + r.terminalCount, 0);
      const successfulProviders = stats.lastResults.filter(r => r.success).length;

      console.log(`\n🎯 Overall Results:`);
      console.log(`  ✅ Successful Providers: ${successfulProviders}/${stats.lastResults.length}`);
      console.log(`  📥 Total Terminals: ${totalTerminals}`);
      console.log(`  🕐 Total Syncs: ${stats.totalSyncs}`);
      console.log(`  ✅ Successful Syncs: ${stats.successfulSyncs}`);
      console.log(`  ❌ Failed Syncs: ${stats.failedSyncs}`);
      console.log(`  🕐 Last Sync: ${stats.lastSync.toISOString()}`);

      if (successfulProviders === stats.lastResults.length) {
        console.log('\n🎉 Data synchronization completed successfully!');
      } else {
        console.log('\n⚠️  Data synchronization completed with errors');
        process.exit(1);
      }
    }
    
  } catch (error) {
    console.error('💥 Data synchronization failed:', error);
    process.exit(1);
  } finally {
    await closeDatabasePool();
    console.log('🔌 Database connection closed');
  }
}

function parseArguments(): SyncOptions {
  const args = process.argv.slice(2);
  const options: SyncOptions = {};
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    const value = args[i + 1];
    
    switch (arg) {
      case '--provider':
      case '-p':
        if (value && !value.startsWith('-')) {
          options.provider = value;
          i++; // Skip next argument as it's the value
        }
        break;
      case '--force':
      case '-f':
        options.force = true;
        break;
      case '--dry-run':
      case '-d':
        options.dryRun = true;
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
      default:
        if (arg && arg.startsWith('-')) {
          console.error(`❌ Unknown option: ${arg}`);
          showHelp();
          process.exit(1);
        }
        break;
    }
  }
  
  return options;
}

function showHelp(): void {
  console.log(`
📡 Postal Terminal Data Synchronization Script

Usage: npm run sync-data [options]

Options:
  -p, --provider <name>    Sync data from specific provider only
                          (LP_EXPRESS, OMNIVA, DPD, VENIPAK)
  -f, --force             Force synchronization even if recently synced
  -d, --dry-run           Show what would be synced without making changes
  -h, --help              Show this help message

Examples:
  npm run sync-data                           # Sync all providers
  npm run sync-data --provider LP_EXPRESS     # Sync only LP Express
  npm run sync-data --force                   # Force sync all providers
  npm run sync-data --dry-run                 # Preview sync without changes

Providers:
  LP_EXPRESS    Lithuanian Post Express terminals (CSV)
  OMNIVA        Omniva parcel terminals (JSON)
  DPD           DPD pickup points (Excel)
  VENIPAK       Venipak parcel lockers and pickup points (JSON API)
`);
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}
