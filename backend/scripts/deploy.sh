#!/bin/bash

# Postal Terminal API Deployment Script
# This script automates the deployment process for production environments

set -e  # Exit on any error

# Configuration
APP_NAME="postal-terminal-api"
APP_DIR="/opt/${APP_NAME}"
SERVICE_USER="postal-api"
NODE_VERSION="18"
PM2_APP_NAME="postal-terminal-api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js ${NODE_VERSION} or later."
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt $NODE_VERSION ]]; then
        log_error "Node.js version ${NODE_VERSION} or later is required. Current version: $(node -v)"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    # Check PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 is not installed. Installing PM2..."
        npm install -g pm2
    fi
    
    # Check PostgreSQL client
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL client is not installed"
        log_error "Install with: sudo apt install postgresql-client (Ubuntu/Debian) or brew install postgresql (macOS)"
        exit 1
    fi
    
    # Check PostGIS availability
    log_info "Checking PostGIS availability..."
    if ! psql --version &> /dev/null; then
        log_error "PostgreSQL client tools not found"
        exit 1
    fi
    
    log_success "System requirements check passed"
}

# Create service user
create_service_user() {
    if ! id "$SERVICE_USER" &>/dev/null; then
        log_info "Creating service user: $SERVICE_USER"
        sudo useradd -r -s /bin/false -d "$APP_DIR" "$SERVICE_USER"
        log_success "Service user created"
    else
        log_info "Service user $SERVICE_USER already exists"
    fi
}

# Setup application directory
setup_app_directory() {
    log_info "Setting up application directory: $APP_DIR"
    
    sudo mkdir -p "$APP_DIR"
    sudo mkdir -p "$APP_DIR/releases"
    sudo mkdir -p "$APP_DIR/logs"
    sudo mkdir -p "$APP_DIR/data"
    sudo chown -R "$SERVICE_USER:$SERVICE_USER" "$APP_DIR"
    sudo chmod 755 "$APP_DIR"
    
    log_success "Application directory setup complete"
}

# Deploy application code
deploy_code() {
    log_info "Deploying application code..."
    
    # Create backup of current deployment
    if [[ -d "$APP_DIR/current" ]]; then
        log_info "Creating backup of current deployment..."
        sudo -u "$SERVICE_USER" cp -r "$APP_DIR/current" "$APP_DIR/backup-$(date +%Y%m%d-%H%M%S)" 2>/dev/null || true
    fi
    
    # Create new release directory
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local release_dir="$APP_DIR/releases/$timestamp"
    sudo -u "$SERVICE_USER" mkdir -p "$release_dir"
    
    # Copy application files (assuming we're in the project directory)
    log_info "Copying application files..."
    sudo -u "$SERVICE_USER" cp -r . "$release_dir/"
    
    # Remove development files
    sudo -u "$SERVICE_USER" rm -rf "$release_dir/node_modules" 2>/dev/null || true
    sudo -u "$SERVICE_USER" rm -rf "$release_dir/.git" 2>/dev/null || true
    sudo -u "$SERVICE_USER" rm -f "$release_dir/.env*" 2>/dev/null || true
    
    # Create symlink to current
    sudo -u "$SERVICE_USER" ln -sfn "$release_dir" "$APP_DIR/current"
    
    log_success "Code deployment complete"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    cd "$APP_DIR/current"
    sudo -u "$SERVICE_USER" npm ci --production --silent
    
    log_success "Dependencies installed"
}

# Build application
build_application() {
    log_info "Building application..."
    
    cd "$APP_DIR/current"
    sudo -u "$SERVICE_USER" npm run build
    
    log_success "Application built successfully"
}

# Setup environment configuration
setup_environment() {
    log_info "Setting up environment configuration..."
    
    local env_file="$APP_DIR/current/.env"
    
    if [[ ! -f "$env_file" ]]; then
        log_warning "Environment file not found. Creating production template..."
        sudo -u "$SERVICE_USER" cat > "$env_file" << 'EOF'
# ================================================================================
# POSTAL TERMINAL API - PRODUCTION ENVIRONMENT CONFIGURATION
# ================================================================================

# Node.js Environment
NODE_ENV=production
PORT=3000

# ================================================================================
# DATABASE CONFIGURATION
# ================================================================================
# PostgreSQL with PostGIS support
# Format: postgresql://username:password@host:port/database
DATABASE_URL=postgresql://postal_api:CHANGE_THIS_PASSWORD@localhost:5432/postal_terminal_api

# Database connection pool settings
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=2
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=300000
DB_QUERY_TIMEOUT=30000
DB_STATEMENT_TIMEOUT=60000

# ================================================================================
# API SECURITY & AUTHENTICATION
# ================================================================================
# Generate with: openssl rand -hex 32
API_KEY_SECRET=CHANGE_THIS_TO_A_SECURE_64_CHARACTER_HEX_STRING

# Rate limiting (requests per minute per IP)
DEFAULT_RATE_LIMIT=1000
BURST_RATE_LIMIT=2000

# CORS settings (comma-separated domains, or * for all)
ALLOWED_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# ================================================================================
# APPLICATION SETTINGS
# ================================================================================
APP_VERSION=1.0.0
LOG_LEVEL=info

# Cache settings
CACHE_SIZE=100000
CACHE_TTL=3600

# ================================================================================
# DATA SYNCHRONIZATION
# ================================================================================
# Automatic data updates
AUTO_UPDATE_ENABLED=true
UPDATE_FREQUENCY=daily
DATA_SYNC_SCHEDULE="0 2 * * *"

# Run initial data sync on startup (set to false after first run)
RUN_INITIAL_SYNC=false

# Provider-specific settings
LP_EXPRESS_ENABLED=true
OMNIVA_ENABLED=true
DPD_ENABLED=true

# ================================================================================
# NOTIFICATION SETTINGS
# ================================================================================
# Admin email for alerts and notifications
ADMIN_EMAIL=<EMAIL>

# Email notifications (SMTP)
ENABLE_EMAIL_NOTIFICATIONS=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-specific-password

# Webhook notifications (Slack, Discord, etc.)
ENABLE_WEBHOOK_NOTIFICATIONS=true
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# ================================================================================
# MONITORING & LOGGING
# ================================================================================
# Log retention (days)
LOG_RETENTION_DAYS=30

# Health check settings
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Performance monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# ================================================================================
# EXTERNAL SERVICES
# ================================================================================
# Geocoding service settings
GEOCODING_PROVIDER=nominatim
GEOCODING_RATE_LIMIT=1000
GEOCODING_TIMEOUT=10000

# Backup and storage
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 3 * * 0"
BACKUP_RETENTION_DAYS=30

# ================================================================================
# SECURITY HEADERS
# ================================================================================
# Security settings
ENABLE_HELMET=true
ENABLE_CORS=true
ENABLE_COMPRESSION=true

# SSL/TLS settings (for reverse proxy)
FORCE_HTTPS=true
HSTS_MAX_AGE=31536000

# ================================================================================
# DEVELOPMENT & DEBUG (should be false in production)
# ================================================================================
DEBUG_MODE=false
ENABLE_API_DOCS=false
ENABLE_DEBUG_ROUTES=false
EOF
        
        log_error "IMPORTANT: Environment file created at $env_file"
        log_error "You MUST edit this file with your actual configuration values before continuing!"
        log_error "Required changes:"
        log_error "  1. Set DATABASE_URL with your database credentials"
        log_error "  2. Set API_KEY_SECRET to a secure random string"
        log_error "  3. Configure ADMIN_EMAIL and notification settings"
        log_error "  4. Update ALLOWED_ORIGINS with your domains"
        log_error ""
        log_error "Generate API_KEY_SECRET with: openssl rand -hex 32"
        log_error ""
        read -p "Press Enter after you've updated the environment file..."
    fi
    
    log_success "Environment configuration setup complete"
}

# Validate environment configuration
validate_environment() {
    log_info "Validating environment configuration..."
    
    local env_file="$APP_DIR/current/.env"
    source "$env_file"
    
    # Check required variables
    local required_vars=(
        "NODE_ENV"
        "PORT"
        "DATABASE_URL"
        "API_KEY_SECRET"
        "ADMIN_EMAIL"
    )
    
    local missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        exit 1
    fi
    
    # Check for default values that need to be changed
    if [[ "$API_KEY_SECRET" == "CHANGE_THIS_TO_A_SECURE_64_CHARACTER_HEX_STRING" ]]; then
        log_error "API_KEY_SECRET must be changed from default value"
        log_error "Generate with: openssl rand -hex 32"
        exit 1
    fi
    
    if [[ "$DATABASE_URL" == *"CHANGE_THIS_PASSWORD"* ]]; then
        log_error "DATABASE_URL password must be changed from default value"
        exit 1
    fi
    
    if [[ "$ADMIN_EMAIL" == "<EMAIL>" ]]; then
        log_error "ADMIN_EMAIL must be changed from default value"
        exit 1
    fi
    
    log_success "Environment validation passed"
}

# Test database connection
test_database_connection() {
    log_info "Testing database connection..."
    
    cd "$APP_DIR/current"
    if sudo -u "$SERVICE_USER" npm run health-check &>/dev/null; then
        log_success "Database connection test passed"
    else
        log_error "Database connection test failed"
        log_error "Please check your DATABASE_URL and ensure PostgreSQL is running"
        exit 1
    fi
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    cd "$APP_DIR/current"
    sudo -u "$SERVICE_USER" npm run migrate
    
    log_success "Database migrations completed"
}

# Pre-process DPD geocoding if needed
preprocess_dpd_geocoding() {
    log_info "Checking DPD geocoding cache..."
    
    local cache_file="$APP_DIR/current/data/dpd-geocoding-cache.json"
    
    if [[ ! -f "$cache_file" ]]; then
        log_warning "DPD geocoding cache not found"
        read -p "Do you want to pre-process DPD geocoding now? This will take 8-10 minutes (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Starting DPD geocoding pre-processing..."
            cd "$APP_DIR/current"
            sudo -u "$SERVICE_USER" npm run preprocess-dpd
            log_success "DPD geocoding pre-processing completed"
        else
            log_warning "Skipping DPD geocoding pre-processing"
            log_warning "DPD terminals will use placeholder coordinates"
            log_warning "Run 'npm run preprocess-dpd' later to geocode DPD terminals"
        fi
    else
        log_success "DPD geocoding cache found"
    fi
}

# Setup PM2 configuration
setup_pm2() {
    log_info "Setting up PM2 configuration..."
    
    local pm2_config="$APP_DIR/current/ecosystem.config.js"
    
    # Update ecosystem config for production
    sudo -u "$SERVICE_USER" cat > "$pm2_config" << EOF
module.exports = {
  apps: [{
    name: '${PM2_APP_NAME}',
    script: './dist/index.js',
    cwd: '${APP_DIR}/current',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_file: '.env',
    log_file: '${APP_DIR}/logs/combined.log',
    out_file: '${APP_DIR}/logs/out.log',
    error_file: '${APP_DIR}/logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000,
    restart_delay: 1000,
    max_restarts: 10,
    min_uptime: '10s',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'data'],
    autorestart: true,
    cron_restart: '0 4 * * *'  // Restart daily at 4 AM
  }]
};
EOF
    
    log_success "PM2 configuration setup complete"
}

# Start application with PM2
start_application() {
    log_info "Starting application with PM2..."
    
    cd "$APP_DIR/current"
    
    # Stop existing application if running
    if sudo -u "$SERVICE_USER" pm2 list | grep -q "$PM2_APP_NAME"; then
        log_info "Stopping existing application..."
        sudo -u "$SERVICE_USER" pm2 stop "$PM2_APP_NAME"
        sudo -u "$SERVICE_USER" pm2 delete "$PM2_APP_NAME"
    fi
    
    # Start new application
    sudo -u "$SERVICE_USER" pm2 start ecosystem.config.js
    
    # Save PM2 configuration
    sudo -u "$SERVICE_USER" pm2 save
    
    # Setup PM2 startup script
    local startup_cmd=$(sudo -u "$SERVICE_USER" pm2 startup | tail -n 1)
    if [[ $startup_cmd == sudo* ]]; then
        eval "$startup_cmd"
    fi
    
    log_success "Application started successfully"
}

# Setup nginx reverse proxy
setup_nginx() {
    log_info "Setting up Nginx reverse proxy..."
    
    if ! command -v nginx &> /dev/null; then
        log_warning "Nginx is not installed. Please install Nginx manually."
        log_warning "Ubuntu/Debian: sudo apt install nginx"
        log_warning "CentOS/RHEL: sudo yum install nginx"
        return
    fi
    
    local nginx_config="/etc/nginx/sites-available/$APP_NAME"
    
    sudo cat > "$nginx_config" << 'EOF'
# Postal Terminal API Nginx Configuration

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api_burst:10m rate=50r/s;

# Upstream backend
upstream postal_terminal_api {
    server 127.0.0.1:3000;
    keepalive 32;
}

server {
    listen 80;
    server_name your-domain.com;  # Replace with your actual domain
    
    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Remove server header
    server_tokens off;
    
    # Main API endpoints
    location /api/ {
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;
        
        # Proxy settings
        proxy_pass http://postal_terminal_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # Health check endpoint (no rate limiting)
    location /api/v1/health {
        proxy_pass http://postal_terminal_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        access_log off;
    }
    
    # Metrics endpoint (restrict access)
    location /api/v1/metrics {
        # Allow only local access
        allow 127.0.0.1;
        allow ::1;
        deny all;
        
        proxy_pass http://postal_terminal_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        access_log off;
    }
    
    # Default location (API documentation or landing page)
    location / {
        proxy_pass http://postal_terminal_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(package\.json|\.env|ecosystem\.config\.js)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# SSL configuration (uncomment and configure for HTTPS)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     
#     ssl_certificate /etc/ssl/certs/your-domain.com.pem;
#     ssl_certificate_key /etc/ssl/private/your-domain.com.key;
#     
#     # SSL settings
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # Copy location blocks from HTTP server here
# }

# Redirect HTTP to HTTPS (uncomment for SSL)
# server {
#     listen 80;
#     server_name your-domain.com;
#     return 301 https://$server_name$request_uri;
# }
EOF
    
    # Enable site
    sudo ln -sf "$nginx_config" "/etc/nginx/sites-enabled/$APP_NAME"
    
    # Remove default site if it exists
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test nginx configuration
    if sudo nginx -t; then
        sudo systemctl reload nginx
        log_success "Nginx configuration setup complete"
        log_warning "Remember to update server_name in /etc/nginx/sites-available/$APP_NAME"
    else
        log_error "Nginx configuration test failed"
    fi
}

# Setup log rotation
setup_log_rotation() {
    log_info "Setting up log rotation..."
    
    sudo cat > "/etc/logrotate.d/$APP_NAME" << EOF
${APP_DIR}/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 ${SERVICE_USER} ${SERVICE_USER}
    postrotate
        sudo -u ${SERVICE_USER} pm2 reloadLogs
    endscript
}
EOF
    
    log_success "Log rotation setup complete"
}

# Setup systemd service for PM2
setup_systemd_service() {
    log_info "Setting up systemd service for PM2..."
    
    sudo cat > "/etc/systemd/system/$APP_NAME.service" << EOF
[Unit]
Description=Postal Terminal API
Documentation=https://github.com/your-org/postal-terminal-api
After=network.target postgresql.service

[Service]
Type=forking
User=${SERVICE_USER}
LimitNOFILE=65536
LimitNPROC=65536
PIDFile=${APP_DIR}/current/pm2.pid
Restart=on-failure
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

ExecStart=/usr/bin/pm2 resurrect
ExecReload=/usr/bin/pm2 reload all
ExecStop=/usr/bin/pm2 kill

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable "$APP_NAME"
    
    log_success "Systemd service setup complete"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s http://localhost:3000/api/v1/health > /dev/null; then
            log_success "Health check passed"
            return 0
        fi
        
        log_info "Health check attempt $attempt/$max_attempts failed, retrying in 2 seconds..."
        sleep 2
        ((attempt++))
    done
    
    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# Setup monitoring and alerting
setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Create monitoring script
    sudo cat > "$APP_DIR/monitor.sh" << 'EOF'
#!/bin/bash
# Simple monitoring script for Postal Terminal API

APP_NAME="postal-terminal-api"
HEALTH_URL="http://localhost:3000/api/v1/health"
WEBHOOK_URL="${WEBHOOK_URL:-}"
ADMIN_EMAIL="${ADMIN_EMAIL:-}"

check_health() {
    if ! curl -f -s "$HEALTH_URL" > /dev/null; then
        return 1
    fi
    return 0
}

send_alert() {
    local message="$1"
    echo "$(date): $message" >> /var/log/${APP_NAME}-monitor.log
    
    # Send webhook alert if configured
    if [[ -n "$WEBHOOK_URL" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 $APP_NAME Alert: $message\"}" \
            "$WEBHOOK_URL" 2>/dev/null || true
    fi
    
    # Send email alert if configured
    if [[ -n "$ADMIN_EMAIL" ]] && command -v mail &> /dev/null; then
        echo "$message" | mail -s "$APP_NAME Alert" "$ADMIN_EMAIL" 2>/dev/null || true
    fi
}

# Main check
if ! check_health; then
    send_alert "Health check failed - service may be down"
    # Try to restart the service
    pm2 restart "$APP_NAME" 2>/dev/null || true
    sleep 10
    
    # Check again after restart
    if ! check_health; then
        send_alert "Service restart failed - manual intervention required"
        exit 1
    else
        send_alert "Service successfully restarted after health check failure"
    fi
fi
EOF
    
    sudo chmod +x "$APP_DIR/monitor.sh"
    sudo chown "$SERVICE_USER:$SERVICE_USER" "$APP_DIR/monitor.sh"
    
    # Add to crontab for service user
    sudo -u "$SERVICE_USER" crontab -l 2>/dev/null | grep -v "$APP_DIR/monitor.sh" | sudo -u "$SERVICE_USER" crontab - 2>/dev/null || true
    echo "*/5 * * * * $APP_DIR/monitor.sh" | sudo -u "$SERVICE_USER" crontab -
    
    log_success "Monitoring setup complete"
}

# Cleanup old releases
cleanup_old_releases() {
    log_info "Cleaning up old releases..."
    
    cd "$APP_DIR/releases"
    # Keep only the 5 most recent releases
    sudo -u "$SERVICE_USER" ls -t | tail -n +6 | xargs -r rm -rf
    
    log_success "Old releases cleaned up"
}

# Display post-deployment information
show_deployment_summary() {
    log_success "Deployment completed successfully!"
    echo ""
    echo "🎉 Postal Terminal API is now running!"
    echo ""
    echo "📊 Service Information:"
    echo "  • Application: $APP_NAME"
    echo "  • Directory: $APP_DIR/current"
    echo "  • User: $SERVICE_USER"
    echo "  • URL: http://localhost:3000"
    echo ""
    echo "🔧 Management Commands:"
    echo "  • Check status: sudo -u $SERVICE_USER pm2 status"
    echo "  • View logs: sudo -u $SERVICE_USER pm2 logs $PM2_APP_NAME"
    echo "  • Restart: sudo -u $SERVICE_USER pm2 restart $PM2_APP_NAME"
    echo "  • Monitor: sudo -u $SERVICE_USER pm2 monit"
    echo ""
    echo "🗂️ Important Files:"
    echo "  • Environment: $APP_DIR/current/.env"
    echo "  • Logs: $APP_DIR/logs/"
    echo "  • Nginx config: /etc/nginx/sites-available/$APP_NAME"
    echo ""
    echo "📋 Next Steps:"
    echo "  1. Update Nginx server_name with your domain"
    echo "  2. Configure SSL certificate (recommended)"
    echo "  3. Set up domain DNS to point to this server"
    echo "  4. Run initial data sync: sudo -u $SERVICE_USER npm run sync-data"
    echo "  5. Create API keys: sudo -u $SERVICE_USER npm run create-api-key"
    echo ""
    echo "🔍 Health Check: curl http://localhost:3000/api/v1/health"
}

# Main deployment function
main() {
    log_info "Starting deployment of $APP_NAME..."
    
    check_root
    check_requirements
    create_service_user
    setup_app_directory
    deploy_code
    install_dependencies
    build_application
    setup_environment
    validate_environment
    run_migrations
    preprocess_dpd_geocoding
    setup_pm2
    start_application
    setup_nginx
    setup_log_rotation
    setup_systemd_service
    setup_monitoring
    
    if health_check; then
        cleanup_old_releases
        show_deployment_summary
    else
        log_error "Deployment failed health check"
        log_error "Check logs with: sudo -u $SERVICE_USER pm2 logs $PM2_APP_NAME"
        exit 1
    fi
}

# Run main function
main "$@"
