version: '3.8'

services:
  # Postal Terminal API - Dokploy optimized
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: postal-terminal-api
    ports:
      - "3000:3000"
    # Environment variables are managed by Dokploy's Environment tab
    # No need to specify them here as they will be injected automatically
    
    volumes:
      # Data persistence for downloads and cache
      - postal_data:/app/data
    
    restart: unless-stopped
    
    # Health check for Dokploy monitoring
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

volumes:
  postal_data:
    driver: local
