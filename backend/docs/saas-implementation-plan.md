# Postal Terminal API → Minimalistic SaaS Transformation Plan

**Version:** 1.0 • **Date:** 2025-07-09 • **Status:** Complete Analysis & Implementation Ready

---

## Table of Contents
1. [Phase 1 — Backend Analysis](#phase-1--backend-analysis)
2. [Phase 2 — SaaS Requirements](#phase-2--saas-requirements)
3. [Phase 3 — Technical Architecture](#phase-3--technical-architecture)
4. [Phase 4 — Implementation Roadmap](#phase-4--implementation-roadmap)
5. [Phase 5 — Development Phases](#phase-5--development-phases)
6. [Phase 6 — Testing & Deployment](#phase-6--testing--deployment)

> ✅ **Analysis Complete**: All findings based on comprehensive codebase examination conducted on 2025-07-09

---

## Phase 1 — Backend Analysis

### 1.1 Current Architecture & Technology Stack

**Framework & Core Technologies:**
- **Fastify 4.24.3** - High-performance web framework with plugin architecture
- **PostgreSQL + PostGIS** - Geospatial database with advanced indexing
- **TypeScript 5.2.2** - Full type safety across the codebase
- **Zod 3.25.76** - Runtime validation and schema definition
- **Node.js 22+** - Modern runtime with latest features

**Project Structure:**
```
backend/src/
├── routes/          # HTTP endpoints with Fastify plugin registration
├── middleware/      # Auth, rate limiting, logging, security, performance
├── services/        # Business logic (terminals, tracking, sync, cache, notifications)
├── database/        # Connection pooling, migrations, health checks
├── types/           # TypeScript interfaces and type definitions
├── validation/      # Zod schemas for request/response validation
├── utils/           # Crypto, validation, status mapping utilities
├── config/          # Environment validation and configuration management
└── scripts/         # CLI tools (migrations, API key creation, data sync)
```

**Design Patterns:**
- **Service Layer Pattern** - Clean separation of business logic
- **Dependency Injection** - Services are instantiated and injected where needed
- **Singleton Pattern** - Used for cache, notification, and language services
- **Factory Pattern** - Data collectors for different postal providers
- **Repository Pattern** - Database operations abstracted through services

### 1.2 Current API Endpoints

**Public Endpoints:**
| Method | Path | Auth | Description |
|--------|------|------|-------------|
| GET | `/` | None | Root endpoint with basic service info |
| GET | `/api/v1/health` | None | System health check with database status |

**Protected Endpoints (API Key Required):**
| Method | Path | Description | Response Format |
|--------|------|-------------|-----------------|
| GET | `/api/v1/info` | API documentation and endpoint listing | JSON with endpoint specs |
| GET | `/api/v1/metrics` | System performance metrics | JSON with timing stats |
| GET | `/api/v1/terminals` | Paginated terminal listing with filters | Paginated response |
| GET | `/api/v1/terminals/:id` | Individual terminal details | Single terminal object |
| GET | `/api/v1/terminals/search` | Full-text search across terminals | Paginated search results |
| GET | `/api/v1/terminals/nearby` | Geospatial radius search | Distance-sorted results |
| GET | `/api/v1/track` | Package tracking across 4 providers | Standardized tracking data |

**Request/Response Validation:**
- All endpoints use Zod schemas for validation
- Consistent error response format with request IDs
- TypeScript interfaces ensure type safety
- Comprehensive input sanitization and validation

### 1.3 Database Schema & Architecture

**Core Tables (from `000_complete_schema.sql`):**

**`terminals` Table:**
- Primary postal terminal catalog with PostGIS geography support
- Fields: id, name, city, address, postal_code, coordinates, provider, terminal_type
- Advanced indexing: GiST spatial index, GIN trigram indexes, covering indexes
- Full-text search vector with automatic updates via triggers
- Supports 4 providers: LP_EXPRESS, OMNIVA, DPD, VENIPAK

**`api_keys` Table:**
- Hashed API key storage with comprehensive rate limiting
- Fields: id, key_hash, tenant_id, name, subscription_tier, rate limits
- Usage tracking: total_requests, requests_this_month, last_used_at
- Security features: allowed_ips, allowed_domains, expiration dates

**`api_cache` Table:**
- PostgreSQL-based caching system with tenant isolation
- JSONB storage for flexible data types
- Automatic expiration and cleanup processes
- Multi-tenant support with tenant_id partitioning

**`tenants` Table:**
- Multi-tenant architecture foundation (currently using default tenant)
- Subscription management fields ready for SaaS expansion
- Billing and configuration storage capabilities

**Tracking Tables (from `001_add_tracking_tables.sql`):**
- `tracking_snapshots` - Historical tracking data storage
- `tracking_events` - Individual tracking event records
- Optimized for high-volume tracking operations

**Database Features:**
- PostGIS extensions for geospatial operations
- pg_trgm for fuzzy text search
- UUID generation functions
- Comprehensive constraints and validation
- Performance-optimized indexes for all query patterns

### 1.4 Current Authentication & Security

**API Key Authentication:**
- SHA-256 hashed keys stored in database
- Format validation: `ptapi_` prefix with specific length requirements
- Support for both `X-API-Key` and `Authorization: Bearer` headers
- Comprehensive rate limiting with burst support

**Rate Limiting Implementation:**
- Per-minute and per-day limits with burst allowance
- PostgreSQL-based rate limit tracking
- Configurable limits per API key
- Graceful degradation on rate limit failures

**Security Features:**
- Request ID generation for audit trails
- IP and domain restrictions per API key
- Security headers middleware
- Comprehensive security logging
- Input validation and sanitization

**Current Limitations:**
- No user accounts or role-based access control
- No OAuth or social authentication
- No session management
- Single authentication method (API keys only)

### 1.5 Business Logic & Service Architecture

**Data Collection & Synchronization:**
1. **Provider-Specific Collectors** - Specialized classes for each postal provider
   - `LPExpressDataCollector` - CSV data processing
   - `OmnivaDataCollector` - JSON API integration
   - `DPDDataCollector` - Excel file processing with geocoding
   - `VenipakDataCollector` - JSON API with data transformation

2. **DataSynchronizationService** - Orchestrates data collection
   - Scheduled updates via cron jobs
   - Smart sync with change detection
   - Error handling and notification
   - Bulk import operations with transaction safety

3. **TerminalService** - Core business logic
   - Dynamic SQL query building
   - Geospatial search operations
   - Full-text search with trigrams
   - Caching integration
   - Bulk import/update operations

4. **TrackingService** - Multi-provider package tracking
   - Provider abstraction layer
   - Standardized response format
   - Circuit breaker pattern for reliability
   - Adaptive caching based on delivery status

5. **PostgreSQLCacheService** - High-performance caching
   - Tenant-aware caching
   - Configurable TTL per cache type
   - Statistics tracking
   - Automatic cleanup processes

6. **NotificationService** - Alert and monitoring system
   - Email notifications via SMTP
   - Webhook integrations
   - Batching for efficiency
   - Configurable notification types

### 1.6 External Integrations & Dependencies

**Core Runtime Dependencies:**
- `fastify` (4.24.3) - Web framework
- `pg` (8.16.3) - PostgreSQL client
- `zod` (3.25.76) - Schema validation
- `axios` (1.10.0) - HTTP client
- `node-cron` (3.0.3) - Scheduled tasks

**Data Processing:**
- `csv-parse` (5.5.2) - CSV file processing
- `exceljs` (4.4.0) - Excel file handling
- `node-fetch` (3.3.2) - HTTP requests

**External API Integrations:**
- **LP Express** - CSV terminal data + tracking API
- **Omniva** - JSON terminal locations + tracking
- **DPD** - Excel terminal data + tracking (with limitations)
- **Venipak** - JSON API for terminals + tracking
- **OpenStreetMap Nominatim** - Geocoding service

**Development & Quality:**
- TypeScript with strict configuration
- ESLint with comprehensive rules
- Comprehensive error handling
- Performance monitoring and metrics

### 1.7 Code Quality & Standards

**Strengths:**
- Consistent TypeScript usage with strict type checking
- Comprehensive input validation with Zod schemas
- Well-structured service layer architecture
- Extensive error handling and logging
- Performance-optimized database queries
- Security-first approach with comprehensive middleware
- Comprehensive documentation and code comments

**Architecture Patterns:**
- Clean separation of concerns
- Dependency injection where appropriate
- Singleton pattern for shared services
- Factory pattern for provider implementations
- Repository pattern abstraction over database operations

**Performance Optimizations:**
- Connection pooling for database operations
- Multi-level caching strategy
- Optimized SQL queries with proper indexing
- Bulk operations for data synchronization
- Efficient memory usage patterns

---

## Phase 2 — SaaS Requirements

### 2.1 Target SaaS Architecture

**Core Concept:** Transform the existing postal terminal API into a minimalistic SaaS platform with two distinct user roles and comprehensive subscription management.

**User Roles:**
- **Admin** - Full system control and management capabilities
- **Customer** - Self-service subscription and API access management

### 2.2 Admin Features (Required)

**User Management:**
- Complete CRUD operations for user accounts
- User suspension and activation controls
- Role assignment and management
- User activity monitoring and audit logs

**Subscription Management:**
- Create, edit, and remove subscription plans
- Manage user subscriptions (activate, suspend, change plans)
- View subscription analytics and revenue metrics
- Handle subscription lifecycle events

**System Administration:**
- API key management with rate limiting controls
- System settings and configuration management
- Usage statistics and performance metrics dashboard
- Order management system (basic implementation)

**Payment & Billing:**
- Stripe integration for Euro currency payments
- Invoice management and payment tracking
- Future support planning for PayPal and cryptocurrency
- Subscription plan pricing and feature management

### 2.3 Customer Features (Required)

**Authentication & Account Management:**
- Email/password registration and login
- Google OAuth integration for seamless access
- Password reset and email verification flows
- Account profile management

**Subscription & Payment Management:**
- View current subscription status and details
- Change subscription plans with prorated billing
- Cancel subscriptions with proper handling
- Access to invoices and payment history
- Payment method management

**API Access Management:**
- Personal API key generation and management
- API key regeneration and revocation
- Rate limiting visibility and usage tracking
- API documentation access

**Usage Analytics:**
- Personal usage statistics and metrics
- Performance monitoring for API calls
- Historical usage data and trends
- Rate limit monitoring and alerts

### 2.4 Technical Specifications

**Backend Requirements:**
- Preserve all existing terminal and tracking functionality
- Maintain current API endpoint compatibility
- Implement JWT-based authentication alongside existing API key system
- Add comprehensive role-based access control (RBAC)
- Integrate Stripe for subscription billing in EUR
- Maintain high performance and scalability

**Frontend Requirements:**
- **Technology Stack:** Next.js 15+, DaisyUI 5, TailwindCSS 4
- **Internationalization:** English and Lithuanian support using Next.js 15+ i18n
- **Design:** Responsive, clean, professional, minimalistic UI
- **Consistency:** Uniform design system across all pages
- **Performance:** Fast loading times and smooth user experience

**Required Frontend Pages:**
- Landing page with clear value proposition
- API documentation (terminals and tracking endpoints only)
- Terminal search interface with interactive map (city/provider selection)
- Authentication pages (unified login/register, password reset, email verification)
- Customer dashboard with usage metrics and subscription management
- Admin dashboard with comprehensive system management
- Legal pages (privacy policy, terms of service)
- Contact page with support information

### 2.5 Implementation Constraints

**Preservation Requirements:**
- Maintain all existing backend functionality without modifications
- Preserve current API endpoint behavior and response formats
- Keep existing database schema compatibility
- Maintain current performance characteristics

**Development Standards:**
- Follow existing coding standards from documentation
- Use actual API calls throughout - no mock data
- Maintain uniform API request/response formats
- Implement clean, immediate solutions without legacy considerations
- Stay within defined project scope to avoid unnecessary complexity

**Security & Compliance:**
- Minimal user data collection (no company names, phone numbers)
- Secure API key management with proper rate limiting
- GDPR-compliant data handling for EU users
- Secure payment processing through Stripe

---

## Phase 3 — Technical Architecture

### 3.1 Backend Architecture Evolution

**Preservation Strategy:**
- Keep all existing Fastify routes and middleware unchanged
- Maintain current PostgreSQL/PostGIS database schema
- Preserve existing terminal and tracking API endpoints
- Continue using current authentication for API access

**New Additions:**
- **User Management System** - Email/password + Google OAuth authentication
- **JWT-based Session Management** - For web dashboard access
- **Role-Based Access Control** - Admin and Customer role separation
- **Stripe Payment Integration** - Subscription billing in EUR with webhook handling
- **Enhanced Multi-tenancy** - Leverage existing tenant_id architecture
- **Unified Response Format** - Consistent JSON API responses for new endpoints

**Dual Authentication Strategy:**
- **API Key Authentication** - Continue for programmatic API access
- **JWT Authentication** - New system for web dashboard access
- **Hybrid Middleware** - Support both authentication methods where appropriate

### 3.2 Frontend Architecture

**Technology Stack:**
- **Next.js 15+** with App Router for modern React development
- **TypeScript** for type safety and better developer experience
- **TailwindCSS 4** for utility-first styling
- **DaisyUI 5** for consistent component library
- **React Query** for server state management
- **Next-intl** for internationalization (EN/LT)

**Architecture Patterns:**
- **Server Components** - Leverage Next.js 15 server components for performance
- **Client Components** - Interactive elements with proper hydration
- **API Routes** - Next.js API routes for frontend-specific endpoints
- **Middleware** - Authentication and authorization checks
- **Layout System** - Consistent layouts across all pages

**State Management:**
- **Server State** - React Query for API data management
- **Client State** - React Context for UI state
- **Form State** - React Hook Form for form management
- **Authentication State** - Custom auth context with JWT handling

### 3.3 Database Schema Extensions

**New Tables Required:**

```sql
-- User accounts and authentication
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT,
  google_id TEXT UNIQUE,
  role TEXT NOT NULL CHECK (role IN ('ADMIN', 'CUSTOMER')),
  is_active BOOLEAN DEFAULT TRUE,
  email_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Subscription plans
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  price_cents INTEGER NOT NULL,
  currency CHAR(3) DEFAULT 'EUR',
  interval TEXT NOT NULL CHECK (interval IN ('month', 'year')),
  rate_limit_per_minute INTEGER DEFAULT 1000,
  rate_limit_per_day INTEGER DEFAULT 50000,
  rate_limit_burst INTEGER DEFAULT 2000,
  features JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User subscriptions
CREATE TABLE user_subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  plan_id UUID REFERENCES subscription_plans(id),
  stripe_subscription_id TEXT UNIQUE,
  stripe_customer_id TEXT,
  status TEXT NOT NULL CHECK (status IN ('active', 'past_due', 'canceled', 'unpaid', 'trialing')),
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Order management and transaction history
CREATE TABLE orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES user_subscriptions(id),
  type TEXT NOT NULL CHECK (type IN ('subscription_create', 'subscription_change', 'subscription_cancel', 'refund', 'chargeback')),
  amount_cents INTEGER NOT NULL,
  currency CHAR(3) DEFAULT 'EUR',
  stripe_payment_intent_id TEXT,
  stripe_invoice_id TEXT,
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded', 'disputed')),
  failure_reason TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  refunded_at TIMESTAMPTZ
);

-- Subscription audit trail for admin oversight
CREATE TABLE subscription_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE CASCADE,
  changed_by_user_id UUID REFERENCES users(id),
  change_type TEXT NOT NULL CHECK (change_type IN ('created', 'status_changed', 'plan_changed', 'canceled', 'admin_override', 'payment_failed', 'payment_recovered')),
  old_values JSONB DEFAULT '{}'::jsonb,
  new_values JSONB DEFAULT '{}'::jsonb,
  reason TEXT,
  admin_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Payment issues and dispute tracking
CREATE TABLE payment_issues (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES user_subscriptions(id),
  order_id UUID REFERENCES orders(id),
  issue_type TEXT NOT NULL CHECK (issue_type IN ('payment_failed', 'dispute', 'chargeback', 'refund_request')),
  stripe_dispute_id TEXT,
  amount_cents INTEGER,
  currency CHAR(3) DEFAULT 'EUR',
  status TEXT NOT NULL CHECK (status IN ('open', 'under_review', 'resolved', 'lost', 'warning_closed')),
  resolution_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  resolved_at TIMESTAMPTZ
);

-- Link existing api_keys to users
ALTER TABLE api_keys ADD COLUMN user_id UUID REFERENCES users(id);
ALTER TABLE api_keys ADD COLUMN created_by_user_id UUID REFERENCES users(id);

-- System settings for admin configuration
CREATE TABLE system_settings (
  key TEXT PRIMARY KEY,
  value JSONB NOT NULL,
  description TEXT,
  updated_by UUID REFERENCES users(id),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Index Optimizations:**
```sql
-- User authentication indexes
CREATE INDEX idx_users_email ON users(email) WHERE is_active = TRUE;
CREATE INDEX idx_users_google_id ON users(google_id) WHERE google_id IS NOT NULL;
CREATE INDEX idx_users_role_active ON users(role, is_active);

-- Subscription indexes
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_stripe ON user_subscriptions(stripe_subscription_id);
CREATE INDEX idx_user_subscriptions_expiring ON user_subscriptions(current_period_end) WHERE status = 'active';

-- Order management indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_subscription_id ON orders(subscription_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_type_status ON orders(type, status);
CREATE INDEX idx_orders_created_at ON orders(created_at);

-- Audit log indexes
CREATE INDEX idx_subscription_audit_subscription_id ON subscription_audit_log(subscription_id);
CREATE INDEX idx_subscription_audit_changed_by ON subscription_audit_log(changed_by_user_id);
CREATE INDEX idx_subscription_audit_created_at ON subscription_audit_log(created_at);

-- Payment issues indexes
CREATE INDEX idx_payment_issues_user_id ON payment_issues(user_id);
CREATE INDEX idx_payment_issues_status ON payment_issues(status);
CREATE INDEX idx_payment_issues_type ON payment_issues(issue_type);
CREATE INDEX idx_payment_issues_created_at ON payment_issues(created_at);

-- API key user relationship
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id) WHERE user_id IS NOT NULL;
```

---

## Phase 4 — Implementation Roadmap

### 4.1 Backend Development Tasks

**Phase 1: Core Authentication System (Priority: Critical)**

| Task | Description | Estimated Effort | Dependencies |
|------|-------------|------------------|--------------|
| User Management Schema | Create users table with proper constraints and indexes | 4 hours | Database migration system |
| Password Authentication | Implement bcrypt hashing and validation | 6 hours | User schema |
| JWT Token System | Access and refresh token generation/validation | 8 hours | User authentication |
| Google OAuth Integration | OAuth 2.0 flow with Google provider | 12 hours | JWT system |
| Auth Middleware | JWT validation middleware for protected routes | 6 hours | JWT system |
| Auth Endpoints | Register, login, logout, refresh, Google OAuth routes | 10 hours | All auth components |

**Phase 2: Subscription Management (Priority: High)**

| Task | Description | Estimated Effort | Dependencies |
|------|-------------|------------------|--------------|
| Subscription Schema | Plans, user_subscriptions, orders, audit_log tables | 6 hours | User schema |
| Plan Management | CRUD operations for subscription plans | 8 hours | Subscription schema |
| Stripe Integration | Customer creation, subscription management | 16 hours | Plan management |
| Webhook Handler | Stripe webhook processing for payment events | 12 hours | Stripe integration |
| Subscription Endpoints | Customer subscription management API | 10 hours | Stripe integration |
| Admin Subscription Management | Advanced admin controls and bulk operations | 12 hours | Subscription endpoints |
| Order Management System | Transaction history and payment tracking | 10 hours | Stripe integration |
| Payment Issue Handling | Failed payments, disputes, refunds management | 8 hours | Order management |

**Phase 3: User Interface APIs (Priority: High)**

| Task | Description | Estimated Effort | Dependencies |
|------|-------------|------------------|--------------|
| User Profile Management | User CRUD operations and profile updates | 8 hours | Auth system |
| API Key Self-Service | User-managed API key creation/revocation | 10 hours | User management |
| Usage Analytics | User-specific usage tracking and reporting | 12 hours | API key system |
| Admin Dashboard APIs | User management, system stats, plan management | 16 hours | All previous phases |

**Phase 4: System Administration (Priority: Medium)**

| Task | Description | Estimated Effort | Dependencies |
|------|-------------|------------------|--------------|
| System Settings | Key-value configuration management | 6 hours | Admin APIs |
| Advanced User Management | Bulk operations, user suspension, role changes | 8 hours | User management |
| Enhanced Analytics | System-wide metrics and reporting | 10 hours | Usage analytics |
| Audit Trail System | Subscription change tracking and admin oversight | 6 hours | Admin subscription management |
| Payment Recovery Workflows | Automated dunning and retry logic | 8 hours | Payment issue handling |

### 4.2 New API Endpoints Specification

**Authentication Endpoints (Public Access):**

| Method | Path | Request Body | Response | Description |
|--------|------|--------------|----------|-------------|
| POST | `/auth/register` | `{email, password, confirmPassword}` | `{user, accessToken, refreshToken}` | User registration |
| POST | `/auth/login` | `{email, password}` | `{user, accessToken, refreshToken}` | User login |
| POST | `/auth/logout` | `{refreshToken}` | `{success: true}` | User logout |
| POST | `/auth/refresh` | `{refreshToken}` | `{accessToken, refreshToken}` | Token refresh |
| GET | `/auth/google` | - | OAuth redirect | Google OAuth initiation |
| GET | `/auth/google/callback` | OAuth params | `{user, accessToken, refreshToken}` | Google OAuth callback |
| POST | `/auth/forgot-password` | `{email}` | `{success: true}` | Password reset request |
| POST | `/auth/reset-password` | `{token, password}` | `{success: true}` | Password reset confirmation |
| POST | `/auth/verify-email` | `{token}` | `{success: true}` | Email verification |

**User Management Endpoints (JWT Required):**

| Method | Path | Role | Request Body | Response | Description |
|--------|------|------|--------------|----------|-------------|
| GET | `/users/me` | Customer/Admin | - | `{user}` | Get current user profile |
| PATCH | `/users/me` | Customer/Admin | `{email?, password?}` | `{user}` | Update user profile |
| DELETE | `/users/me` | Customer | - | `{success: true}` | Delete user account |
| GET | `/users` | Admin | Query params | `{users, pagination}` | List all users |
| GET | `/users/:id` | Admin | - | `{user}` | Get specific user |
| PATCH | `/users/:id` | Admin | `{role?, isActive?}` | `{user}` | Update user |
| DELETE | `/users/:id` | Admin | - | `{success: true}` | Delete user |

**Subscription Management Endpoints:**

| Method | Path | Role | Request Body | Response | Description |
|--------|------|------|--------------|----------|-------------|
| GET | `/subscription-plans` | Public | - | `{plans}` | List available plans |
| POST | `/subscription-plans` | Admin | `{name, price, features}` | `{plan}` | Create subscription plan |
| PATCH | `/subscription-plans/:id` | Admin | `{name?, price?, features?}` | `{plan}` | Update plan |
| DELETE | `/subscription-plans/:id` | Admin | - | `{success: true}` | Delete plan |
| GET | `/subscriptions/me` | Customer | - | `{subscription}` | Get user subscription |
| POST | `/subscriptions/checkout` | Customer | `{planId}` | `{checkoutUrl}` | Create Stripe checkout |
| POST | `/subscriptions/change` | Customer | `{planId}` | `{checkoutUrl}` | Change subscription plan |
| POST | `/subscriptions/cancel` | Customer | - | `{success: true}` | Cancel subscription |

**Admin Subscription Management Endpoints:**

| Method | Path | Role | Request Body | Response | Description |
|--------|------|------|--------------|----------|-------------|
| GET | `/admin/subscriptions` | Admin | Query params | `{subscriptions, pagination}` | List all subscriptions with advanced filtering |
| GET | `/admin/subscriptions/:id` | Admin | - | `{subscription, user, plan, orders}` | Get detailed subscription info |
| PATCH | `/admin/subscriptions/:id/status` | Admin | `{status, reason, notes?}` | `{subscription}` | Manually change subscription status |
| POST | `/admin/subscriptions/:id/extend` | Admin | `{days, reason, notes?}` | `{subscription}` | Extend subscription period |
| POST | `/admin/subscriptions/bulk-action` | Admin | `{action, subscriptionIds, reason}` | `{results}` | Bulk operations on subscriptions |
| GET | `/admin/subscriptions/:id/audit-log` | Admin | Query params | `{auditLog, pagination}` | Get subscription change history |
| GET | `/admin/subscriptions/expiring` | Admin | Query params | `{subscriptions, pagination}` | Get subscriptions expiring soon |

**API Key Management Endpoints:**

| Method | Path | Role | Request Body | Response | Description |
|--------|------|------|--------------|----------|-------------|
| GET | `/api-keys` | Customer/Admin | - | `{apiKeys}` | List user's API keys |
| POST | `/api-keys` | Customer/Admin | `{name}` | `{apiKey, key}` | Create new API key |
| PATCH | `/api-keys/:id` | Customer/Admin | `{name?, isActive?}` | `{apiKey}` | Update API key |
| DELETE | `/api-keys/:id` | Customer/Admin | - | `{success: true}` | Delete API key |
| POST | `/api-keys/:id/regenerate` | Customer/Admin | - | `{apiKey, key}` | Regenerate API key |

**Order Management & Billing Endpoints:**

| Method | Path | Role | Request Body | Response | Description |
|--------|------|------|--------------|----------|-------------|
| GET | `/orders/me` | Customer | Query params | `{orders, pagination}` | Get user's order history |
| GET | `/orders/:id` | Customer/Admin | - | `{order, details}` | Get specific order details |
| GET | `/admin/orders` | Admin | Query params | `{orders, pagination}` | List all orders with filtering |
| POST | `/admin/orders/:id/refund` | Admin | `{amount?, reason, notes?}` | `{refund}` | Process refund for order |
| GET | `/admin/billing/failed-payments` | Admin | Query params | `{failedPayments, pagination}` | List failed payments |
| POST | `/admin/billing/retry-payment` | Admin | `{orderId, notes?}` | `{result}` | Retry failed payment |

**Payment Issues & Dispute Management:**

| Method | Path | Role | Request Body | Response | Description |
|--------|------|------|--------------|----------|-------------|
| GET | `/admin/payment-issues` | Admin | Query params | `{issues, pagination}` | List payment issues and disputes |
| GET | `/admin/payment-issues/:id` | Admin | - | `{issue, details, history}` | Get detailed payment issue info |
| PATCH | `/admin/payment-issues/:id` | Admin | `{status, notes}` | `{issue}` | Update payment issue status |
| POST | `/admin/payment-issues/:id/resolve` | Admin | `{resolution, notes}` | `{issue}` | Mark payment issue as resolved |

**Analytics & Usage Endpoints:**

| Method | Path | Role | Request Body | Response | Description |
|--------|------|------|--------------|----------|-------------|
| GET | `/analytics/usage` | Customer | Query params | `{usage, stats}` | User usage analytics |
| GET | `/analytics/system` | Admin | Query params | `{systemStats}` | System-wide analytics |
| GET | `/analytics/users` | Admin | Query params | `{userStats}` | User analytics |
| GET | `/analytics/revenue` | Admin | Query params | `{revenueStats}` | Revenue analytics |
| GET | `/analytics/subscriptions` | Admin | Query params | `{subscriptionStats}` | Subscription analytics and trends |
| GET | `/analytics/churn` | Admin | Query params | `{churnStats}` | Customer churn analysis |

**System Administration Endpoints:**

| Method | Path | Role | Request Body | Response | Description |
|--------|------|------|--------------|----------|-------------|
| GET | `/admin/settings` | Admin | - | `{settings}` | Get system settings |
| PATCH | `/admin/settings` | Admin | `{key: value}` | `{settings}` | Update system settings |
| GET | `/admin/health` | Admin | - | `{health, metrics}` | Detailed system health |

**Webhook Endpoints (System Access):**

| Method | Path | Access | Request Body | Response | Description |
|--------|------|--------|--------------|----------|-------------|
| POST | `/webhooks/stripe` | Stripe | Stripe event | `200 OK` | Stripe webhook handler for all events |
| POST | `/webhooks/stripe/disputes` | Stripe | Dispute event | `200 OK` | Dedicated dispute webhook handler |

**Admin Subscription Filtering (GET /admin/subscriptions):**
```typescript
// Query parameters for advanced subscription filtering
{
  status?: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing',
  plan_id?: string,
  user_id?: string,
  expiring_in_days?: number,
  created_after?: string,
  created_before?: string,
  updated_after?: string,
  updated_before?: string,
  stripe_customer_id?: string,
  cancel_at_period_end?: boolean,
  page?: number,
  limit?: number,
  sort_by?: 'created_at' | 'updated_at' | 'current_period_end' | 'user_email',
  sort_order?: 'asc' | 'desc'
}

// Response includes comprehensive subscription data
{
  subscriptions: [{
    id: string,
    user: { id, email, role, created_at },
    plan: { id, name, price_cents, interval, features },
    status: string,
    stripe_subscription_id: string,
    stripe_customer_id: string,
    current_period_start: string,
    current_period_end: string,
    cancel_at_period_end: boolean,
    created_at: string,
    updated_at: string,
    last_payment_status?: string,
    next_payment_attempt?: string
  }],
  pagination: { page, limit, total, totalPages, hasNext, hasPrev },
  summary: {
    total_active: number,
    total_past_due: number,
    total_canceled: number,
    expiring_soon: number,
    revenue_this_month: number
  }
}
```

**Preserved Existing Endpoints:**
- All current `/api/v1/*` endpoints remain unchanged
- Continue to require API key authentication
- Maintain existing request/response formats
- No breaking changes to current functionality

### 4.3 Authentication & Authorization Implementation

**Dual Authentication Strategy:**

**JWT Authentication (Web Dashboard):**
- **Access Tokens**: 15-minute expiration, HS256 signing
- **Refresh Tokens**: 7-day expiration, stored in database with revocation support
- **Password Hashing**: bcrypt with 12 rounds for security
- **Token Claims**: `{userId, email, role, iat, exp}`

**Google OAuth Integration:**
- **Provider**: `@fastify/oauth2` plugin for Google OAuth 2.0
- **Scope**: `profile email` for basic user information
- **Flow**: Authorization code flow with PKCE for security
- **Account Linking**: Link Google accounts to existing email accounts

**API Key Authentication (Programmatic Access):**
- **Preserve Current System**: Maintain existing API key authentication
- **Enhanced Integration**: Link API keys to user accounts
- **Rate Limiting**: Apply subscription-based rate limits
- **Scope**: Continue to protect `/api/v1/*` endpoints

**Middleware Architecture:**
```typescript
// Middleware chain for different endpoint types
1. requestLogger          // All requests
2. jwtAuth (optional)     // Web dashboard routes
3. authenticateApiKey     // API routes (/api/v1/*)
4. requireRole(role)      // Role-based protection
```

**Role-Based Access Control:**
- **Admin Role**: Full system access, user management, system settings
- **Customer Role**: Self-service access, own data only
- **Route Protection**: Middleware decorators for role enforcement
- **Permission Checks**: Granular permissions within roles

### 4.4 Enhanced Subscription Management Architecture

**Comprehensive Subscription System:**
```typescript
// Complete subscription lifecycle management
1. Create Stripe Customer on user registration
2. Sync subscription plans with Stripe Products/Prices
3. Handle checkout sessions for plan changes
4. Process webhooks for all subscription and payment events
5. Update local database with subscription status and create audit logs
6. Track orders and transaction history
7. Handle payment failures with automated recovery
8. Manage disputes and billing issues
9. Provide admin oversight and manual controls
```

**Admin Subscription Controls:**
- **Advanced Filtering**: Filter subscriptions by status (active, past_due, canceled, unpaid, trialing), expiration dates, plans, users
- **Bulk Operations**: Suspend, reactivate, or change plans for multiple subscriptions
- **Manual Overrides**: Admin can manually change subscription status with reason tracking
- **Period Extensions**: Extend subscription periods for customer service purposes
- **Audit Trails**: Complete history of all subscription changes with user attribution

**Order Management System:**
- **Transaction History**: Complete record of all subscription-related transactions
- **Payment Tracking**: Track payment attempts, successes, failures, and retries
- **Refund Management**: Process full or partial refunds with admin controls
- **Invoice Integration**: Link orders to Stripe invoices for comprehensive billing records

**Payment Issue Resolution:**
- **Failed Payment Tracking**: Monitor and manage failed payment attempts
- **Dispute Management**: Handle chargebacks and billing disputes
- **Recovery Workflows**: Automated retry logic with configurable intervals
- **Admin Intervention**: Manual payment retry and issue resolution tools

### 4.5 Stripe Payment Integration

**Key Integration Points:**

**Checkout Flow:**
- **Endpoint**: `POST /subscriptions/checkout`
- **Process**: Create Stripe Checkout Session with customer and price
- **Success**: Redirect to dashboard with success message
- **Cancel**: Return to plan selection with cancellation notice

**Webhook Processing:**
- **Subscription Events**: `customer.subscription.created`, `customer.subscription.updated`, `customer.subscription.deleted`, `customer.subscription.trial_will_end`
- **Payment Events**: `invoice.payment_succeeded`, `invoice.payment_failed`, `payment_intent.payment_failed`
- **Dispute Events**: `charge.dispute.created`, `charge.dispute.updated`, `charge.dispute.closed`
- **Security**: Verify webhook signatures using Stripe webhook secret
- **Idempotency**: Handle duplicate webhook events gracefully using Stripe event IDs
- **Database Updates**: Sync subscription status, create audit logs, update API key rate limits
- **Automated Actions**: Trigger payment retry workflows, send notification emails, suspend services

**Currency & Pricing:**
- **Primary Currency**: EUR (Euro) for all transactions
- **Price Storage**: Store prices in cents to avoid floating-point issues
- **Tax Handling**: Use Stripe Tax for automatic tax calculation
- **Invoicing**: Leverage Stripe's built-in invoice system

**Payment Recovery & Dunning:**
- **Smart Retry Logic**: Automatic retry of failed payments with exponential backoff
- **Dunning Management**: Progressive email campaigns for failed payments
- **Grace Periods**: Configurable grace periods before service suspension
- **Payment Method Updates**: Automated requests for updated payment methods

**Future Payment Methods:**
- **PayPal**: Plan for PayPal integration using Stripe's PayPal support
- **Cryptocurrency**: Research Stripe's crypto payment capabilities
- **Bank Transfers**: Consider SEPA Direct Debit for European customers
- **Buy Now, Pay Later**: Integration with Klarna or similar services

### 4.5 Frontend Development Roadmap

**Development Phases:**

**Phase 1: Foundation & Authentication (Week 1-2)**
- Project setup with Next.js 15, TypeScript, TailwindCSS, DaisyUI
- Authentication pages (login, register, password reset)
- Layout components (navbar, footer, sidebar)
- Internationalization setup (EN/LT)
- Basic routing and navigation

**Phase 2: Customer Dashboard (Week 3-4)**
- User profile management
- Subscription status and plan management
- API key management interface
- Usage analytics and charts
- Payment method management

**Phase 3: Terminal Search & API Documentation (Week 5-6)**
- Interactive terminal search with map integration
- Advanced filtering (city, provider, terminal type)
- API documentation pages
- Terminal details modal/page
- Search result optimization

**Phase 4: Admin Dashboard (Week 7-8)**
- User management interface
- Subscription plan management
- System analytics and metrics
- User activity monitoring
- System settings management

**Phase 5: Landing & Legal Pages (Week 9)**
- Marketing landing page
- Pricing page with plan comparison
- Legal pages (privacy policy, terms of service)
- Contact page with support information
- SEO optimization

**Phase 6: Testing & Polish (Week 11-12)**
- Comprehensive testing (unit, integration, E2E)
- Payment flow testing with Stripe test mode
- Admin workflow testing and validation
- Performance optimization and load testing
- Accessibility improvements
- Mobile responsiveness testing
- User experience refinements
- Security audit and penetration testing

**Component Architecture:**
```typescript
// Shared components
- Layout (AppLayout, AuthLayout, DashboardLayout)
- Navigation (Navbar, Sidebar, Breadcrumbs)
- Forms (LoginForm, RegisterForm, ProfileForm)
- Data Display (DataTable, Chart, StatsCard)
- UI Elements (Button, Modal, Alert, Loading)

// Page-specific components
- Dashboard (UsageChart, ApiKeyList, SubscriptionCard)
- Terminal Search (MapView, FilterPanel, ResultsList)
- Admin (UserTable, PlanEditor, SystemMetrics)
```

---

## Phase 5 — Development Phases

---

### 🚀 MVP Acceptance Criteria
1. Customer can signup, pay, receive API key and successfully call `/api/v1/terminals` within assigned rate limits.
2. Admin can create a new plan and suspend a user; suspended user’s API key immediately blocked.
3. Billing events (payment succeeded / subscription canceled) reflected in the database within 60 s via webhook.

---

### 5.1 Sprint Planning & Milestones

**Sprint 1: Core Authentication (2 weeks)**
- **Backend**: User schema, JWT system, auth endpoints
- **Frontend**: Authentication pages, layout foundation
- **Deliverable**: Users can register, login, and access dashboard
- **Success Criteria**: Complete authentication flow with JWT tokens

**Sprint 2: Subscription Management (3 weeks)**
- **Backend**: Subscription schema, Stripe integration, webhook handling, order management
- **Frontend**: Subscription management UI, plan selection, billing history
- **Deliverable**: Users can subscribe to plans and manage subscriptions with full order tracking
- **Success Criteria**: End-to-end subscription flow with Stripe and comprehensive admin controls

**Sprint 3: API Key Self-Service (1 week)**
- **Backend**: API key management endpoints, user linking
- **Frontend**: API key management interface
- **Deliverable**: Users can create and manage their API keys
- **Success Criteria**: API keys work with existing terminal endpoints

**Sprint 4: Usage Analytics (1 week)**
- **Backend**: Usage tracking and analytics endpoints
- **Frontend**: Usage dashboard and charts
- **Deliverable**: Users can view their API usage statistics
- **Success Criteria**: Real-time usage tracking and display

**Sprint 5: Admin Dashboard (3 weeks)**
- **Backend**: Admin endpoints for user and system management, payment issue handling
- **Frontend**: Admin interface for user, subscription, and billing management
- **Deliverable**: Admins can manage users, subscriptions, orders, and payment issues
- **Success Criteria**: Complete admin functionality with billing oversight

**Sprint 6: Terminal Search Interface (1 week)**
- **Frontend**: Terminal search with map integration
- **Deliverable**: Public terminal search interface
- **Success Criteria**: Interactive map with terminal search

**Sprint 7: Payment Recovery & Automation (1 week)**
- **Backend**: Automated dunning workflows, payment retry logic
- **Frontend**: Payment issue resolution interface for admins
- **Deliverable**: Automated payment recovery and admin tools
- **Success Criteria**: Reduced manual intervention for payment issues

**Sprint 8: Documentation & Polish (1 week)**
- **Frontend**: API documentation, landing page, legal pages
- **Deliverable**: Complete public-facing website
- **Success Criteria**: Professional marketing site with documentation

### 5.2 Technical Milestones

**Milestone 1: Authentication System**
- User registration and login
- JWT token management
- Google OAuth integration
- Password reset functionality
- Email verification

**Milestone 2: Subscription System**
- Stripe integration with comprehensive event handling
- Subscription plan management with admin controls
- Webhook processing for all payment and subscription events
- Payment flow completion with retry logic
- Subscription lifecycle management with audit trails
- Order management and transaction history
- Payment issue tracking and dispute management

**Milestone 3: API Integration**
- API key self-service
- Usage tracking
- Rate limit enforcement
- Analytics dashboard
- Existing API preservation

**Milestone 4: Admin Features**
- User management with bulk operations
- Advanced subscription administration
- Comprehensive analytics and reporting
- Payment issue management and resolution
- Configuration management and system settings
- Audit trails and change tracking
- Monitoring and automated alerts

**Milestone 5: User Experience**
- Responsive design
- Internationalization
- Performance optimization
- Accessibility compliance
- SEO optimization

---

## Phase 6 — Testing & Deployment

### 6.1 Testing Strategy

**Backend Testing:**
- **Unit Tests**: Service layer, utilities, validation schemas
- **Integration Tests**: API endpoints, database operations, Stripe integration
- **E2E Tests**: Complete user flows, payment processing, webhook handling
- **Performance Tests**: Load testing with Artillery, database performance
- **Security Tests**: Authentication, authorization, input validation

**Frontend Testing:**
- **Unit Tests**: Components, utilities, hooks
- **Integration Tests**: Page interactions, API integration
- **E2E Tests**: User journeys, payment flows, admin operations
- **Visual Tests**: Component screenshots, responsive design
- **Accessibility Tests**: Screen reader compatibility, keyboard navigation

**Testing Tools:**
- **Backend**: Jest, Supertest, Artillery, Stripe CLI for webhook testing
- **Frontend**: Jest, React Testing Library, Playwright, Storybook
- **Database**: Database seeding, transaction rollback, migration testing

### 6.2 Deployment Architecture

**Infrastructure Requirements:**
- **Application Server**: Node.js 22+ with PM2 process management
- **Database**: PostgreSQL 15+ with PostGIS extension
- **Reverse Proxy**: Nginx for SSL termination and load balancing
- **SSL Certificates**: Let's Encrypt with automatic renewal
- **Monitoring**: Application and infrastructure monitoring

**Environment Configuration:**
```bash
# Production environment variables
NODE_ENV=production
DATABASE_URL=postgresql://...
JWT_SECRET=...
STRIPE_SECRET_KEY=...
STRIPE_WEBHOOK_SECRET=...
GOOGLE_CLIENT_ID=...
GOOGLE_CLIENT_SECRET=...
```

**Deployment Process:**
1. **Database Migration**: Run migrations before deployment
2. **Application Build**: Build both backend and frontend
3. **Health Checks**: Verify application health before traffic routing
4. **Gradual Rollout**: Blue-green deployment for zero downtime
5. **Monitoring**: Real-time monitoring and alerting

**Scaling Considerations:**
- **Horizontal Scaling**: Multiple application instances behind load balancer
- **Database Scaling**: Read replicas for analytics queries
- **Caching**: Redis for session storage and rate limiting
- **CDN**: Static asset delivery optimization
- **Monitoring**: Comprehensive logging and metrics collection

### 6.3 Success Criteria & MVP Acceptance Tests

**Customer Journey:**
1. User can register with email/password or Google OAuth
2. User can select and subscribe to a plan via Stripe
3. User receives API key automatically upon subscription
4. User can successfully call `/api/v1/terminals` within rate limits
5. User can view usage statistics and manage API keys
6. User can change or cancel subscription

**Admin Journey:**
1. Admin can create and manage subscription plans with detailed configuration
2. Admin can view and manage all users with bulk operations
3. Admin can suspend users and manage subscription statuses (API keys immediately blocked)
4. Admin can view comprehensive system analytics, usage metrics, and revenue reports
5. Admin can handle payment issues, disputes, and billing problems
6. Admin can access complete audit trails for all subscription changes
7. Admin can configure system settings and automated workflows
8. Admin can process refunds and manage failed payment recovery

**System Requirements:**
1. All existing API endpoints remain functional
2. Payment events reflected in database within 60 seconds
3. System handles 1000+ concurrent users
4. 99.9% uptime with proper error handling
5. GDPR compliance for EU users

**Performance Benchmarks:**
- **API Response Time**: < 200ms for 95% of requests
- **Database Query Time**: < 100ms for 95% of queries
- **Page Load Time**: < 2 seconds for initial load
- **Payment Processing**: < 30 seconds for subscription changes
- **Webhook Processing**: < 5 seconds for Stripe events

---

## Implementation Summary

This comprehensive plan transforms the existing Postal Terminal API into a fully-featured SaaS platform while preserving all current functionality. The implementation follows a phased approach with clear milestones, comprehensive testing, and production-ready deployment strategies.

**Key Success Factors:**
- Preserve existing API functionality without breaking changes
- Implement robust authentication and authorization systems
- Integrate seamlessly with Stripe for subscription management
- Provide excellent user experience for both customers and administrators
- Maintain high performance and scalability standards
- Follow established coding standards and best practices

**Next Steps:**
1. Review and approve implementation plan
2. Set up development environment and project structure
3. Begin Sprint 1: Core Authentication implementation
4. Establish CI/CD pipeline and testing framework
5. Regular progress reviews and plan adjustments as needed

---

*Plan completed: 2025-07-09 | Status: Ready for implementation*