# Environment Variables Reference

This document provides a complete reference of all environment variables used by the Postal Terminal API.

## ✅ **Configuration Verification Status**

**Last Verified**: June 2025 (v2.1.0)
**Config Loading**: ✅ Working
**Smart Geocoding**: ✅ Properly configured
**All Variables**: ✅ Documented below
**Security**: ✅ Enhanced with configuration masking
**Hardcoded Values**: ✅ All externalized to environment variables

## 🔧 **Required Variables**

These variables **MUST** be set before running the application:

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `DATABASE_URL` | ✅ | PostgreSQL connection string with PostGIS | `postgresql://user:pass@localhost:5432/db` |
| `API_KEY_SECRET` | ✅ | Secret key for API key generation (64 chars) | Generate with `openssl rand -hex 32` |

## 🏗️ **Application Core**

| Variable | Default | Description |
|----------|---------|-------------|
| `NODE_ENV` | `development` | Application environment (`development`, `production`, `test`) |
| `PORT` | `3000` | Port number for the API server |
| `APP_VERSION` | `1.0.0` | Application version identifier |
| `LOG_LEVEL` | `info` | Logging level (`debug`, `info`, `warn`, `error`) |

## 🗄️ **Database Configuration**

| Variable | Default | Description |
|----------|---------|-------------|
| `DB_MAX_CONNECTIONS` | `20` | Maximum number of database connections |
| `DB_QUERY_TIMEOUT` | `30000` | Query timeout in milliseconds |
| `DB_STATEMENT_TIMEOUT` | `60000` | Statement timeout in milliseconds |

## ⚡ **Performance Settings**

| Variable | Default | Description |
|----------|---------|-------------|
| `CACHE_SIZE` | `100000` | In-memory cache size |
| `MAX_CONCURRENT_QUERIES` | `100` | Maximum concurrent database queries |
| `QUERY_COMPLEXITY_LIMIT` | `1000` | Query complexity limit |

## 🚦 **Rate Limiting**

| Variable | Default | Description |
|----------|---------|-------------|
| `DEFAULT_RATE_LIMIT` | `1000` | Default rate limit (requests per minute) |
| `BURST_LIMIT` | `2000` | Burst rate limit |
| `RATE_WINDOW_SIZE` | `60` | Rate limiting window size in seconds |

## 🔍 **Search Configuration**

| Variable | Default | Description |
|----------|---------|-------------|
| `SEARCH_MAX_RESULTS` | `100` | Maximum search results returned |
| `TRIGRAM_THRESHOLD` | `0.3` | Trigram similarity threshold for search |
| `SEARCH_TIMEOUT` | `5000` | Search timeout in milliseconds |
| `SEARCH_MIN_LENGTH` | `2` | Minimum search query length |
| `NEARBY_MAX_RADIUS` | `100` | Maximum radius for nearby search (km) |
| `NEARBY_DEFAULT_RADIUS` | `10` | Default radius for nearby search (km) |

## 🔄 **Data Synchronization**

| Variable | Default | Description |
|----------|---------|-------------|
| `AUTO_UPDATE_ENABLED` | `true` | Enable automatic data updates |
| `UPDATE_FREQUENCY` | `weekly` | Update frequency (`weekly`, `monthly`) |
| `DATA_SYNC_SCHEDULE` | `"0 2 * * *"` | Cron schedule for data sync (daily 2 AM) |
| `RUN_INITIAL_SYNC` | `true` | Run data sync on application startup |
| `SYNC_TIMEOUT` | `1800000` | Sync timeout in milliseconds (30 minutes) |

## 📁 **File Cache**

| Variable | Default | Description |
|----------|---------|-------------|
| `FILE_CACHE_DURATION_DAYS` | `30` | **File cache duration in days (30-day refresh cycle)** |
| `FORCE_DOWNLOAD_ON_SYNC` | `false` | Force download files even if cached |

## 🧠 **Smart Sync (Performance Optimization)**

| Variable | Default | Description |
|----------|---------|-------------|
| `ENABLE_SMART_SYNC` | `true` | **Enable smart sync that checks file changes before processing** |
| `SKIP_SYNC_WHEN_NO_CHANGES` | `true` | **Skip database sync when files haven't changed** |

## 🌍 **Geocoding Configuration**

### **Standard Geocoding**
| Variable | Default | Description |
|----------|---------|-------------|
| `NOMINATIM_BASE_URL` | `https://nominatim.openstreetmap.org/search` | Nominatim API base URL |
| `GEOCODING_RATE_LIMIT_DELAY` | `1000` | **Delay between geocoding requests for full preprocessing (ms)** |

### **Smart Geocoding (Production Optimization)** 🧠
| Variable | Default | Description |
|----------|---------|-------------|
| `SMART_GEOCODING_LIMIT` | `10` | **Max terminals to geocode during sync (production optimization)** |
| `SMART_GEOCODING_RATE_DELAY` | `800` | **Delay between geocoding requests during sync (ms)** |

## 📧 **Notifications (Optional)**

| Variable | Default | Description |
|----------|---------|-------------|
| `ADMIN_EMAIL` | `""` | Admin email for notifications |
| `ENABLE_EMAIL_NOTIFICATIONS` | `false` | Enable email notifications |
| `SMTP_HOST` | `""` | SMTP server hostname |
| `SMTP_PORT` | `587` | SMTP server port |
| `SMTP_USER` | `""` | SMTP username |
| `SMTP_PASSWORD` | `""` | SMTP password |
| `ENABLE_WEBHOOK_NOTIFICATIONS` | `false` | Enable webhook notifications |
| `WEBHOOK_URL` | `""` | Webhook URL for notifications |

## 🎯 **Current Configuration Summary**

Based on the verification, here are the **current active values**:

```bash
# Core Application
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# File Cache (30-day refresh cycle)
FILE_CACHE_DURATION_DAYS=30

# Smart Geocoding (Production Ready)
SMART_GEOCODING_LIMIT=10
SMART_GEOCODING_RATE_DELAY=800
GEOCODING_RATE_LIMIT_DELAY=1000

# Data Sync Schedule
DATA_SYNC_SCHEDULE="0 2 * * *"  # Daily at 2 AM
AUTO_UPDATE_ENABLED=true
```

## 🏭 **Production Configuration Example**

```bash
# ================================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# ================================================================================

# Core
NODE_ENV=production
PORT=3000
LOG_LEVEL=info

# Database
DATABASE_URL=postgresql://postal_api:SECURE_PASSWORD@localhost:5432/postal_terminal_api
DB_MAX_CONNECTIONS=20

# Security
API_KEY_SECRET=your_64_character_hex_string_here

# File Cache & Refresh Cycle
FILE_CACHE_DURATION_DAYS=30          # Auto-refresh every 30 days
FORCE_DOWNLOAD_ON_SYNC=false

# Data Synchronization
AUTO_UPDATE_ENABLED=true
DATA_SYNC_SCHEDULE="0 2 * * *"       # Daily at 2 AM
RUN_INITIAL_SYNC=false               # Set to false after initial deployment

# Smart Geocoding (30-day refresh optimization)
SMART_GEOCODING_LIMIT=10             # Geocode max 10 terminals during sync
SMART_GEOCODING_RATE_DELAY=800       # 800ms delay during sync
GEOCODING_RATE_LIMIT_DELAY=1000      # 1000ms delay for full preprocessing

# Smart Sync (Performance Optimization)
ENABLE_SMART_SYNC=true               # Enable intelligent sync behavior
SKIP_SYNC_WHEN_NO_CHANGES=true       # Skip database updates when files unchanged

# Performance
CACHE_SIZE=100000
MAX_CONCURRENT_QUERIES=100
DEFAULT_RATE_LIMIT=1000

# Search
SEARCH_MAX_RESULTS=100
NEARBY_DEFAULT_RADIUS=10

# Notifications (optional)
ADMIN_EMAIL=<EMAIL>
ENABLE_WEBHOOK_NOTIFICATIONS=true
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
```

## 🔧 **Development Configuration Example**

```bash
# ================================================================================
# DEVELOPMENT ENVIRONMENT CONFIGURATION
# ================================================================================

# Core
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# Database (local)
DATABASE_URL=postgresql://postgres:password@localhost:5432/postal_terminal_dev

# Security (development only)
API_KEY_SECRET=development_key_not_for_production_use_only

# File Cache (shorter for development)
FILE_CACHE_DURATION_DAYS=1           # Refresh daily for testing
FORCE_DOWNLOAD_ON_SYNC=false

# Data Sync (manual for development)
AUTO_UPDATE_ENABLED=false            # Manual sync in development
RUN_INITIAL_SYNC=true                # Run sync on startup

# Smart Geocoding (more aggressive for testing)
SMART_GEOCODING_LIMIT=20             # More terminals during sync for testing
SMART_GEOCODING_RATE_DELAY=500       # Faster rate for development
GEOCODING_RATE_LIMIT_DELAY=600       # Faster full preprocessing

# Performance (lower limits for development)
CACHE_SIZE=10000
MAX_CONCURRENT_QUERIES=10
DEFAULT_RATE_LIMIT=100

# Notifications (disabled for development)
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_WEBHOOK_NOTIFICATIONS=false
```

## ✅ **Verification Commands**

Test your configuration:

```bash
# Check configuration loading
node -e "const { config } = require('./dist/config/index.js'); console.log('Config loaded successfully:', !!config);"

# Verify smart geocoding settings
node -e "const { config } = require('./dist/config/index.js'); console.log('Smart Geocoding Limit:', config.smartGeocodingLimit); console.log('Smart Rate Delay:', config.smartGeocodingRateDelay);"

# Check file cache duration
node -e "const { config } = require('./dist/config/index.js'); console.log('File Cache Duration (days):', config.fileCacheDurationDays);"

# Verify database connection
npm run migrate -- --dry-run
```

## 🚨 **Common Issues**

### **Missing Required Variables**
```bash
Error: Missing required environment variables: DATABASE_URL, API_KEY_SECRET
```
**Solution**: Set the required variables in your `.env` file.

### **Invalid Environment Values**
```bash
Error: Invalid LOG_LEVEL value
```
**Solution**: Use valid values: `debug`, `info`, `warn`, `error`.

### **Smart Geocoding Not Working**
```bash
# Check if variables are loaded
node -e "console.log('SMART_GEOCODING_LIMIT:', process.env.SMART_GEOCODING_LIMIT);"
```

### **File Cache Not Refreshing**
```bash
# Check file cache configuration
node -e "const { config } = require('./dist/config/index.js'); console.log('File cache:', config.fileCacheDurationDays, 'days');"
```

---

## 🆕 **Version 2.1.0 Additions**

### **New Environment Variables Added**

| Variable | Default | Description |
|----------|---------|-------------|
| `LP_EXPRESS_ORIGIN_URL` | `https://lpexpress.lt` | Origin header for LP Express requests |
| `LP_EXPRESS_REFERER_URL` | `https://lpexpress.lt/` | Referer header for LP Express requests |
| `OMNIVA_REFERER_URL` | `https://www.omniva.lt/private/send/parcel_terminals` | Referer header for Omniva requests |
| `DPD_REFERER_URL` | `https://www.dpd.com/lt/private/send_parcel/pickup_locations` | Referer header for DPD requests |
| `VENIPAK_REFERER_URL` | `https://venipak.lt/` | Referer header for Venipak requests |
| `USER_AGENT` | `Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36` | User agent for HTTP requests |

### **Configuration Improvements**
- ✅ **All hardcoded URLs externalized** to environment variables
- ✅ **Enhanced security** with configuration value masking
- ✅ **Improved flexibility** for different deployment environments
- ✅ **Backward compatibility** maintained with sensible defaults
- ✅ **Security headers** configurable via environment
- ✅ **Rate limiting** fully configurable

### **Security Enhancements**
- 🔐 **Configuration masking** prevents sensitive data exposure in logs
- 🛡️ **Environment validation** ensures secure configuration
- 📊 **Security logging** tracks configuration access
- 🚨 **Rate limiting** prevents abuse with configurable limits

---

**Last Updated**: June 2025 (v2.1.0)
**Verification Status**: ✅ All environment variables properly configured and working
**Security Status**: ✅ Enhanced with comprehensive security measures