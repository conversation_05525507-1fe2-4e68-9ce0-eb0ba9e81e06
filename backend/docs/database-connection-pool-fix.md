# Database Connection Pool Fix

## Issue Description

The postal-terminal-api was experiencing database connection leaking issues, evidenced by repeated log messages appearing every ~2 seconds:
```
[2025-06-24T21:29:05.327Z] INFO: 🔗 New database connection established
```

This indicated that new database connections were being created frequently instead of reusing existing connections from the pool.

## Root Cause Analysis

1. **Excessive Connection Logging**: The `pool.on('connect')` event was logging every new physical connection creation, creating noise in development logs.

2. **Suboptimal Pool Configuration**: 
   - No minimum connections configured (causing frequent connection creation/destruction)
   - Short idle timeout (30 seconds) causing connections to be closed and recreated frequently
   - Missing connection keep-alive settings

3. **Inefficient Health Checks**: Health checks were using separate connections instead of being combined, increasing connection usage.

## Solution Implemented

### 1. Enhanced Connection Pool Configuration

**File**: `src/database/connection.ts`

- **Minimum Connections**: Added `min: 2` to maintain a baseline of connections
- **Increased Idle Timeout**: Changed from 30 seconds to 5 minutes (300,000ms)
- **Connection Keep-Alive**: Enabled TCP keep-alive with 10-second initial delay
- **Connection Limits**: Added `maxUses: 7500` to prevent connection degradation
- **Pool Persistence**: Set `allowExitOnIdle: false` to keep pool alive

### 2. Improved Connection Monitoring

**New Features**:
- Connection statistics tracking (created, acquired, released, errors)
- Pool status monitoring functions
- Reduced logging frequency (every 10th connection instead of every connection)
- Enhanced metrics for Prometheus monitoring

**New Functions**:
- `getPoolStats()`: Get current pool statistics
- `getConnectionStats()`: Get connection lifecycle statistics  
- `logPoolStatus()`: Log current pool status for debugging

### 3. Optimized Health Checks

**File**: `src/routes/health.ts`

- **Combined Health Check**: Created `checkDatabaseAndPostGISHealth()` to run both database and PostGIS checks in a single connection
- **Efficient Connection Usage**: Updated health endpoints to use the combined check
- **Pool Metrics**: Added database connection pool metrics to `/metrics` endpoint
- **Debug Endpoint**: Added `/pool-status` endpoint for connection pool debugging

### 4. Updated Configuration

**Files**: `src/config/index.ts`, `src/types/api.ts`

Added new database configuration options:
- `DB_MIN_CONNECTIONS`: Minimum pool connections (default: 2)
- `DB_IDLE_TIMEOUT`: Connection idle timeout (default: 300000ms = 5 minutes)
- `DB_CONNECTION_TIMEOUT`: Connection establishment timeout (default: 30000ms)

### 5. Enhanced Application Startup

**File**: `src/index.ts`

- Use combined health check during startup to reduce connection usage
- Log initial pool status for monitoring
- More efficient database connectivity verification

## Configuration Changes

### Environment Variables

New environment variables added to deployment configurations:

```bash
# Database connection pool settings
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=2
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=300000
DB_QUERY_TIMEOUT=30000
DB_STATEMENT_TIMEOUT=60000
```

### Files Updated

1. `scripts/deploy.sh` - Updated deployment configuration
2. `ecosystem.config.js` - Updated PM2 configuration
3. `.env.example` - Would need to be updated with new variables

## Testing

### Connection Pool Test Script

**File**: `src/scripts/test-connection-pool.ts`

Created a comprehensive test script to verify:
- Connection reuse efficiency
- Pool statistics accuracy
- No connection leaks
- Concurrent query handling
- Health check optimization

**Usage**:
```bash
npm run test-connection-pool
```

### Monitoring Endpoints

1. **Health Check**: `GET /api/v1/health` - Optimized health check
2. **Metrics**: `GET /api/v1/metrics` - Prometheus metrics with pool statistics
3. **Pool Status**: `GET /api/v1/pool-status` - Detailed pool debugging information

## Expected Results

After implementing these fixes:

1. **Eliminated Frequent Logging**: The "New database connection established" messages should appear much less frequently (only when pool actually needs to create new connections)

2. **Improved Connection Efficiency**: 
   - Connections are reused more effectively
   - Minimum connections maintained to reduce creation overhead
   - Longer idle timeout reduces unnecessary connection cycling

3. **Better Monitoring**: 
   - Detailed pool statistics available
   - Connection lifecycle tracking
   - Prometheus metrics for monitoring

4. **Reduced Database Load**: 
   - Fewer connection establishments
   - More efficient health checks
   - Better connection pooling

## Verification Steps

1. **Start the application** and observe startup logs
2. **Monitor connection logs** - should see fewer "New database connection established" messages
3. **Check pool status**: `curl http://localhost:3000/api/v1/pool-status`
4. **Run connection test**: `npm run test-connection-pool`
5. **Monitor metrics**: `curl http://localhost:3000/api/v1/metrics | grep db_connections`

## Performance Impact

- **Positive**: Reduced database connection overhead
- **Positive**: More efficient resource utilization
- **Positive**: Better connection reuse
- **Minimal**: Slight increase in memory usage due to minimum connections
- **Positive**: Reduced logging noise in development

## Maintenance

- Monitor the new pool metrics regularly
- Adjust `DB_MIN_CONNECTIONS` and `DB_IDLE_TIMEOUT` based on usage patterns
- Use the test script periodically to verify pool health
- Check pool status endpoint if connection issues arise
