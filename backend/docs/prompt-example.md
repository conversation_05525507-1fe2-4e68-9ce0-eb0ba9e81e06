lets do an in-depth systematic factual analysis of the @ by recursively scanning all the files 1 by 1 to understand it implementation, do not make assumptions.


We recently created this opencart plugin for opencart v3.0.x and php 7.2+ based on @docs/api-documentation.md, then we implemented the tracking functionality in opencart plugin based on this plan @opencart-postal-terminal-plugin-tracking-integration-plan.md
lets do an in-depth systematic factual analysis of the @opencart-postal-terminal-plugin and abovementiond by recursively scanning all the files 1 by 1 to understand it implementation, do not make assumptions.

here is opencart file if you need to understand the system https://github.com/condor2/Opencart_30xx/tree/master/upload

Then let fix these issues
Tracking form is not showing on admin ordeer details (order_info page)
# Possible reasons according me:
- Missing databsae table due to foreing key contraint - fix the @install_tables.sql file
- Missing integration - make sure the plan @opencart-postal-terminal-plugin-tracking-integration-plan.md was fully implemented
- Missing events - make sure events are properly implemented and registered for update the parcel tracking data and data syncronization.
- 
Find all the possible root cause, create a details plan to fix all these issues.
Be pricise and straight forward. DO NO MAKE ASSUMPTIONS. DO NOT WANDER. Write the plan to a markdown .md file.


Found 11 errors in 3 files.

Errors  Files
     3  src/routes/admin/analytics.ts:92
     3  src/services/StripeService.ts:36
     5  src/services/UsageAlertService.ts:111

and  79 problems (0 errors, 79 warnings) lint related.
once everything above is done count @routes how many total endpoints are there, then test all the api endpoints 1 by 1 using curl in terminal, every endpoint must handle errors properly, return proper error code and message. and for sucess must return proper data as inteded not just empty success message.
Along side the testing write it to a markdown `full-api-documentation.md` file, for each endpoint include proper format with request reponse format, if there is issue then resolve it. 

