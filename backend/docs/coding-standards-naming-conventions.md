# Coding Standards and Naming Conventions

This document outlines the coding standards and naming conventions for the StablecoinPay project. Adhering to these standards ensures code consistency, readability, and maintainability across the project.

## Table of Contents

- [JavaScript/TypeScript Naming Conventions](#javascripttypescript-naming-conventions)
- [React Component Conventions](#react-component-conventions)
- [Database Naming Conventions](#database-naming-conventions)
- [API Endpoint Conventions](#api-endpoint-conventions)
- [General Coding Standards](#general-coding-standards)
- [File Structure Conventions](#file-structure-conventions)
- [Error Handling Conventions](#error-handling-conventions)
- [Documentation Conventions](#documentation-conventions)
- [Consistency Patterns](#consistency-patterns)

## JavaScript/TypeScript Naming Conventions

### Variables and Functions

- Use camelCase for variables and function names
- Names should be descriptive and clear about their purpose
- Examples: `getUserBalance()`, `transactionAmount`, `calculateFee()`

```typescript
const transactionAmount = 100;
function processPayment(amount, currency) {
  /* ... */
}
const getUserBalance = async userId => {
  /* ... */
};
```

### Classes and Constructors

- Use PascalCase for class names and constructors
- Class names should be nouns that describe the entity
- Examples: `BlockchainAdapter`, `WalletManager`, `TransactionProcessor`

```typescript
class BlockchainAdapter {
  constructor(config) {
    /* ... */
  }
}

class WalletManager {
  getWallets() {
    /* ... */
  }
}
```

### Constants

- Use UPPER_SNAKE_CASE for constants
- Group related constants in objects or enums
- Examples: `API_BASE_URL`, `DEFAULT_CONFIRMATION_THRESHOLD`

```typescript
const API_BASE_URL = "https://api.example.com";
const DEFAULT_CONFIRMATION_THRESHOLD = 6;

// Group related constants in objects
const BLOCKCHAIN = {
  ETHEREUM: "ethereum",
  TRON: "tron",
  BSC: "bsc",
  SOLANA: "solana",
};
```

### Private Properties and Methods

- Prefix with underscore for private properties/methods
- Indicates that these elements are for internal use only
- Examples: `_processTransaction()`, `_apiKey`

```typescript
class PaymentProcessor {
  _apiKey = "secret";

  processPayment() {
    this._validatePayment();
  }

  _validatePayment() {
    // Internal method logic
  }
}
```

### File Names

- Use kebab-case for file names (general)
- Examples: `blockchain-adapter.js`, `wallet-manager.js`
- Exception: React components use PascalCase
- Examples: `DashboardView.jsx`, `TransactionTable.jsx`

## React Component Conventions

- Component names should be descriptive and use PascalCase
- Props should use camelCase
- Folder structure should group related components

```typescript
// Component example
function TransactionList({ transactions, onSelect }) {
  return (/* component JSX */);
}

// Usage example
<TransactionList
  transactions={data}
  onSelect={handleSelection}
/>
```

## Database Naming Conventions

### Models

- Use singular PascalCase for model names
- Model names should represent a single instance of the entity
- Examples: `Wallet`, `Transaction`, `User`

```prisma
model Wallet {
  id Int @id @default(autoincrement())
  // fields...
}

model Transaction {
  id Int @id @default(autoincrement())
  // fields...
}
```

### Database Fields

- Use camelCase for field names
- Field names should be descriptive of the data they store
- Examples: `createdAt`, `walletAddress`, `transactionHash`

```prisma
model Wallet {
  id Int @id @default(autoincrement())
  blockchain String
  address String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  // more fields...
}
```

### Foreign Keys

- Use camelCase with descriptive names
- Include the referenced model name
- Examples: `userId`, `walletId`, `paymentId`

```prisma
model Transaction {
  id Int @id @default(autoincrement())
  walletId Int
  wallet Wallet @relation(fields: [walletId], references: [id])
  // more fields...
}
```

### Consistency Patterns

For database models with similar features, use consistent naming:

#### Approval Workflow Fields

All approval-based models should implement the same pattern:

- `isApproved` boolean field
- `approvedById` foreign key to User model
- `approvedAt` timestamp
- `approvalNotes` optional text field

#### Metadata Handling

All models with metadata should use the same approach:

- `metadata` field as JSON string
- Use consistent helper functions for handling metadata

```javascript
// Sample metadata helpers
const metadataHelpers = {
  // Parse metadata string to object with error handling
  parse: jsonString => {
    if (!jsonString) return {};
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      logger.warn(`Failed to parse metadata: ${error.message}`);
      return {};
    }
  },

  // Convert object to metadata string with error handling
  stringify: data => {
    if (!data) return "{}";
    try {
      return JSON.stringify(data);
    } catch (error) {
      logger.warn(`Failed to stringify metadata: ${error.message}`);
      return "{}";
    }
  },

  // Get a specific value from metadata with dot notation
  getValue: (jsonString, path, defaultValue = null) => {
    const data = metadataHelpers.parse(jsonString);
    const parts = path.split(".");
    let current = data;

    for (const part of parts) {
      if (
        current === null ||
        current === undefined ||
        typeof current !== "object"
      ) {
        return defaultValue;
      }
      current = current[part];
    }

    return current !== undefined ? current : defaultValue;
  },

  // Set a specific value in metadata with dot notation
  setValue: (jsonString, path, value) => {
    const data = metadataHelpers.parse(jsonString);
    const parts = path.split(".");
    let current = data;

    // Navigate to the nested object, creating objects as needed
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!(part in current) || typeof current[part] !== "object") {
        current[part] = {};
      }
      current = current[part];
    }

    // Set the value
    current[parts[parts.length - 1]] = value;
    return metadataHelpers.stringify(data);
  },
};
```

## API Endpoint Conventions

### URL Structure

- Use kebab-case for URL paths
- Use plural nouns for resources
- Examples: `/api/wallets`, `/api/transactions`

Resource endpoints follow these patterns:

- Public endpoints: `/api/v1/[resource]`
- Internal endpoints: `/api/v1/[resource]`
- Resource-specific actions: `/api/v1/[resource]/:id/[action]`
- Global actions: `/api/v1/[resource]/[action]`

### Naming Conventions

- All resource endpoints use plural nouns (e.g., `/payments`, `/transactions`, `/wallets`)
- Action endpoints use verbs in kebab-case (e.g., `/verify-txid`, `/export-data`)
- Query parameters use camelCase (e.g., `?startDate=`, `?isActive=`)

### RESTful Endpoints

Each resource follows a RESTful pattern with consistent CRUD operations:

- `GET /api/v1/[resource]` - List resources with filtering
- `POST /api/v1/[resource]` - Create new resource
- `GET /api/v1/[resource]/:id` - Get specific resource
- `PUT /api/v1/[resource]/:id` - Update resource
- `DELETE /api/v1/[resource]/:id` - Delete resource

For operations that don't fit the CRUD pattern:

- `POST /api/v1/[resource]/:id/[action]` - Perform action on resource

### Query Parameters

- Use camelCase for query parameters
- Examples: `?startDate=2023-01-01&endDate=2023-12-31&isActive=true`

### Response Fields

- Use camelCase for all JSON response fields
- Example: `{ "walletAddress": "0x...", "createdAt": "..." }`

### Standardized Response Formats

- Success responses will always include a consistent structure
- Pagination will use consistent parameters (`page`, `limit`, `total`, `items`)
- All timestamps will use ISO 8601 format (UTC)

## Error Handling Conventions

### Error Response Format

All API errors should follow this consistent format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      // Optional additional error details
    },
    "requestId": "unique-request-id",
    "timestamp": "2025-04-21T14:30:00Z"
  }
}
```

### Error Codes

Error codes should be clear, specific, and use uppercase snake case:

- `VALIDATION_ERROR`
- `AUTH_ERROR`
- `PERMISSION_ERROR`
- `NOT_FOUND`
- `BLOCKCHAIN_ERROR`
- `BLOCKCHAIN_CONNECTION_ERROR`
- `SERVER_ERROR`
- etc.

### Error Messages

Error messages should:

- Start with a capital letter
- Not end with a period
- Be concise and descriptive
- Use "must be" instead of "is required" for consistency

## General Coding Standards

### Comments and Documentation

- Use JSDoc for documenting functions, classes, and complex code blocks
- Add comments for non-obvious code sections
- Keep comments up-to-date with code changes

### Code Formatting

- Use consistent indentation (2 spaces)
- Use single quotes for strings
- Add semicolons at the end of statements
- Use trailing commas in multiline object/array literals

### TypeScript Usage

- Define proper interfaces for complex objects
- Use proper typing for function parameters and return values
- Avoid using `any` type when possible

### Testing

- Write unit tests for core functionality
- Name test files with `.test.ts` or `.spec.ts` suffix
- Group tests logically with describe blocks
- Use clear test names describing the expected behavior

## File Structure Conventions

The project structure strictly separates backend API and admin dashboard components:

```
stablecoin-pay/
├── backend/                 # Backend API implementation
│   ├── src/
│   │   ├── api/             # API route definitions
│   │   ├── blockchain/      # Blockchain integration adapters
│   │   ├── models/          # Data models and database interaction
│   │   ├── services/        # Business logic implementation
│   │   ├── utils/           # Utility functions
│   │   └── app.js           # Express application entry point
│   ├── prisma/              # Prisma schema and migrations
│   ├── tests/               # Backend API tests
│   └── package.json         # Backend dependencies
│
├── admin-dashboard/         # Admin interface implementation
│   ├── public/              # Static assets
│   ├── src/
│   │   ├── components/      # React UI components
│   │   ├── pages/           # Dashboard page components
│   │   ├── services/        # API integration services
│   │   ├── utils/           # Frontend utilities
│   │   └── App.jsx          # Main React component
│   └── package.json         # Frontend dependencies
│
└── docs/                    # Documentation
    ├── api/                 # API documentation
    └── admin/               # Admin dashboard documentation
```

### Separation Principles

1. **Independent Deployment**

   - No shared code libraries between frontend and backend

2. **Communication via API Only**

   - All business logic remains in the backend

3. **Distinct Authentication**
   - Backend API uses API keys for public endpoints
   - Separate auth flows and middleware

## Documentation Conventions

### Code Documentation

- Use JSDoc comments for functions, classes, and interfaces
- Document parameters, return values, and thrown exceptions
- Include examples for complex functions

### API Documentation

- Document all API endpoints using OpenAPI/Swagger
- Include request/response examples
- Document error responses and status codes

### README Files

- Include setup instructions
- Document common workflows
- Include troubleshooting information

## Conclusion

Following these coding standards and naming conventions will ensure consistency across the StablecoinPay project, making the codebase more maintainable, readable, and easier to collaborate on. All team members should adhere to these standards when contributing to the project.
