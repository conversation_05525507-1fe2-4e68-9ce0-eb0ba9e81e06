# Postal service - parcel locker / terminal providers

## LPExpress
- Data fetching
curl "https://api-esavitarna.post.lt/terminal/list/csv" \
  -H "accept: application/json, text/plain, */*" \
  -H "accept-encoding: gzip, deflate, br, zstd" \
  -H "accept-language: en" \
  -H "connection: keep-alive" \
  -H "dnt: 1" \
  -H "host: api-esavitarna.post.lt" \
  -H "origin: https://lpexpress.lt" \
  -H "referer: https://lpexpress.lt/" \
  -H "sec-ch-ua: \"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"" \
  -H "sec-ch-ua-mobile: ?0" \
  -H "sec-ch-ua-platform: \"macOS\"" \
  -H "sec-fetch-dest: empty" \
  -H "sec-fetch-mode: cors" \
  -H "sec-fetch-site: cross-site" \
  -H "sec-fetch-storage-access: active" \
  -H "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" \
  --compressed \
  -o terminals.csv

- Tracking
https://api-esavitarna.post.lt/tracking/{TRACKING_NUMBER}/events


https://api-esavitarna.post.lt/tracking/CC937862025LT/events
CC937862025LT

[{"publicStateType":"LABEL_CREATED","publicStateText":"Siunta sukurta","isRoutingToTerminalAvailable":false,"id":"18a1fdad-a243-f9ba-6f73-82840d775d9b","mailBarcode":"CC937862025LT","eventDate":"2025-06-20T13:04:37+03:00","countryCode":"LT","publicEventType":"LABEL_CREATED","publicEventText":"Sukurtas siuntos lipdukas"},{"publicStateType":"PARCEL_RECEIVED","publicStateText":"Siunta priimta","isRoutingToTerminalAvailable":false,"id":"e50705bd-73ed-cf2c-da1c-1febd78f89af","mailBarcode":"CC937862025LT","eventDate":"2025-06-22T14:33:56+03:00","location":"5201 paštomatas, Centrinis Nemėžio paštas, V. Sirokomlės g. 4, Nemėžio k.","countryCode":"LT","publicEventType":"ACCEPTED_TERMINAL","publicEventText":"Siunta įdėta į LP EXPRESS paštomatą išsiuntimui"},{"publicStateType":"ON_THE_WAY","publicStateText":"Siunta kelyje","isRoutingToTerminalAvailable":false,"id":"3de7669c-227f-6142-c527-620a6c7b8b02","mailBarcode":"CC937862025LT","eventDate":"2025-06-23T13:20:57+03:00","location":"5201 paštomatas, Centrinis Nemėžio paštas, V. Sirokomlės g. 4, Nemėžio k.","countryCode":"LT","publicEventType":"RECEIVED_TERMINAL_OUT","publicEventText":"Siunta paimta kurjerio iš LP EXPRESS paštomato"},{"publicStateType":"ON_THE_WAY","publicStateText":"Siunta kelyje","isRoutingToTerminalAvailable":false,"id":"dc9045d2-3d58-0e50-68ec-c343b7d86416","mailBarcode":"CC937862025LT","eventDate":"2025-06-24T04:19:38+03:00","location":"Vilnius","countryCode":"LT","publicEventType":"RECEIVED_LC","publicEventText":"Siunta skirstymo centre"},{"publicStateType":"ON_THE_WAY","publicStateText":"Siunta kelyje","isRoutingToTerminalAvailable":false,"id":"e002cf2a-2e51-b9cf-7239-3a3667ce78ca","mailBarcode":"CC937862025LT","eventDate":"2025-06-24T08:08:16+03:00","countryCode":"LT","publicEventType":"TRANSFERRED_FOR_DELIVERY_COURIER","publicEventText":"Siunta perduota pristatymui"},{"publicStateType":"ON_THE_WAY","publicStateText":"Siunta kelyje","isRoutingToTerminalAvailable":false,"id":"b5fc9cf6-1d13-95fd-2025-9fc269879465","mailBarcode":"CC937862025LT","eventDate":"2025-06-24T12:14:23+03:00","location":"0140 paštomatas, Norfa XL, Genių g. 10A, Vilnius","countryCode":"LT","publicEventType":"RECEIVED_TERMINAL","publicEventText":"Siunta pristatyta į LPEXPRESS paštomatą"},{"publicStateType":"PARCEL_DELIVERED","publicStateText":"Siunta pristatyta","isRoutingToTerminalAvailable":false,"id":"51c0e611-3d28-cbf0-f725-05ac0435e5a8","mailBarcode":"CC937862025LT","eventDate":"2025-06-24T16:51:48+03:00","location":"0140 paštomatas, Norfa XL, Genių g. 10A, Vilnius","countryCode":"LT","publicEventType":"DELIVERY_DELIVERED","publicEventText":"Siunta atsiimta gavėjo"}]

## Omniva
- Data fetching
https://www.omniva.lt/locations.json

- Tracking
https://mano.omniva.lt/api/track/shipment/{TRACKING_NUMBER}

https://mano.omniva.lt/api/track/shipment/CE209166688EE

CE209166688EE


{"shipmentBarcode":"CE209166688EE","currentDeliveryChannel":"PARCEL_MACHINE","returningShipment":false,"customsDeclarationRequired":false,"deliveryLocation":{"zip":"99593","locationName":"Panevėžio LIDL J. Basanavičiaus paštomatas","servicePointTypeCode":"PARCEL_MACHINE","countryCode":"LT"},"estimatedDeliveryDate":"2025-05-22","states":[{"stateCode":"DELIVERED"}],"currentStateCode":"DELIVERED","events":[{"eventId":**********,"eventCode":"TRT_SHIPMENT_STORED_IN_PM","eventName":"Shipment is ready for collection from the parcel machine","eventDate":"2025-05-21T09:03:37.3573943","location":{"zip":"99593","locationName":"Panevėžio LIDL J. Basanavičiaus paštomatas","servicePointTypeCode":"PARCEL_MACHINE","countryCode":"LT","translations":{}},"eventParameters":[]},{"eventId":1158335494,"eventCode":"TRT_SHIPMENT_DELIVERED","eventName":"Shipment delivered","eventDate":"2025-05-21T09:15:48.7349434","location":{"zip":"99593","locationName":"Panevėžio LIDL J. Basanavičiaus paštomatas","servicePointTypeCode":"PARCEL_MACHINE","countryCode":"LT","translations":{}},"eventParameters":[]},{"eventId":1157555741,"eventCode":"TRT_SHIPMENT_ARRIVED_TO_PO","eventName":"Shipment has arrived at a postal facility for further processing","eventDate":"2025-05-20T21:24:53.481","location":{"zip":"99993","locationName":"Kaunas sorting line","servicePointTypeCode":"WORKSTATION","countryCode":"LT","translations":{"lv":{"id":4162,"name":"Lietuvas šķirošanas centrs"},"et":{"id":4161,"name":"Leedu sorteerimiskeskus"},"ru":{"id":4164,"name":"Cортировочный центр Литвы"},"en":{"id":4160,"name":"Lithuanian sorting center"},"lt":{"id":4163,"name":"Skirstymo centras Lietuvoje"}}},"eventParameters":[]},{"eventId":1157139327,"eventCode":"TRT_SHIPMENT_DEPARTED_FROM_PM","eventName":"Shipment departed from parcel machine","eventDate":"2025-05-20T14:27:25.2446482","location":{"zip":"77796","locationName":"Rudaminos IKI paštomatas","servicePointTypeCode":"PARCEL_MACHINE","countryCode":"LT","translations":{}},"eventParameters":[]},{"eventId":1157127320,"eventCode":"TRT_SHIPMENT_HANDOVER_TO_PM","eventName":"Sender has placed the shipment in the parcel machine","eventDate":"2025-05-20T14:14:46","location":{"zip":"77796","locationName":"Rudaminos IKI paštomatas","servicePointTypeCode":"PARCEL_MACHINE","countryCode":"LT","translations":{}},"eventParameters":[]},{"eventId":1157023561,"eventCode":"TRT_SHIPMENT_REGISTERED","eventName":"Shipment registered","eventDate":"2025-05-20T13:05:22.6129462","eventParameters":[]}]}

## DPD
- Data fetching
https://www.dpd.com/wp-content/uploads/sites/232/2025/04/DPD-pastomatu-sarasas-aptarnavimo-laikai-3.xlsx

- Tracking - no easy way or json available

https://www.dpdgroup.com/lt/mydpd/my-parcels/incoming?parcelNumber=05815092708653

copy(JSON.stringify({
  parcelNumber: document.querySelector('.parcelDetailsBox span')?.textContent.trim(),
  steps: Array.from(document.querySelectorAll('.parcelStatus .row'))
    .map(row => {
      const status = row.querySelector('.col-xs-7 span')?.textContent.trim();
      const date = row.querySelector('.col-xs-5 .inlineDate')?.textContent.trim();
      return status ? { status, date: date || null } : null;
    }).filter(Boolean)
}, null, 2));
console.log('Copied parcel tracking info to clipboard.');

{parcelNumber: '05815092708653', steps: Array(5)}
parcelNumber
: 
"05815092708653"
steps
: 
(5) [{…}, {…}, {…}, {…}, {…}]
[[Prototype]]
: 
Object

const puppeteer = require('puppeteer');

async function trackParcel(parcelNumber) {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Go to the DPD tracking page
  await page.goto('https://www.dpdgroup.com/lt/mydpd/my-parcels/incoming');

  // Enter the parcel number into the search form
  await page.waitForSelector('#searchParcel');
  await page.type('#searchParcel', parcelNumber);
  await page.click('.submit-btn'); // submit icon

  // Wait for parcel status info to appear
  await page.waitForSelector('.parcelStatus', { timeout: 10000 });

  // Extract parcel steps
  const result = await page.evaluate(() => {
    const steps = Array.from(document.querySelectorAll('.parcelStatus .row'))
      .map(row => {
        const status = row.querySelector('.col-xs-7 span')?.textContent.trim();
        const date = row.querySelector('.col-xs-5 .inlineDate')?.textContent.trim();
        return status ? { status, date: date || null } : null;
      }).filter(Boolean);
    
    const parcelNumber = document.querySelector('.parcelDetailsBox span')?.textContent.trim();

    return { parcelNumber, steps };
  });

  await browser.close();
  return result;
}

// 🔁 Example usage:
(async () => {
  const parcels = ['05815092708653', 'OTHER_PARCEL_NUMBER'];
  for (const pn of parcels) {
    const info = await trackParcel(pn);
    console.log(info);
  }
})();

## Venipak
- Data fetching
https://go.venipak.lt/ws/get_pickup_points

- Tracking
https://venipak.com/lt/wp-json/v1/shipment-tracker/{TRACKING_NUMBER}

https://venipak.com/lt/wp-json/v1/shipment-tracker/87262530

87262530

It returns json with unicode

{"parcels":"<input type=\"hidden\" id=\"searchQuery\" value=\"PAIE\u0160KOS RAKTA\u017dODIS: 87262530\"><input type=\"hidden\" id=\"consignmentID\" value=\"87262530\"><input type=\"hidden\" id=\"starting-position\" data-lat=\"54.6646930\" data-lng=\"25.2476460\"><div id=\"tracking-heading-rest\" style=\"display: none;\" class=\"tracking-heading \"><p class=\"tracking-heading-caption\"><span class=\"icon not-delivered\"><\/span>Dar nepristatytos pakuot\u0117s:<\/p><\/div><div class=\"tracking-info\"><\/div><div id=\"tracking-heading-delivered\"  class=\"tracking-heading \"><p class=\"tracking-heading-caption\"><span class=\"icon delivered\"><\/span>Pristatytos pakuot\u0117s:<\/p><button class=\"parcel-nr parcel-delivered active\" \" data-for=\"V03363E0261957\">V03363E0261957<\/button><\/div><div class=\"tracking-info\"><div class=\"parcel-info-container\" data-parcel-status=\"9\" id=\"parcel-V03363E0261957\"><div class=\"parcel-info status-1\" data-order-nr=\"1\" data-lat=\"0\" data-lng=\"0\" data-status=\"2\"><div class=\"info-time\">2025-05-06 16:52<\/div><div class=\"info-place\">Pas siunt\u0117j\u0105<\/div><div class=\"info-status-icon icon-2\">1<\/div><\/div><div class=\"parcel-info status-2\" data-order-nr=\"2\" data-lat=\"54.6646930\" data-lng=\"25.2476460\" data-status=\"1\"><div class=\"info-time\">2025-05-06 18:52<\/div><div class=\"info-place\">Terminale, VILNIUS<\/div><div class=\"info-status-icon icon-1\">2<\/div><\/div><div class=\"parcel-info status-3\" data-order-nr=\"3\" data-lat=\"54.6485980\" data-lng=\"25.1217820\" data-status=\"1\"><div class=\"info-time\">2025-05-07 05:52<\/div><div class=\"info-place\">Terminale<\/div><div class=\"info-status-icon icon-1\">3<\/div><\/div><div class=\"parcel-info status-4\" data-order-nr=\"4\" data-lat=\"54.6485980\" data-lng=\"25.1217820\" data-status=\"2\"><div class=\"info-time\">2025-05-07 09:26<\/div><div class=\"info-place\">Pakeliui pas gav\u0117j\u0105<\/div><div class=\"info-status-icon icon-2\">4<\/div><\/div><div class=\"parcel-info status-5\" data-order-nr=\"5\" data-lat=\"0\" data-lng=\"0\" data-status=\"9\"><div class=\"info-time\">2025-05-07 15:50<\/div><div class=\"info-place\">Pristatyta<\/div><div class=\"info-status-icon icon-9\">5<\/div><div class=\"info-receiver-explanation\">Pristatymo koordina\u010di\u0173 bei kit\u0173 detali\u0173 n\u0117ra<\/div><\/div><\/div><\/div>\r\n                "}

