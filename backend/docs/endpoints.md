# API Endpoints Documentation

This document provides a comprehensive overview of all API endpoints in the Postal Terminal API backend.

## Base URL
- Development: `http://localhost:3000/api/v1`
- Production: not available yet

## Authentication

The API uses two types of authentication:

1. **JWT Authentication** - For user-specific endpoints
   - Header: `Authorization: Bearer <jwt_token>`
   - Used for user profile, subscriptions, analytics, etc.

2. **API Key Authentication** - For public API endpoints
   - Header: `X-API-Key: <api_key>`
   - Used for terminals, tracking, and other public data

## Endpoint Categories

### 1. Health Check

#### GET /health
- **Description**: Basic health check endpoint
- **Authentication**: None
- **Response**: System status and uptime

---

### 2. Authentication Endpoints

#### POST /auth/register
- **Description**: Register a new user account
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "securePassword123",
    "firstName": "<PERSON>",
    "lastName": "Doe"
  }
  ```
- **Response**: User details and JWT tokens

#### POST /auth/login
- **Description**: Authenticate user and get tokens
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "securePassword123"
  }
  ```
- **Response**: User details, access token, and refresh token

#### POST /auth/refresh
- **Description**: Refresh access token using refresh token
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "refreshToken": "refresh_token_here"
  }
  ```
- **Response**: New access and refresh tokens

#### POST /auth/logout
- **Description**: Revoke refresh token (logout)
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "refreshToken": "refresh_token_here"
  }
  ```
- **Response**: Success confirmation

#### POST /auth/forgot-password
- **Description**: Initiate password reset process
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **Response**: Success message (doesn't reveal if email exists)

#### POST /auth/reset-password
- **Description**: Reset password using token
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "token": "reset_token_here",
    "newPassword": "newSecurePassword123"
  }
  ```
- **Response**: Success confirmation

#### POST /auth/verify-email
- **Description**: Verify email address using token
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "token": "verification_token_here"
  }
  ```
- **Response**: Success confirmation

#### POST /auth/resend-verification
- **Description**: Resend email verification link
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **Response**: Success message

#### GET /auth/google/callback
- **Description**: Google OAuth callback endpoint
- **Authentication**: None
- **Query Parameters**: OAuth callback parameters
- **Response**: User details and JWT tokens

---

### 3. User Management

#### GET /users/me
- **Description**: Get authenticated user's profile
- **Authentication**: JWT required
- **Response**: User profile details

#### PATCH /users/me
- **Description**: Update authenticated user's profile
- **Authentication**: JWT required
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  }
  ```
- **Response**: Updated user profile

#### DELETE /users/me
- **Description**: Soft delete authenticated user's account
- **Authentication**: JWT required
- **Response**: Success confirmation

#### GET /users
- **Description**: List all users (Admin only)
- **Authentication**: JWT required (Admin role)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 50, max: 100)
  - `role`: Filter by role (ADMIN, CUSTOMER)
  - `active`: Filter by active status (true/false)
  - `emailVerified`: Filter by email verification status
  - `search`: Search in email, first name, last name
  - `sortBy`: Sort field (created_at, updated_at, email, last_login_at)
  - `sortOrder`: Sort order (asc, desc)
- **Response**: Paginated list of users

#### GET /users/:id
- **Description**: Get specific user by ID (Admin only)
- **Authentication**: JWT required (Admin role)
- **Response**: User details

#### POST /users
- **Description**: Create new user (Admin only)
- **Authentication**: JWT required (Admin role)
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "securePassword123",
    "firstName": "John",
    "lastName": "Doe",
    "role": "CUSTOMER"
  }
  ```
- **Response**: Created user details

---

### 4. API Key Management

#### GET /my-api-keys
- **Description**: List user's API keys
- **Authentication**: JWT required
- **Response**: List of user's API keys with usage statistics

#### POST /my-api-keys
- **Description**: Create new API key
- **Authentication**: JWT required
- **Request Body**:
  ```json
  {
    "name": "My API Key",
    "description": "For my application",
    "allowedIps": ["***********"],
    "allowedDomains": ["example.com"],
    "expiresAt": "2024-12-31T23:59:59Z"
  }
  ```
- **Response**: Created API key details (includes the actual key)

#### GET /my-api-keys/:id
- **Description**: Get specific API key details
- **Authentication**: JWT required
- **Response**: API key details

---

### 5. Terminal Endpoints

#### GET /terminals
- **Description**: List postal terminals with filtering
- **Authentication**: API Key required
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 50)
  - `sort`: Sort field and order (e.g., "name:asc")
  - `city`: Filter by city
  - `provider`: Filter by provider (LP_EXPRESS, OMNIVA, DPD, VENIPAK)
  - `terminalType`: Filter by terminal type
  - `active`: Filter by active status (true/false)
- **Response**: Paginated list of terminals
- **Cache**: 5 minutes

#### GET /terminals/:id
- **Description**: Get specific terminal by ID
- **Authentication**: API Key required
- **Response**: Terminal details
- **Cache**: 5 minutes

#### GET /terminals/nearby
- **Description**: Find terminals near coordinates
- **Authentication**: API Key required
- **Query Parameters**:
  - `lat`: Latitude (required)
  - `lng`: Longitude (required)
  - `radius`: Search radius in meters (default: 5000)
  - `limit`: Maximum results (default: 20)
- **Response**: List of nearby terminals with distances
- **Cache**: 2 minutes

#### GET /terminals/search
- **Description**: Search terminals by text
- **Authentication**: API Key required
- **Query Parameters**:
  - `q`: Search query (required)
  - `limit`: Maximum results (default: 20)
- **Response**: List of matching terminals
- **Cache**: 2 minutes

---

### 6. Package Tracking

#### GET /track
- **Description**: Track package by provider and tracking number
- **Authentication**: API Key required
- **Query Parameters**:
  - `provider`: Shipping provider (LP_EXPRESS, OMNIVA, DPD, VENIPAK)
  - `trackingNumber`: Package tracking number
  - `refresh`: Force refresh from provider (optional)
- **Response**: Package tracking information

---

### 7. Subscription Management

#### GET /subscriptions/plans
- **Description**: List available subscription plans
- **Authentication**: JWT required
- **Query Parameters**:
  - `includePrivate`: Include private plans (default: false)
- **Response**: List of subscription plans

#### GET /subscriptions/plans/:id
- **Description**: Get specific subscription plan
- **Authentication**: JWT required
- **Response**: Subscription plan details

#### POST /subscriptions/plans
- **Description**: Create subscription plan (Admin only)
- **Authentication**: JWT required (Admin role)
- **Request Body**: Subscription plan data
- **Response**: Created plan details

#### PUT /subscriptions/plans/:id
- **Description**: Update subscription plan (Admin only)
- **Authentication**: JWT required (Admin role)
- **Request Body**: Updated plan data
- **Response**: Updated plan details

#### DELETE /subscriptions/plans/:id
- **Description**: Delete subscription plan (Admin only)
- **Authentication**: JWT required (Admin role)
- **Response**: Success confirmation

---

### 8. Analytics (User)

#### GET /analytics/dashboard
- **Description**: Get user's analytics dashboard data
- **Authentication**: JWT required
- **Response**: Comprehensive dashboard statistics

#### GET /analytics/usage-stats
- **Description**: Get detailed usage statistics
- **Authentication**: JWT required
- **Query Parameters**:
  - `startDate`: Start date (ISO format)
  - `endDate`: End date (ISO format)
  - `apiKeyId`: Filter by API key
  - `endpoint`: Filter by endpoint
- **Response**: Usage statistics

#### GET /analytics/time-series
- **Description**: Get time series usage data
- **Authentication**: JWT required
- **Query Parameters**:
  - `startDate`: Start date (ISO format)
  - `endDate`: End date (ISO format)
  - `apiKeyId`: Filter by API key
  - `interval`: Time interval (hour, day, week)
- **Response**: Time series data

#### GET /analytics/top-endpoints
- **Description**: Get most used endpoints
- **Authentication**: JWT required
- **Query Parameters**:
  - `startDate`: Start date (ISO format)
  - `endDate`: End date (ISO format)
  - `limit`: Number of results (default: 10)
- **Response**: List of top endpoints by usage

#### GET /analytics/quotas
- **Description**: Get usage quotas and limits
- **Authentication**: JWT required
- **Response**: Current usage vs. quotas

#### GET /analytics/alerts
- **Description**: Get usage alerts
- **Authentication**: JWT required
- **Query Parameters**:
  - `includeResolved`: Include resolved alerts (default: false)
- **Response**: List of usage alerts

---

### 9. Webhooks

#### POST /webhooks/stripe
- **Description**: Handle Stripe webhook events
- **Authentication**: Stripe signature verification
- **Headers**: `stripe-signature` required
- **Request Body**: Stripe webhook payload
- **Response**: Success confirmation

#### GET /webhooks/stripe/test
- **Description**: Test webhook endpoint accessibility
- **Authentication**: None
- **Response**: Accessibility confirmation

---

### 10. Admin Endpoints

All admin endpoints require JWT authentication with ADMIN role.

#### Admin Analytics

##### GET /admin/analytics/system-overview
- **Description**: Get system-wide analytics overview
- **Response**: Comprehensive system statistics

##### GET /admin/analytics/users
- **Description**: Get analytics for all users
- **Query Parameters**: Pagination and filtering options
- **Response**: User analytics data

##### GET /admin/analytics/api-keys
- **Description**: Get analytics for all API keys
- **Query Parameters**: Pagination and filtering options
- **Response**: API key analytics data

##### GET /admin/analytics/system-metrics
- **Description**: Get detailed system performance metrics
- **Query Parameters**:
  - `period`: Time period (hour, day, week, month)
  - `limit`: Number of results (default: 30)
- **Response**: System performance metrics

##### GET /admin/analytics/alerts
- **Description**: Get all system alerts
- **Query Parameters**:
  - `includeResolved`: Include resolved alerts
  - `severity`: Filter by severity level
- **Response**: System alerts

#### Admin User Management

##### GET /admin/users
- **Description**: Advanced user listing with filtering
- **Query Parameters**: Extensive filtering and sorting options
- **Response**: Detailed user list with statistics

##### GET /admin/users/:id
- **Description**: Get comprehensive user details
- **Response**: User details with subscriptions, API keys, and usage stats

##### PATCH /admin/users/:id
- **Description**: Update user profile and settings
- **Request Body**: User update data
- **Response**: Updated user details

##### POST /admin/users/:id/suspend
- **Description**: Suspend user account
- **Request Body**:
  ```json
  {
    "reason": "Violation of terms",
    "notes": "Additional notes"
  }
  ```
- **Response**: Success confirmation

#### Admin Subscription Management

##### GET /admin/subscriptions
- **Description**: List all subscriptions with advanced filtering
- **Query Parameters**: Comprehensive filtering options
- **Response**: Subscription list with revenue statistics

##### GET /admin/subscriptions/:id
- **Description**: Get detailed subscription information
- **Response**: Comprehensive subscription details

##### PATCH /admin/subscriptions/:id/status
- **Description**: Manually change subscription status
- **Request Body**:
  ```json
  {
    "status": "active",
    "reason": "Manual adjustment",
    "notes": "Additional notes"
  }
  ```
- **Response**: Updated subscription

##### POST /admin/subscriptions/:id/extend
- **Description**: Extend subscription period
- **Request Body**:
  ```json
  {
    "days": 30,
    "reason": "Customer service gesture",
    "notes": "Additional notes"
  }
  ```
- **Response**: Updated subscription

#### Admin System Management

##### GET /admin/system/health
- **Description**: Detailed system health information
- **Response**: Comprehensive system health data including database, PostGIS, and memory metrics

##### GET /admin/system/metrics
- **Description**: Comprehensive system metrics
- **Response**: Database statistics, table sizes, connections, and system performance data

##### GET /admin/system/settings
- **Description**: Get system settings
- **Response**: System configuration settings

---

## Error Responses

All endpoints return errors in a consistent format:

```json
{
  "success": false,
  "error": "Error message",
  "message": "Detailed error description",
  "code": "ERROR_CODE" // Optional
}
```

### Common HTTP Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (missing or invalid authentication)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `429`: Too Many Requests (rate limiting)
- `500`: Internal Server Error

## Rate Limiting

API endpoints are rate-limited based on the user's subscription plan:

- **Free Plan**: 1,000 requests/month
- **Basic Plan**: 10,000 requests/month
- **Pro Plan**: 100,000 requests/month
- **Enterprise Plan**: Custom limits

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Total requests allowed
- `X-RateLimit-Remaining`: Requests remaining
- `X-RateLimit-Reset`: Reset timestamp

## Caching

Some endpoints include caching headers:
- `Cache-Control`: Caching directives
- `ETag`: Entity tag for cache validation

Terminal and tracking endpoints are cached for performance:
- Terminal listings: 5 minutes
- Terminal details: 5 minutes
- Nearby terminals: 2 minutes
- Terminal search: 2 minutes