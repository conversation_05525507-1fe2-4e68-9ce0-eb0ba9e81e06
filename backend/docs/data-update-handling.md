# Data Update Handling

This document explains how the postal terminal API handles data updates, including address changes, new terminals, and removed terminals across all providers.

## Overview

The system performs full synchronization on each run, replacing all data from each provider. However, the handling differs based on whether providers include coordinates or require geocoding.

## Update Mechanisms by Provider

### 1. LP Express, Omniva, Venipak (Direct Coordinates)

These providers include coordinates in their data sources, making updates straightforward.

**Data Flow:**
```
Provider API/File → Download → Parse → Validate → Database Upsert
```

**Update Behavior:**
- **New Terminals**: Immediately available with correct coordinates
- **Address Changes**: New address and coordinates applied instantly  
- **Removed Terminals**: Deleted from database during sync
- **Coordinate Changes**: Updated coordinates applied immediately
- **Performance**: Very fast (~120-300ms per provider)

**Database Operation:**
```sql
INSERT INTO terminals (...) VALUES (...)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  city = EXCLUDED.city,
  address = EXCLUDED.address,
  coordinates = EXCLUDED.coordinates,
  updated = NOW()
```

### 2. DPD (Geocoded Coordinates)

DPD requires geocoding as their data doesn't include coordinates. This uses a sophisticated caching system.

**Data Flow:**
```
DPD Excel → Download → Parse → Cache Lookup → Geocode (if needed) → Database Upsert
```

**Update Behavior:**

#### New Terminals
✅ **Works Automatically**
- No cache entry exists
- Automatically geocoded during preprocessing
- Correct coordinates applied

#### Address Changes  
✅ **Detects and Handles Changes** (Enhanced)
- Compares cached address with current address
- Automatically re-geocodes when address/city changes
- Updates cache with new coordinates
- Logs address changes for visibility

```
📍 Address changed for LT90001: "Akmenės g. 8, Akmenė" → "Tilžės g. 15, Akmenė"
```

#### Removed Terminals
✅ **Works Automatically**
- Database is wiped and re-imported each sync
- Removed terminals disappear from database
- Cache entries remain but don't affect functionality

#### Coordinate Accuracy Changes
✅ **Handles Geocoding Improvements**
- Failed geocoding attempts (`geocoded: false`) are retried
- Geocoding service improvements benefit all terminals
- Manual cache clearing forces re-geocoding

## Detailed Update Scenarios

### Scenario 1: Regular Address Change (LP Express)
**Example**: Terminal moves to new location

**Before:**
```json
{
  "id": "lp_12345",
  "name": "Vilnius Akropolis",
  "address": "Ozo g. 25",
  "coordinates": { "lat": 54.7297, "lng": 25.2681 }
}
```

**After Data Update:**
```json
{
  "id": "lp_12345", 
  "name": "Vilnius Akropolis",
  "address": "Ozo g. 27", 
  "coordinates": { "lat": 54.7299, "lng": 25.2685 }
}
```

**Result**: Next sync automatically updates address and coordinates. API immediately returns new data.

### Scenario 2: DPD Address Change (Enhanced Detection)
**Example**: DPD terminal relocates within same city

**Cached Data:**
```json
{
  "LT90001": {
    "lat": 56.2431,
    "lng": 22.7575,
    "geocoded": true,
    "address": "Akmenės g. 8",
    "city": "Akmenė",
    "cachedAt": "2025-06-20T10:00:00.000Z"
  }
}
```

**New Excel Data:**
```
ID: LT90001
Adresas: Tilžės g. 15
Miestas: Akmenė
```

**Processing:**
1. System detects address change: `"Akmenės g. 8" → "Tilžės g. 15"`
2. Logs the change with clear message
3. Adds terminal to geocoding queue
4. Geocodes new address during preprocessing
5. Updates cache with new coordinates
6. Database gets updated coordinates

### Scenario 3: New Provider Terminal
**Example**: New Venipak pickup point added

**Processing:**
1. New terminal appears in API response
2. Parsed and validated during sync
3. Inserted into database with full details
4. Immediately available via API

### Scenario 4: Terminal Removal
**Example**: Omniva terminal permanently closed

**Processing:**
1. Terminal no longer appears in provider data
2. Database sync removes terminal (full replace operation)
3. Terminal becomes unavailable via API
4. Cache entries (DPD only) remain but don't affect functionality

## Manual Address Change Detection

### For DPD Terminals (Automatic)
The system automatically detects address changes:

```typescript
// Enhanced change detection
const addressChanged = cached.address !== currentAddress || 
                      cached.city !== currentCity;

if (addressChanged) {
  console.log(`📍 Address changed for ${terminalId}: ` +
             `"${cached.address}, ${cached.city}" → ` + 
             `"${currentAddress}, ${currentCity}"`);
  return true; // Trigger re-geocoding
}
```

### For Other Providers (Immediate)
Address changes are handled immediately since coordinates come from provider APIs.

## Troubleshooting Updates

### Issue: DPD Terminal Has Wrong Coordinates
**Symptom**: Terminal shows old location after address change

**Diagnosis:**
```bash
# Check if address change was detected
npm run preprocess-dpd
# Look for "Address changed" messages
```

**Solution:**
1. Run preprocessing to detect and fix address changes:
   ```bash
   npm run preprocess-dpd
   ```

2. Or manually clear cache entry and re-geocode:
   ```bash
   # Edit data/dpd-geocoding-cache.json
   # Remove the terminal's cache entry
   npm run preprocess-dpd
   ```

### Issue: New Terminal Not Appearing
**Symptom**: Known new terminal doesn't appear in API

**Diagnosis:**
```bash
# Check sync logs for validation errors
npm run sync-data -- --provider [PROVIDER_NAME]
```

**Solution:**
1. Verify provider data includes required fields
2. Check validation logs for specific errors
3. Ensure coordinates are within valid ranges

### Issue: Geocoding Cache Becomes Stale
**Symptom**: Multiple DPD terminals have outdated coordinates

**Solution:**
```bash
# Force complete re-geocoding
rm data/dpd-geocoding-cache.json
npm run preprocess-dpd
```

## Production DPD Geocoding with 30-Day Refresh Cycle

### 🏭 **Automated Production Flow**

When `FILE_CACHE_DURATION_DAYS=30` is configured, the system automatically handles DPD geocoding refresh in production without manual intervention:

#### **Daily Sync Process (2 AM) - Smart Sync Enabled**
```typescript
// Production automatic sync via cron: '0 2 * * *'
DataSynchronizationService.syncAllProviders()
```

1. **Smart File Check**: Checks if any provider files have changed (expired cache)
2. **Skip if No Changes**: If all files are cached and fresh, skips database operations entirely
3. **Process Only Changes**: If files have changed, downloads fresh data and syncs database
4. **Smart Preprocessing**: Automatically geocodes up to 10 critical terminals when needed
5. **Optimal Performance**: ~5ms when no changes, ~669ms when files need updating

#### **Smart Geocoding Strategy**

The system uses a **two-tier approach** optimized for production:

**Tier 1: Critical Updates (During Sync)**
- New terminals without coordinates
- Existing terminals with address changes  
- Limited to 10 terminals for sync performance
- Completion: ~8 seconds additional sync time

**Tier 2: Full Coverage (Background/Manual)**
- Remaining new terminals
- Comprehensive address validation
- Triggered manually when needed: `npm run preprocess-dpd`

#### **Production Logs Example**

**Regular daily sync (files cached, no changes needed):**
```bash
[2025-01-15 02:00:15] 🔄 Starting data synchronization for all providers...
[2025-01-15 02:00:15] 📁 File cached: lp-express-terminals.csv (5.2 days old) - up to date
[2025-01-15 02:00:15] 📁 File cached: omniva-terminals.json (5.2 days old) - up to date
[2025-01-15 02:00:15] 📁 File cached: dpd-terminals.xlsx (5.2 days old) - up to date
[2025-01-15 02:00:15] 📁 File cached: venipak-terminals.json (5.2 days old) - up to date
[2025-01-15 02:00:15] 📁 All files are cached and fresh - skipping sync to avoid unnecessary database operations
[2025-01-15 02:00:15] ⚡ Smart sync completed in 5ms (no changes detected)
```

**When 30-day refresh triggers (files expired):**
```bash
[2025-01-15 02:00:15] 🔄 Starting data synchronization for all providers...
[2025-01-15 02:00:15] 📄 File expired: dpd-terminals.xlsx (30.1 days old) - sync needed
[2025-01-15 02:00:15] 📥 File changes detected - proceeding with full sync...
[2025-01-15 02:00:16] 🔄 Syncing DPD terminals...
[2025-01-15 02:00:16] 📁 Fresh file downloaded - checking geocoding cache freshness...
[2025-01-15 02:00:16] 🧠 Running smart geocoding preprocessing...
[2025-01-15 02:00:17] 🌍 Smart geocoding 3 terminals during sync...
[2025-01-15 02:00:17] 📍 Address changed for LT00123: "Vilniaus g. 5, Vilnius" → "Vilniaus g. 7, Vilnius"
[2025-01-15 02:00:20] [1/3] Geocoding Maxima (LT00123)
[2025-01-15 02:00:22] [2/3] Geocoding Iki (LT00456)
[2025-01-15 02:00:24] [3/3] Geocoding Rimi (LT00789)
[2025-01-15 02:00:25] 🎯 Smart geocoding completed: 3 success, 0 failed
[2025-01-15 02:00:25] ✅ DPD: 400 terminals synced successfully
```

### 🔄 **30-Day Refresh Behavior**

#### **File Cache vs Geocoding Cache**

| Component | Duration | Behavior |
|-----------|----------|----------|
| **DPD Excel File** | 30 days | Auto-refreshed when expired |
| **Geocoding Cache** | Indefinite | Smart updates on address changes |

#### **Automatic Refresh Triggers**

1. **File Age Check**: Every sync checks if DPD file is >30 days old
2. **Fresh Download**: Downloads new Excel file from DPD servers
3. **Address Comparison**: Compares new addresses with cached ones
4. **Smart Geocoding**: Geocodes only changed/new terminals during sync

#### **Production Environment Variables**

```bash
# .env for production
FILE_CACHE_DURATION_DAYS=30          # Auto-refresh every 30 days
DATA_SYNC_SCHEDULE="0 2 * * *"       # Daily sync at 2 AM
AUTO_UPDATE_ENABLED=true             # Enable automatic sync
RUN_INITIAL_SYNC=true                # Sync on startup
```

### 🎯 **Handling Large Address Changes**

If DPD makes significant updates (>10 changed addresses), the system handles it gracefully:

#### **During Sync (Fast Mode)**
```bash
[2025-01-15 02:00:17] 🌍 Smart geocoding 23 terminals during sync...
[2025-01-15 02:00:17] ⚡ Limiting to 10 terminals during sync (13 remaining for manual preprocessing)
[2025-01-15 02:00:25] 🎯 Smart geocoding completed: 10 success, 0 failed
[2025-01-15 02:00:25] 💡 Run 'npm run preprocess-dpd' to geocode remaining 13 terminals
```

#### **Background Processing (Complete Coverage)**
```bash
# Admin can run complete geocoding when convenient
npm run preprocess-dpd

# Or schedule it separately in production
0 3 * * 0  cd /app && npm run preprocess-dpd  # Weekly at 3 AM Sunday
```

### 🚨 **Production Monitoring**

#### **Notification Alerts**
The system sends notifications for:
- Failed DPD file downloads
- Geocoding failures during sync
- Large number of address changes detected

#### **Performance Monitoring** 
```bash
# Sync duration includes smart geocoding
DPD: 8.2s (120ms file + 8s smart geocoding)

# vs normal sync without fresh data
DPD: 120ms (cached file, no geocoding needed)
```

#### **Health Check Considerations**
- Sync duration may increase on 30-day refresh cycles
- API endpoints remain responsive during sync
- Database updates are atomic per provider

### 🛠 **Production Operations**

#### **Deployment Process**
1. **New Deployment**: System automatically runs initial sync
2. **30-Day Refresh**: Handled transparently during regular operations  
3. **Manual Override**: Admin can force refresh if needed

#### **Monitoring Commands**
```bash
# Check cache status
curl -H "x-api-key: $API_KEY" http://localhost:3000/health

# Monitor sync status
pm2 logs postal-terminal-api

# Verify geocoding coverage
ls -la data/dpd-geocoding-cache.json
```

#### **Manual Intervention (Rare)**
```bash
# Force complete DPD refresh
rm data/downloads/dpd-terminals.xlsx
rm data/dpd-geocoding-cache.json
npm run sync-data -- --provider DPD

# This triggers fresh download + full preprocessing
```

### 💡 **Benefits of This Approach**

1. **Zero Downtime**: Refreshes happen during regular maintenance windows
2. **Performance Optimized**: Only geocodes what's necessary during sync
3. **Automatic Recovery**: Handles provider data changes gracefully  
4. **Scalable**: Works for any number of address changes
5. **Monitored**: Full logging and notification coverage

## Performance Considerations

### Fast Sync Mode (Default)
```bash
npm run sync-data
# Uses cached coordinates for DPD
# Duration: ~669ms for all providers
```

### Full Re-geocoding (When Needed)
```bash
npm run preprocess-dpd
# Re-geocodes changed/new addresses
# Duration: ~8 minutes for 400 terminals
```

### Optimal Update Workflow
1. **Regular syncs**: Use fast sync mode for daily operations
2. **Address changes**: Automatically detected and handled during preprocessing
3. **Monthly maintenance**: Optional full re-geocoding to catch any missed changes

## Monitoring Updates

### Sync Logs Show:
- Number of terminals per provider
- Validation results (valid vs invalid)
- Duration for each provider
- Address change detection (DPD)

### Cache Logs Show:
- Geocoding coverage statistics
- New vs changed terminals
- Success/failure rates for geocoding
- Address change notifications

## Best Practices

1. **Monitor sync logs** for validation errors or performance issues
2. **Review address change logs** to verify DPD updates are detected
3. **Run preprocessing periodically** to ensure geocoding cache accuracy
4. **Test API responses** after major provider updates
5. **Monitor cache file size** to detect growth patterns

## Smart Sync Optimization

The system now includes smart sync capabilities that automatically detect when cached files are still fresh, dramatically reducing unnecessary processing time and database operations.

## Smart Sync Benefits

- **99.25% efficiency improvement**: Most sync runs complete in ~5ms when no changes are detected
- **Automatic detection**: Checks file age against `FILE_CACHE_DURATION_DAYS` setting
- **Resource conservation**: Avoids unnecessary downloads and database operations
- **Production optimized**: Designed for frequent scheduled syncs

## Overriding Smart Sync (Manual Operations)

When running data sync manually, you may want to force a full refresh regardless of cache status. Here are all available methods:

### Method 1: Disable Smart Sync (Recommended)
```bash
# Disable smart sync optimization completely
ENABLE_SMART_SYNC=false npm run sync-data
```

### Method 2: Force Download Only
```bash
# Force fresh downloads but keep smart sync logic
FORCE_DOWNLOAD_ON_SYNC=true npm run sync-data
```

### Method 3: Complete Override (Most Reliable)
```bash
# Disable both smart sync and force fresh downloads
ENABLE_SMART_SYNC=false FORCE_DOWNLOAD_ON_SYNC=true npm run sync-data
```

### Method 4: Convenience Script (NEW!)
```bash
# Use the built-in force sync script
npm run sync-data:force
```

This convenience script is equivalent to Method 3 and is the **recommended approach** for manual force syncing.

## Configuration Variables

// ... existing code ...

---

**Last Updated**: November 2024  
**System Version**: 1.0.0 