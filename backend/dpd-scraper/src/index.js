import Fastify from 'fastify';
import puppeteer from 'puppeteer-extra';
import Stealth from 'puppeteer-extra-plugin-stealth';
import RecaptchaPlugin from 'puppeteer-extra-plugin-recaptcha';

puppeteer.use(Stealth());
puppeteer.use(
  RecaptchaPlugin({
    provider: {
      id: '2captcha',            // or 'capsolver', 'anticaptcha', …
      token: process.env.CAPTCHA_KEY   // set env var in Docker / PM2
    },
    visualFeedback: false
  })
);

const app = Fastify({ logger: true });
const PORT = process.env.PORT || 4000;

// Helper: visit root first so <PERSON>f<PERSON>e can set cf_clearance, then go to real page
async function openWithCloudflareBypass(page, finalUrl) {
  // 1) Visit LT portal root (avoids corporate home redirect to dpd.com)
  await page.goto('https://www.dpdgroup.com/lt/mydpd', {
    waitUntil: 'domcontentloaded',
    timeout: 60000
  });

  // 2) Wait up to 20 s for the clearance cookie that indicates we are good
  try {
    await page.waitForFunction(
      () => document.cookie.includes('cf_clearance'),
      { timeout: 20000 }
    );
  } catch (_) {
    // If the cookie never appears it's fine – maybe CF didn't challenge this IP
  }

  // 3) Navigate to the actual tracking page, wait for network to be mostly idle
  await page.goto(finalUrl, { waitUntil: 'networkidle2', timeout: 60000 });
}

app.get('/track', async (request, reply) => {
  const number = request.query.number;
  if (!number) return reply.code(400).send({ error: 'number required' });

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false,
      slowMo: 50,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();
    page.setDefaultTimeout(60000);
    await page.setUserAgent(
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120 Safari/537.36'
    );

    // 1) Open LT MyDPD root to pass Cloudflare
    await page.goto('https://www.dpdgroup.com/lt/mydpd', {
      waitUntil: 'domcontentloaded'
    });

    // Wait up to 60 s for Cloudflare to set cf_clearance cookie
    await page.waitForFunction(
      () => document.cookie.includes('cf_clearance'),
      { polling: 500, timeout: 60000 }
    ).catch(() => {});

    // 2) Navigate to incoming parcels page
    await page.goto('https://www.dpdgroup.com/lt/mydpd/my-parcels/incoming', {
      waitUntil: 'domcontentloaded'
    });

    // Accept cookies banner if present
    try {
      await page.click('#onetrust-accept-btn-handler', { timeout: 3000 });
    } catch {}

    const INPUT_SELECTOR = '#searchParcel, input[name="value"]';
    await page.waitForSelector(INPUT_SELECTOR, { timeout: 90000 });

    // Fill the input via JS and dispatch input event
    await page.$eval(
      INPUT_SELECTOR,
      (el, val) => {
        el.value = val;
        el.dispatchEvent(new Event('input', { bubbles: true }));
      },
      number
    );

    // Submit the form programmatically (no click)
    await page.$eval('form.search-box', (form) => form.submit());

    // Wait for navigation / ajax render
    await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 90000 }).catch(() => {});

    // 5) Wait for timeline
    await page.waitForSelector('.parcelStatus', { timeout: 60000 });

    const events = await page.$$eval('.parcelStatus .row', rows =>
      rows.map(row => ({
        status: row.querySelector('.col-xs-7 span')?.textContent.trim(),
        timestamp: row.querySelector('.col-xs-5 .inlineDate')?.textContent.trim()
      }))
    );

    const latest = events.at(-1) || {};

    reply.send({
      provider: 'DPD',
      trackingNumber: number,
      status: latest.status || 'UNKNOWN',
      events,
      lastUpdated: latest.timestamp || new Date().toISOString()
    });
  } catch (e) {
    console.error(e);
    reply.code(503).send({ error: 'scrape_failed', message: e.message });
  } finally {
    if (browser) await browser.close();
  }
});

app.get('/health', async () => ({ status: 'ok', timestamp: new Date().toISOString() }));

app.listen({ port: PORT, host: '0.0.0.0' }).then(() => {
  console.log(`DPD scraper listening on :${PORT}`);
}); 