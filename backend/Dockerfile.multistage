# Multi-stage build for better optimization and fewer warnings
FROM node:22.13-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

# Install system dependencies for building (some packages need native compilation)
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies for build)
# Skip postinstall scripts to avoid build errors before source code is copied
RUN npm ci --ignore-scripts --silent

# Copy source code and remove the DPD scraper micro-service (built separately)
COPY . .
RUN rm -rf ./dpd-scraper

# Build the application
RUN npm run build

# Production stage
FROM node:22.13-alpine AS production

# Set the working directory inside the container
WORKDIR /app

# Install system dependencies for runtime
RUN apk add --no-cache \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production --silent && \
    npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Create data directory
RUN mkdir -p /app/data/downloads

# Create a non-root user to run the application
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Set ownership of app directory
RUN chown -R nodejs:nodejs /app

# Switch to the non-root user
USER nodejs

# Expose the port the app runs on
EXPOSE 3000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/v1/health || exit 1

# Start the application
CMD ["npm", "start"]
