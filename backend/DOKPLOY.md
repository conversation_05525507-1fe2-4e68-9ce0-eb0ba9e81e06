# Dokploy Deployment Guide

This guide shows how to deploy the Postal Terminal API using Dokploy with your existing database.

## Prerequisites

- Dokploy installed and running
- Existing PostgreSQL database with PostGIS extension
- Database already set up with migrations run
- Git repository accessible by <PERSON>kploy

## How Dokploy Handles Environment Variables

Dokploy automatically injects environment variables set in the Environment tab into your container. This means:

✅ **Set variables in Dokploy's Environment tab** - Clean and secure  
✅ **No need to specify in docker-compose.yml** - Avoids duplication  
✅ **Easy to update** - Change variables without redeploying  
✅ **Secure** - Variables are encrypted and managed by Dokploy  

### Environment Variable Priority
1. **Dokploy Environment Tab** - Highest priority
2. Docker Compose environment section - Not used in our setup
3. .env files - Not used in container deployment

## Deployment Steps

### 1. Create New Application in Dokploy

1. **Login to Dokploy Dashboard**
2. **Click "Create Application"**
3. **Choose "Docker Compose" deployment type**
4. **Set Application Name**: `postal-terminal-api`

### 2. Configure Source

1. **Repository Settings**:
   - Repository URL: `https://github.com/your-username/postal-terminal-api`
   - Branch: `main` (or your preferred branch)
   - Build Path: `/` (root of repository)

2. **Docker Compose File**: 
   - Select `docker-compose.dokploy.yml`

### 3. Environment Variables

**Important**: All environment variables are managed in Dokploy's Environment tab. The Docker Compose file does NOT contain environment variables to avoid conflicts.

In Dokploy's Environment section, add these variables:

```env
# Required - Database Connection
DATABASE_URL=************************************************/postal_terminals

# Required - Security
API_KEY_SECRET=your-super-secure-64-character-minimum-secret-key-here

# Application Settings
NODE_ENV=production
PORT=3000
APP_VERSION=1.0.0
LOG_LEVEL=info

# Database Pool Settings
DB_MAX_CONNECTIONS=20
DB_QUERY_TIMEOUT=30000
DB_STATEMENT_TIMEOUT=60000

# Performance Settings
CACHE_SIZE=100000
MAX_CONCURRENT_QUERIES=100
QUERY_COMPLEXITY_LIMIT=1000

# Rate Limiting
DEFAULT_RATE_LIMIT=1000
BURST_LIMIT=2000
RATE_WINDOW_SIZE=60

# Data Synchronization (Optional)
AUTO_UPDATE_ENABLED=true
RUN_INITIAL_SYNC=false
UPDATE_FREQUENCY=weekly
DATA_SYNC_SCHEDULE="0 2 * * 1"
ENABLE_SMART_SYNC=true
```

### 4. Port Configuration

- **Container Port**: `3000`
- **Host Port**: `3000` (or your preferred port)
- **Protocol**: `HTTP`

### 5. Domain Configuration (Optional)

1. **Add Custom Domain** (if you have one):
   - Domain: `api.yourdomain.com`
   - Enable HTTPS: `Yes`
   - Auto SSL: `Yes` (if using Let's Encrypt)

### 6. Resource Limits

Set appropriate resource limits:
- **Memory Limit**: `1GB`
- **CPU Limit**: `1.0`
- **Memory Reservation**: `512MB`
- **CPU Reservation**: `0.5`

### 7. Health Check Configuration

Dokploy will automatically use the health check defined in the Docker Compose file:
- **Health Check URL**: `/api/v1/health`
- **Interval**: `30s`
- **Timeout**: `10s`
- **Retries**: `3`

## Database Connection Options

### Option 1: External Database Server
```env
DATABASE_URL=******************************************************/postal_terminals
```

### Option 2: Database on Same Server (Different Container)
```env
DATABASE_URL=******************************************************/postal_terminals
```

### Option 3: Database on Host Machine
```env
DATABASE_URL=postgresql://username:<EMAIL>:5432/postal_terminals
```

## Deployment Process

### 1. Initial Deployment

1. **Click "Deploy"** in Dokploy
2. **Monitor Build Logs** for any issues
3. **Wait for Container to Start** (check health status)
4. **Test API Endpoint**: `http://your-server:3000/api/v1/health`

### 2. Post-Deployment Tasks

Since your database is already set up, you might want to:

```bash
# Connect to running container (via Dokploy terminal)
docker exec -it postal-terminal-api npm run sync-data

# Or create additional API keys if needed
docker exec -it postal-terminal-api npm run create-api-key
```

## Monitoring and Logs

### Dokploy Dashboard
- **Container Status**: Real-time health monitoring
- **Resource Usage**: CPU, Memory, Network graphs
- **Application Logs**: Streaming logs with filtering

### Health Check Endpoints
- **API Health**: `http://your-server:3000/api/v1/health`
- **Detailed Status**: `http://your-server:3000/api/v1/terminals/stats`

## Scaling

### Horizontal Scaling
In Dokploy, you can scale horizontally:
1. Go to application settings
2. Increase **Replicas** count
3. Dokploy will handle load balancing automatically

### Vertical Scaling
Adjust resource limits:
1. Update **Memory Limit** and **CPU Limit**
2. Redeploy the application

## Backup and Recovery

### Application Data
The application data volume is automatically managed by Dokploy:
- **Volume Name**: `postal_data`
- **Backup**: Use Dokploy's backup features or Docker volume backup

### Database Backup
Since your database is external, backup according to your database setup:
```bash
# Example PostgreSQL backup
pg_dump -h your-db-host -U username postal_terminals > backup.sql
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check `DATABASE_URL` format
   - Verify database server is accessible from Dokploy
   - Test connection: `docker exec -it container-name npm run migrate:status`

2. **Application Won't Start**
   - Check environment variables are set correctly
   - Review application logs in Dokploy
   - Verify port 3000 is not conflicting

3. **Health Check Failing**
   - Check if application is listening on port 3000
   - Verify `/api/v1/health` endpoint responds
   - Review application logs for errors

### Debug Mode

Enable debug logging:
```env
LOG_LEVEL=debug
```

Then redeploy and check logs in Dokploy dashboard.

## Updates and Maintenance

### Code Updates
1. **Push changes** to your Git repository
2. **Trigger redeploy** in Dokploy (auto-deploy if configured)
3. **Monitor deployment** in Dokploy dashboard

### Environment Updates
1. **Update environment variables** in Dokploy
2. **Restart application** to apply changes

### Zero-Downtime Deployments
Dokploy supports rolling deployments:
1. **Scale to 2+ replicas**
2. **Deploy updates** - Dokploy updates containers one by one
3. **Monitor health checks** during deployment

## Security Considerations

1. **Database Access**: Ensure database is only accessible from your Dokploy server
2. **API Keys**: Use environment variables, never hardcode
3. **HTTPS**: Enable SSL/TLS for production domains
4. **Firewall**: Restrict access to necessary ports only
5. **Updates**: Keep Dokploy and base images updated

This setup gives you a production-ready deployment with proper monitoring, scaling, and maintenance capabilities while using your existing database infrastructure.
